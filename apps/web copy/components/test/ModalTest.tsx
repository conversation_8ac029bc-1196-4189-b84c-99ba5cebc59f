'use client'

import { useModal } from '@shared/hooks/useModal'
import { Button } from '@ui/components/button'

/**
 * 测试组件 - 验证模态框功能是否正常工作
 * 在页面中导入此组件来测试全局弹窗系统
 */
export function ModalTest() {
  const { showLoginModal, showInsufficientCreditsModal, showConfirmModal, showDeleteConfirm } = useModal()

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-bold">弹窗测试组件</h2>
      
      <div className="grid grid-cols-2 gap-4">
        <Button onClick={() => showLoginModal()}>
          测试登录弹窗
        </Button>

        <Button onClick={() => showLoginModal({
          title: '自定义登录标题',
          content: '这是自定义的登录内容'
        })}>
          自定义登录弹窗
        </Button>

        <Button onClick={() => showInsufficientCreditsModal()}>
          测试积分不足弹窗
        </Button>

        <Button onClick={() => showConfirmModal({
          title: '确认测试',
          content: '这是一个测试确认弹窗',
          onConfirm: () => alert('确认了！'),
          onCancel: () => alert('取消了！')
        })}>
          测试确认弹窗
        </Button>

        <Button 
          variant="destructive"
          onClick={() => showDeleteConfirm(
            () => alert('删除完成！'),
            '测试项目'
          )}
        >
          测试删除确认
        </Button>
      </div>
    </div>
  )
}