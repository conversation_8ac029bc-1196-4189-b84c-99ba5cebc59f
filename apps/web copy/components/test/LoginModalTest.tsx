'use client'

import { useModal } from '@shared/hooks/useModal'
import { Button } from '@ui/components/button'

/**
 * 测试新的紫色主题登录弹窗样式
 */
export function LoginModalTest() {
  const { showLoginModal } = useModal()

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 p-6 flex items-center justify-center">
      <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 max-w-md w-full">
        <h2 className="text-2xl font-bold text-white mb-6 text-center">ImgGen Brand Login Modal</h2>
        
        <div className="space-y-4">
          <Button 
            onClick={() => showLoginModal()}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
          >
            默认登录弹窗
          </Button>

          <Button 
            onClick={() => showLoginModal({
              title: 'AI Image Upscaler',
              content: '登录后解锁AI图片放大功能，提升图片质量到4K！',
              props: {
                needBottomArea: true
              }
            })}
            className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700"
          >
            AI图片放大登录
          </Button>

          <Button 
            onClick={() => showLoginModal({
              title: 'AI Image Generator',
              content: 'Create stunning AI-generated images with professional quality',
              props: {
                needBottomArea: true
              }
            })}
            className="w-full bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700"
          >
            AI图片生成登录
          </Button>

          <Button 
            onClick={() => showLoginModal({
              title: 'Premium Features',
              content: 'Unlock unlimited AI generation and advanced customization',
              props: {
                needBottomArea: false
              }
            })}
            className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
          >
            高级功能登录
          </Button>
        </div>

        <div className="mt-8 text-center">
          <p className="text-purple-200 text-sm">
            测试 ImgGen 品牌风格登录弹窗
          </p>
          <p className="text-purple-300 text-xs mt-2">
            左侧展示极光背景 + ImgGen 品牌标识
          </p>
        </div>
      </div>
    </div>
  )
}