import { routing } from '@i18n/routing'
import { PATH_THEME_PRIORITY_HIGHER } from '@marketing/stores'
import createMiddleware from 'next-intl/middleware'
import { NextRequest, NextResponse } from 'next/server'

const intlMiddleware = createMiddleware(routing)

// 主题相关常量
const THEME_COOKIE_NAME = 'theme'
const USER_THEME_OVERRIDE_COOKIE = 'user-theme-override'

// 判断是否为AI页面
function isAiPage(pathname: string): boolean {
  // 移除语言前缀后判断
  const pathWithoutLocale =
    pathname.replace(/^\/[a-z]{2}(-[A-Z]{2})?/, '') || '/'
  return pathname.startsWith('/ai/') || pathWithoutLocale.startsWith('/ai/')
}

// 获取基于路径的默认主题
function getDefaultThemeForPath(pathname: string): 'light' | 'dark' {
  return isAiPage(pathname) ? 'light' : 'dark'
}

// 主题解析函数：根据优先级配置决定最终主题
function resolveTheme(
  userOverride: string | undefined,
  pathname: string
): 'light' | 'dark' {
  const pathBasedTheme = getDefaultThemeForPath(pathname)

  if (PATH_THEME_PRIORITY_HIGHER) {
    // 路径优先级更高：AI页面强制light，其他页面强制dark
    return pathBasedTheme
  } else {
    // 用户覆盖优先级更高：用户设置 > 路径判断
    if (userOverride && (userOverride === 'light' || userOverride === 'dark')) {
      return userOverride as 'light' | 'dark'
    }
    return pathBasedTheme
  }
}

export default async function middleware(req: NextRequest) {
  // 先执行国际化中间件
  const response = intlMiddleware(req)

  // 如果国际化中间件返回重定向，直接返回
  if (response && response.status >= 300 && response.status < 400) {
    return response
  }

  // 创建新的响应对象（如果intlMiddleware没有返回响应）
  const nextResponse = response || NextResponse.next()

  // 获取当前路径
  const pathname = req.nextUrl.pathname

  // 获取用户的主题偏好
  const userThemeOverride = req.cookies.get(USER_THEME_OVERRIDE_COOKIE)?.value
  const currentThemeCookie = req.cookies.get(THEME_COOKIE_NAME)?.value as
    | 'light'
    | 'dark'
    | undefined

  // 使用主题解析函数确定最终主题
  const targetTheme = resolveTheme(userThemeOverride, pathname)

  // 如果当前主题cookie与目标主题不一致，更新cookie
  if (currentThemeCookie !== targetTheme) {
    nextResponse.cookies.set(THEME_COOKIE_NAME, targetTheme, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1年
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production',
    })
  }

  // 设置响应头，供客户端使用
  nextResponse.headers.set('x-theme', targetTheme)
  nextResponse.headers.set('x-is-ai-page', isAiPage(pathname).toString())
  nextResponse.headers.set(
    'x-has-user-override',
    (!!userThemeOverride).toString()
  )

  return nextResponse
}

export const config = {
  matcher: ['/((?!api|_next|.*\\..*).*)'],
}
