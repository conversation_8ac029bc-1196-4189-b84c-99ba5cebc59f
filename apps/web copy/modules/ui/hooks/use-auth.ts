'use client'

import { useState, useCallback, useMemo } from 'react'
import { useUser } from '../../saas/auth/hooks/use-user'
import { usePathname } from '@i18n/routing'
import { getUserFromClientCookies } from '@/utils/client-cookies'

interface UseAuthReturn {
  isLoggedIn: boolean
  user: any
  isLoginModalOpen: boolean
  login: () => void
  logout: () => Promise<void>
  openLoginModal: () => void
  closeLoginModal: () => void
}

export function useAuth(): UseAuthReturn {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false)
  // const { user, logout: logoutUser } = useUser()
  const pathname = usePathname()

  // 使用 useMemo 稳定用户对象，仅当cookie值变化时重新创建
  const user = useMemo(() => {
    const userData = getUserFromClientCookies()
    return userData
  }, [
    // 依赖具体的cookie值而不是整个函数调用
    typeof window !== 'undefined' ? document.cookie : '',
  ])

  const isLoggedIn = Boolean(user?.id)

  const login = useCallback(() => {
    // 保存当前路径用于登录后重定向
    localStorage.setItem('REDIRECT_PATH', pathname)
    // 跳转到 Google OAuth 登录
    window.location.href = '/api/oauth/google'
  }, [pathname])

  const logout = useCallback(async () => {
    // await logoutUser()
  }, [])

  const openLoginModal = useCallback(() => {
    setIsLoginModalOpen(true)
  }, [])

  const closeLoginModal = useCallback(() => {
    setIsLoginModalOpen(false)
  }, [])

  return {
    isLoggedIn,
    user,
    isLoginModalOpen,
    login,
    logout,
    openLoginModal,
    closeLoginModal,
  }
}
