'use client'

import { <PERSON> } from '@i18n/routing'
import { UndoIcon } from 'lucide-react'

export function NotFound() {
  return (
    <div className="flex h-screen flex-col items-center justify-center text-white">
      <h1 className="font-bold text-5xl">404</h1>
      <p className="mt-2 text-2xl">Page not found</p>

      <button className="mt-4 group relative inline-flex h-12 items-center justify-center overflow-hidden rounded-md   bg-gradient-to-r dark:from-[#070e41] dark:to-[#263381] from-[#f6f7ff] to-[#f5f6ff] dark:border-[rgb(76_100_255)] border-2 border-[#263381] bg-transparent px-6 font-medium dark:text-white text-black transition-all duration-100 dark:[box-shadow:5px_5px_rgb(76_100_255)] [box-shadow:5px_5px_rgb(38_51_129)] active:translate-x-[3px] active:translate-y-[3px] active:[box-shadow:0px_0px_rgb(38_51_129)] dark:active:[box-shadow:0px_0px_rgb(76_100_255)]">
        <Link href="/" className="flex gap-1 items-center">
          <UndoIcon className="mr-2 size-4" /> Go to homepage
        </Link>
      </button>
    </div>
  )
}
