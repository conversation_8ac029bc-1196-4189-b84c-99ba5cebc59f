'use client'
import { Link, usePathname } from '@i18n/routing'
import { Logo, getMenuCategories } from '@shared/components/Logo'
import { useTranslations } from 'next-intl'

const HotLabel = () => (
  <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-purple-600 to-pink-500 px-1.5 py-0.5 text-xs font-medium text-white ml-1 shadow-sm">
    Hot
  </span>
)

const NewLabel = () => (
  <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-violet-500 to-purple-600 px-1.5 py-0.5 text-xs font-medium text-white ml-1 shadow-sm">
    New
  </span>
)

const ComingLabel = () => (
  <span className="inline-flex items-center justify-center  px-1.5 py-0.5 text-xs font-medium rounded ml-1 whitespace-nowrap border border-orange-500/20">
    Coming
  </span>
)

export function Footer() {
  const t = useTranslations()
  const pathname = usePathname()
  const isAiPage = pathname.startsWith('/ai/')
  const menuCategories = getMenuCategories(isAiPage, t)

  return (
    <footer className="relative py-12 bg-gray-900/80 border-t border-gray-800 overflow-hidden">
      {/* 背景光束效果 - 直线光束 */}
      <div className="absolute inset-0 pointer-events-none">
        {/* 主要斜向光束 - 从左下到右上 */}
        <div className="absolute -bottom-10 -left-20 w-full h-6 bg-gradient-to-r from-purple-500/20 via-purple-500/30 via-blue-500/20 to-transparent transform rotate-12 blur-sm animate-pulse"></div>

        {/* 次要斜向光束 - 从右下到左上 */}
        <div
          className="absolute -bottom-5 -right-20 w-full h-4 bg-gradient-to-l from-pink-500/15 via-purple-500/25 to-transparent transform -rotate-12 blur-sm animate-pulse"
          style={{ animationDuration: '4s' }}
        ></div>

        {/* 顶部斜向光束 */}
        <div className="absolute -top-5 -left-10 w-full h-3 bg-gradient-to-r from-cyan-500/10 via-blue-500/20 to-transparent transform rotate-6 blur-sm"></div>

        {/* 中央穿透光束 */}
        <div
          className="absolute top-1/2 left-0 w-full h-8 bg-gradient-to-r from-transparent via-purple-500/15 via-blue-500/10 to-transparent transform -rotate-8 blur-md animate-pulse"
          style={{ animationDuration: '6s' }}
        ></div>

        {/* 细线光束组 */}
        <div className="absolute top-0 left-1/6 w-0.5 h-full bg-gradient-to-b from-transparent via-purple-500/25 to-transparent transform rotate-15 blur-[1px]"></div>
        <div className="absolute top-0 left-1/3 w-0.5 h-full bg-gradient-to-b from-transparent via-blue-500/20 to-transparent transform rotate-10 blur-[1px]"></div>
        <div className="absolute top-0 left-1/2 w-0.5 h-full bg-gradient-to-b from-transparent via-cyan-500/15 to-transparent transform rotate-5 blur-[1px]"></div>
        <div className="absolute top-0 left-2/3 w-0.5 h-full bg-gradient-to-b from-transparent via-pink-500/18 to-transparent transform -rotate-5 blur-[1px]"></div>
        <div className="absolute top-0 right-1/4 w-0.5 h-full bg-gradient-to-b from-transparent via-purple-500/20 to-transparent transform -rotate-10 blur-[1px]"></div>

        {/* 交叉宽光束 */}
        <div className="absolute top-1/4 left-0 w-full h-5 bg-gradient-to-r from-transparent via-indigo-500/12 to-transparent transform rotate-3 blur-md"></div>
        <div className="absolute top-3/4 left-0 w-full h-4 bg-gradient-to-r from-transparent via-violet-500/10 to-transparent transform -rotate-3 blur-md"></div>
      </div>

      {/* 内容容器 - 确保内容在光束之上 */}
      <div className="container relative z-10">
        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-8 gap-6 mb-8">
          {/* Logo和公司信息 */}
          <div className="lg:col-span-2">
            <Logo />
            <p className="mt-3 text-sm opacity-70 text-gray-400">
              {t('imageTools.platformDescription')}
            </p>
            <p className="mt-3 text-sm opacity-70 text-gray-400">
              AIYONG TECHNOLOGY CO., LIMITED
            </p>
            <p className="mt-2 text-sm opacity-70 text-gray-400">
              © {new Date().getFullYear()} <EMAIL>
            </p>
          </div>

          {/* 工具分类展示 */}
          <div className="lg:col-span-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(menuCategories).map(([key, category]) => (
              <div key={key} className="space-y-3">
                <h3 className="font-semibold text-gray-200 text-sm uppercase tracking-wide">
                  {category.label}
                </h3>
                <ul className="space-y-2">
                  {category.items
                    .filter((item: any) => !item.hidden && !item.comingSoon)
                    .slice(0, 6)
                    .map((item: any, index: number) => (
                      <li key={index}>
                        {item.coming ? (
                          <></>
                        ) : (
                          // 先不展示 coming
                          // <div className="text-sm text-gray-500 cursor-not-allowed flex items-center opacity-60">
                          //   <span className="mr-2">{item.title}</span>
                          //   {item.isHot && <HotLabel />}
                          //   {item.isNew && <NewLabel />}
                          //   {item.coming && <ComingLabel />}
                          // </div>
                          <Link
                            href={item.href}
                            className="text-sm text-gray-400 hover:text-purple-300 transition-colors duration-200 flex items-center group"
                          >
                            <span className="group-hover:translate-x-1 transition-transform duration-200 mr-2">
                              {item.title}
                            </span>
                            {item.isHot && <HotLabel />}
                            {item.isNew && <NewLabel />}
                          </Link>
                        )}
                      </li>
                    ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* 底部法律信息 */}
        <div className="pt-6 border-t border-gray-800">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex flex-wrap gap-6">
              <Link
                href="/legal/privacy-policy"
                className="text-sm text-gray-400 hover:text-purple-300 transition-colors"
              >
                {t('privacyPolicy')}
              </Link>
              <Link
                href="/legal/terms"
                className="text-sm text-gray-400 hover:text-purple-300 transition-colors"
              >
                {t('termsAndConditions')}
              </Link>
              <Link
                href="/contact"
                className="text-sm text-gray-400 hover:text-purple-300 transition-colors"
              >
                {t('contact.text')}
              </Link>
              {/* <Link
                href="/about"
                className="text-sm text-gray-400 hover:text-purple-300 transition-colors"
              >
                {t('aboutUs')}
              </Link> */}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
