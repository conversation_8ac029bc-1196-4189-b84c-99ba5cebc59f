'use client'

import { <PERSON> } from '@i18n/routing'
import { usePathname } from '@i18n/routing'
import { LocaleSwitch } from '@shared/components/LocaleSwitch'
import { Logo } from '@shared/components/Logo'
import { Button } from '@ui/components/button'
import { Sheet, SheetContent, SheetTrigger } from '@ui/components/sheet'
import { cn } from '@ui/lib'
import { MenuIcon } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useDebounceCallback } from 'usehooks-ts'
import { getUserFromClientCookies } from '../../../../app/utils/client-cookies'
import Cookies from 'js-cookie'

import { User, LogOut, ChevronDown } from 'lucide-react'
import { analyticsService } from '@/services/analytics'
import { useAtom } from 'jotai'
import { UserInfo, userInfoAtom } from '@marketing/stores'
import { Avatar } from './Avatar'
import { useRouter } from '@i18n/routing'

function UserMenu({ userInfo }: { userInfo: UserInfo }) {
  const [isOpen, setIsOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const t = useTranslations()

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleLogout = () => {
    Cookies.remove('oauth_avatar')
    Cookies.remove('oauth_email')
    Cookies.remove('oauth_id')
    window.location.href = '/'
  }

  return (
    <div className="flex items-center gap-2">
      <div className="relative" ref={menuRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-between font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500/50 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-11 rounded-full text-sm whitespace-nowrap px-3 bg-gray-800/60 hover:bg-gray-700/70 text-gray-100 transition-all border border-purple-500/30 shadow-sm hover:shadow md:min-w-[180px]"
        >
          <div className="flex items-center gap-2.5">
            <Avatar src={userInfo.avatar_url} alt={userInfo.email} />
            <span className="hidden lg:block text-sm font-medium truncate pr-2 max-w-[100px] text-white">
              {userInfo.email.split('@')[0]}
            </span>
          </div>
          <ChevronDown
            className={cn(
              'h-4 w-4 transition-transform duration-200 text-purple-300',
              isOpen && 'rotate-180'
            )}
          />
        </button>

        {isOpen && (
          <div className="absolute right-0 mt-2 w-64 origin-top-right rounded-lg border border-purple-500/30 shadow-lg animate-in fade-in-0 zoom-in-95 bg-gray-800/90 backdrop-blur-md">
            <div className="px-4 py-3">
              <p className="text-sm font-medium text-white">{userInfo.email}</p>
              <p className="text-xs text-purple-300">
                {t('common.points')}: {userInfo?.points ?? 0}
              </p>
            </div>

            <div className="border-t border-purple-500/30">
              <div className="flex flex-col py-2">
                <Link
                  href="/auth/login"
                  className="flex items-center px-4 py-2 text-sm text-gray-200 hover:bg-purple-900/50 transition-colors duration-200"
                >
                  <User className="mr-2 h-4 w-4 text-purple-300" />
                  {t('common.menu.profile')}
                </Link>
              </div>
            </div>

            <div className="border-t border-purple-500/30 py-2">
              <button
                onClick={handleLogout}
                className="flex w-full items-center px-4 py-2 text-sm text-pink-300 hover:bg-purple-900/50 transition-colors duration-200"
              >
                <LogOut className="mr-2 h-4 w-4" />
                {t('common.menu.logout')}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export function NavBarSeo() {
  const t = useTranslations()
  const router = useRouter()
  const [userInfo, setUserInfo] = useAtom(userInfoAtom)
  const user = getUserFromClientCookies()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const pathname = usePathname()
  const [isTop, setIsTop] = useState(true)

  const handleRedirect = () => {
    const redirectPath = localStorage.getItem('REDIRECT_PATH')
    if (redirectPath) {
      console.log('handleRedirect redirectPath', redirectPath)
      localStorage.removeItem('REDIRECT_PATH')
      router.push(redirectPath)
    }
  }

  const getUserInfo = async () => {
    if (user && user.email) {
      const userInfoResponse = await fetch(
        `/api/user/info?email=${encodeURIComponent(user.email)}`
      )
      const userInfoData = await userInfoResponse.json()
      console.log('faith=============userInfoData', userInfoData)
      if (userInfoData.success) {
        setUserInfo(userInfoData.data)
        analyticsService.trackUserVisit(userInfoData.data.id)
        handleRedirect()
      }
    }
  }

  const debouncedScrollHandler = useDebounceCallback(
    () => {
      setIsTop(window.scrollY <= 10)
    },
    150,
    {
      maxWait: 150,
    }
  )

  const menuItems: {
    label: string
    href: string
  }[] = [
    // {
    //   label: t('homepage'),
    //   href: '/',
    // },
  ]

  const isMenuItemActive = (href: string) => {
    // 对于首页特殊处理
    if (href === '/') {
      return pathname === '/'
    }
    // 其他页面精确匹配
    return pathname === href
  }

  const saveRedirectPath = useCallback(() => {
    localStorage.setItem('REDIRECT_PATH', pathname)
  }, [pathname])

  useEffect(() => {
    window.addEventListener('scroll', debouncedScrollHandler)
    debouncedScrollHandler()
    return () => {
      window.removeEventListener('scroll', debouncedScrollHandler)
    }
  }, [debouncedScrollHandler])

  useEffect(() => {
    setMobileMenuOpen(false)
  }, [pathname])

  useEffect(() => {
    getUserInfo()
  }, [])

  return (
    <nav
      className={`fixed top-0 left-0 z-50 w-full ${
        isTop
          ? 'shadow-none'
          : 'bg-gray-900/40 shadow-lg shadow-purple-500/20 backdrop-blur-lg'
      } transition-shadow duration-200`}
      data-test="navigation"
    >
      <div className="w-full max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8 overflow-visible">
        <div
          className={`flex items-center justify-between ${
            isTop ? 'py-4' : 'py-4'
          } transition-[padding] duration-200 overflow-visible`}
        >
          {/* 左侧布局：Logo + 菜单 */}
          <div className="flex items-center gap-8 overflow-visible flex-shrink-0">
            <Logo withLabel />

            <div className="hidden items-center lg:flex">
              <div className="flex items-center gap-0 overflow-hidden">
                {menuItems.map((menuItem) => (
                  <Link
                    key={menuItem.href + menuItem.label}
                    href={menuItem.href}
                    className={cn(
                      'block px-3 py-2 font-medium text-gray-200/90 text-sm whitespace-nowrap flex-shrink-0',
                      isMenuItemActive(menuItem.href)
                        ? 'font-bold text-white'
                        : 'hover:text-white'
                    )}
                  >
                    {menuItem.label}
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧布局 */}
          <div className="flex items-center justify-end gap-3 flex-shrink-0">
            <LocaleSwitch />

            <Sheet
              open={mobileMenuOpen}
              onOpenChange={(open) => setMobileMenuOpen(open)}
            >
              <SheetTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  aria-label="Menu"
                  className="lg:hidden border-purple-500/30 bg-gray-800/60 hover:bg-gray-700/70"
                >
                  <MenuIcon className="size-4 text-purple-300" />
                </Button>
              </SheetTrigger>
              <SheetContent
                className="w-[250px] border-l-0 bg-gray-900/95 backdrop-blur-md border-r border-purple-500/30 text-white"
                side="right"
              >
                <div className="flex flex-col items-start justify-center gap-1">
                  {menuItems.map((menuItem) => (
                    <Link
                      key={menuItem.href}
                      href={menuItem.href}
                      className={cn(
                        'block px-4 py-2.5 font-medium text-base rounded-xl transition-colors',
                        isMenuItemActive(menuItem.href)
                          ? 'bg-purple-900/50 text-purple-200 font-bold'
                          : 'text-gray-300 hover:bg-purple-900/30'
                      )}
                    >
                      {menuItem.label}
                    </Link>
                  ))}

                  <Link
                    key={userInfo ? 'dashboard' : 'login'}
                    href={userInfo ? '/app' : '/auth/login'}
                    className="block px-4 py-2.5 text-base rounded-xl text-gray-300 hover:bg-purple-900/30 transition-colors"
                    prefetch={!userInfo}
                    onClick={saveRedirectPath}
                  >
                    {userInfo
                      ? t('common.menu.dashboard')
                      : t('common.menu.login')}
                  </Link>
                </div>
              </SheetContent>
            </Sheet>

            {userInfo ? (
              <UserMenu userInfo={userInfo} />
            ) : (
              <Button
                key="login"
                className="hidden lg:flex bg-purple-600 hover:bg-purple-500 text-white border-0 shadow-md shadow-purple-500/20"
                asChild
                onClick={saveRedirectPath}
              >
                <Link href="/auth/login">{t('common.menu.login')}</Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
}
