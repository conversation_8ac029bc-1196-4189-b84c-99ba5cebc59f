'use client'

import { MDXContent } from '@content-collections/mdx/react'
import { mdxComponents } from '../utils/mdx-components'
import type { Post, LegalPage, DocumentationPage } from 'content-collections'

type PostContentProps =
  | { post: Post; content?: never }
  | { post?: never; content: any }
  | { post: LegalPage; content?: never }
  | { post: DocumentationPage; content?: never }

export function PostContent({ post, content }: PostContentProps) {
  const mdxContent = content || post?.body

  if (!mdxContent) {
    return (
      <div className="prose dark:prose-invert mx-auto mt-6 max-w-4xl">
        <p>Content not found</p>
      </div>
    )
  }

  return (
    <div className="prose prose-lg prose-invert max-w-4xl mx-auto mt-6 prose-headings:text-white prose-p:text-gray-200 prose-strong:text-white prose-a:text-blue-400 hover:prose-a:text-blue-300 prose-blockquote:text-gray-300 prose-code:text-gray-200">
      <MDXContent code={mdxContent} components={mdxComponents} />
    </div>
  )
}
