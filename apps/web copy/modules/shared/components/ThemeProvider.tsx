'use client'

import { useAtom } from 'jotai'
import {
  themeAtom,
  isAiPageAtom,
  setThemeAtom,
  resolveTheme,
  type Theme,
} from '@marketing/stores'
import { useEffect, useLayoutEffect } from 'react'
import { usePathname } from 'next/navigation'

// 判断是否为AI页面
function isAiPage(pathname: string): boolean {
  // 移除语言前缀后判断
  const pathWithoutLocale =
    pathname.replace(/^\/[a-z]{2}(-[A-Z]{2})?/, '') || '/'
  return pathname.startsWith('/ai/') || pathWithoutLocale.startsWith('/ai/')
}

// 从cookie获取主题
function getThemeFromCookie(): Theme | null {
  if (typeof document === 'undefined') return null

  const match = document.cookie.match(new RegExp('(^| )theme=([^;]+)'))
  const theme = match ? match[2] : null

  return theme === 'light' || theme === 'dark' ? theme : null
}

// 从cookie获取用户覆盖设置
function getUserOverrideFromCookie(): Theme | null {
  if (typeof document === 'undefined') return null

  const match = document.cookie.match(
    new RegExp('(^| )user-theme-override=([^;]+)')
  )
  const override = match ? match[2] : null

  return override === 'light' || override === 'dark' ? override : null
}

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useAtom(themeAtom)
  const [, setIsAiPage] = useAtom(isAiPageAtom)
  const [, setThemeAction] = useAtom(setThemeAtom)
  const pathname = usePathname()

  // 使用 useLayoutEffect 来避免闪烁
  useLayoutEffect(() => {
    // 立即从cookie读取主题并应用到DOM
    const cookieTheme = getThemeFromCookie()
    if (cookieTheme) {
      document.documentElement.classList.remove('light', 'dark')
      document.documentElement.classList.add(cookieTheme)

      // 如果cookie主题与当前atom不一致，更新atom
      if (cookieTheme !== theme) {
        setTheme(cookieTheme)
      }
    }
  }, [])

  // 路径变化时的主题逻辑
  useEffect(() => {
    console.log(pathname, 'pathname')
    const currentIsAiPage = isAiPage(pathname)
    console.log(currentIsAiPage, 'currentIsAiPage')
    setIsAiPage(currentIsAiPage)

    // 获取用户覆盖设置
    const userOverrideFromCookie = getUserOverrideFromCookie()

    // 使用主题解析函数确定最终主题
    const targetTheme = resolveTheme(userOverrideFromCookie, currentIsAiPage)

    // 如果目标主题与当前主题不同，更新主题
    if (targetTheme !== theme) {
      setThemeAction(targetTheme)
    }
  }, [pathname, setIsAiPage, setThemeAction, theme])

  // 主题变化时同步到DOM
  useEffect(() => {
    if (typeof window !== 'undefined') {
      document.documentElement.classList.remove('light', 'dark')
      document.documentElement.classList.add(theme)

      // 设置CSS变量供其他组件使用
      document.documentElement.style.setProperty('--theme', theme)

      // 调试信息
      if (process.env.NODE_ENV === 'development') {
        console.log(`🎨 主题已应用: ${theme} (路径: ${pathname})`)
      }
    }
  }, [theme, pathname])

  return <>{children}</>
}
