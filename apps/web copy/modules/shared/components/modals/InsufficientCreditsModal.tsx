'use client'

import { useTranslations } from 'next-intl'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import PricingSection from '@/[locale]/(marketing)/(home)/components/PricingSection'

interface InsufficientCreditsModalProps {
  isOpen: boolean
  onClose: () => void
  message?: string
}

export function InsufficientCreditsModal({
  isOpen,
  onClose,
  message,
}: InsufficientCreditsModalProps) {
  const t = useTranslations()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="
          max-w-[1200px]
          w-full
          h-[920px]
          bg-[#1A1B1E]
          border-[#2D2E32]
          shadow-xl
        "
      >
        <DialogHeader>
          <DialogTitle className="text-white">
            {t('insufficientCredits')}
          </DialogTitle>
        </DialogHeader>
        <div className="w-full bg-red-500/10 border border-red-500/20 rounded-xl p-4">
          <p className="text-center font-semibold text-xl text-purple-300">
            <span className="text-red-500">{message}</span>
          </p>
        </div>
        <PricingSection needTitle={false} className="leading-none" />
      </DialogContent>
    </Dialog>
  )
}