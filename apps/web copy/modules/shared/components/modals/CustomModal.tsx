'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON><PERSON>itle,
  DialogFooter,
} from '@ui/components/dialog'
import { Button } from '@ui/components/button'
import { ModalConfig } from '@shared/stores'

interface CustomModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  config: ModalConfig
}

export function CustomModal({
  isOpen,
  onClose,
  onConfirm,
  config,
}: CustomModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        {config.title && (
          <DialogHeader>
            <DialogTitle>{config.title}</DialogTitle>
          </DialogHeader>
        )}
        
        {config.content && (
          <div className="py-4">
            <div 
              className="text-gray-600 text-sm leading-relaxed"
              dangerouslySetInnerHTML={{ __html: config.content }}
            />
          </div>
        )}

        <DialogFooter className="flex space-x-2">
          {config.showCancel && (
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              {config.cancelText || '取消'}
            </Button>
          )}
          {config.onConfirm && (
            <Button
              onClick={onConfirm}
              className="flex-1"
            >
              {config.confirmText || '确认'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}