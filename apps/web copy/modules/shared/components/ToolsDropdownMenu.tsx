'use client'

import { cn } from '@ui/lib'
import { useState, useCallback, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Link, usePathname } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import {
  ChevronRight,
  Camera,
  Image as ImageIcon,
  FileText,
  Settings,
  Zap,
  ChevronDown,
} from 'lucide-react'
import { getMenuCategories } from './Logo'
import { useAtom } from 'jotai'
import { themeAtom } from '@marketing/stores'

// 类型定义
interface MenuItem {
  title: string
  desc: string
  href: string
  seoHref?: string
  isHot?: boolean
  isNew?: boolean
  coming?: boolean
  preview?: string
  hidden?: boolean
  point?: number
  icon?: JSX.Element
  isVideo?: boolean
}

// 热门和新品标签组件
const HotLabel = () => {
  const t = useTranslations()
  return (
    <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-fuchsia-500 to-purple-600 px-2 py-0.5 text-xs font-medium text-white ml-1.5 shadow-sm">
      {t('common.menu.hot')}
    </span>
  )
}

const NewLabel = () => {
  const t = useTranslations()
  return (
    <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-violet-500 to-purple-600 px-2 py-0.5 text-xs font-medium text-white ml-1.5 shadow-sm">
      {t('common.menu.new')}
    </span>
  )
}

const ComingLabel = () => {
  const t = useTranslations()
  return (
    <span className="inline-flex items-center justify-center bg-orange-800/60 text-orange-300 px-2 py-0.5 text-xs font-medium rounded ml-1.5 whitespace-nowrap border border-orange-500/20">
      {t('common.menu.coming')}
    </span>
  )
}

// 获取工具对应的图标
const getToolIcon = (category: string) => {
  const iconMap = {
    business: <ImageIcon className="w-5 h-5 text-purple-400" />,
    creative: <Zap className="w-5 h-5 text-fuchsia-400" />,
    memory: <FileText className="w-5 h-5 text-pink-400" />,
    imageTools: <Camera className="w-5 h-5 text-violet-400" />,
    videoCreation: <Camera className="w-5 h-5 text-purple-300" />,
    utilities: <Settings className="w-5 h-5 text-indigo-400" />,
    fun: <Zap className="w-5 h-5 text-pink-400" />,
  }

  return (
    iconMap[category as keyof typeof iconMap] || (
      <Camera className="w-5 h-5 text-purple-400" />
    )
  )
}

// Tools Dropdown Menu Component
interface ToolsDropdownMenuProps {
  children: React.ReactNode
  className?: string
}

export function ToolsDropdownMenu({
  children,
  className,
}: ToolsDropdownMenuProps) {
  const [isHovered, setIsHovered] = useState(false)
  const pathname = usePathname()
  const router = useRouter()
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const t = useTranslations()
  const [theme] = useAtom(themeAtom)

  // 检查是否在 AI 页面
  const isAiPage = pathname.startsWith('/ai/')

  // 菜单数据结构
  const menuCategories = getMenuCategories(isAiPage, t)

  // 导航处理函数
  const handleNavigation = useCallback(() => {
    setIsHovered(false)
  }, [])

  // 处理鼠标进入事件
  const handleMouseEnter = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    setIsHovered(true)
  }, [])

  // 处理鼠标离开事件
  const handleMouseLeave = useCallback(() => {
    timeoutRef.current = setTimeout(() => {
      setIsHovered(false)
    }, 150)
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return (
    <div
      className={cn('relative', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}

      {/* Dropdown Menu */}
      <div
        className={cn(
          'absolute max-h-[calc(100vh-62px)] overscroll-y-contain !overflow-y-auto top-[calc(100%+8px)] left-1/2 backdrop-blur-md rounded-xl overflow-hidden z-[200]',
          'w-[1120px] max-w-[95vw]',
          'transition-all  origin-top',
          theme === 'light'
            ? 'bg-white border border-blue-200/50'
            : 'bg-gray-900/95 border border-purple-500/20',
          isHovered
            ? theme === 'light'
              ? 'animate-dropdown opacity-100 visible -translate-x-1/3 translate-y-0 shadow-[0_4px_20px_-2px_rgba(0,0,0,0.1),0_0_15px_-3px_rgba(59,130,246,0.2)] border-blue-200/60'
              : 'animate-dropdown opacity-100 visible -translate-x-1/3 translate-y-0 shadow-[0_4px_20px_-2px_rgba(0,0,0,0.3),0_0_15px_-3px_rgba(127,50,237,0.3)] border-purple-500/30'
            : 'opacity-0 invisible -translate-x-1/3 translate-y-[-8px] shadow-none pointer-events-none'
        )}
        style={{ '--translate-x': '-33.33%' } as React.CSSProperties}
      >
        <div className="p-8">
          {/* AI Image Tools Section */}
          <div className="mb-8">
            <h2
              className={cn(
                'text-xl font-semibold mb-6',
                theme === 'light' ? 'text-gray-900' : 'text-white'
              )}
            >
              {t('common.menu.aiImageTools')}
            </h2>

            {/* Featured Tools - 4 cards in a row */}
            <div className="grid grid-cols-4 gap-4 mb-6">
              {/* AI Image Generator */}
              <Link
                href={'/tools/ai-text-to-image'}
                onClick={() => handleNavigation()}
                className={cn(
                  'group relative rounded-xl p-4 cursor-pointer transition-all',
                  theme === 'light'
                    ? 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:shadow-lg hover:shadow-blue-500/20'
                    : 'bg-gradient-to-r from-purple-600 to-pink-600 hover:shadow-lg hover:shadow-purple-500/20'
                )}
              >
                <div className="absolute top-3 left-3">
                  <span
                    className={cn(
                      'text-white text-xs px-2 py-1 rounded-full font-medium',
                      theme === 'light' ? 'bg-white/20' : 'bg-blue-500'
                    )}
                  >
                    {t('common.menu.free')}
                  </span>
                </div>
                <div className="pt-8">
                  <h3 className="text-white font-medium text-lg mb-2">
                    {t('common.menu.aiImageGenerator')}
                  </h3>
                  <p className="text-white/80 text-sm">
                    {t('common.menu.generateStunningImages')}
                  </p>
                </div>
              </Link>

              {/* Face Swap Photo */}
              <Link
                href={'/tools/ai-face-swap'}
                onClick={() => handleNavigation()}
                className={cn(
                  'group relative rounded-xl p-4 cursor-pointer transition-all',
                  theme === 'light'
                    ? 'bg-gradient-to-r from-green-500 to-teal-600 hover:shadow-lg hover:shadow-green-500/20'
                    : 'bg-gradient-to-r from-green-600 to-blue-600 hover:shadow-lg hover:shadow-purple-500/20'
                )}
              >
                <div className="absolute top-3 left-3">
                  <span
                    className={cn(
                      'text-white text-xs px-2 py-1 rounded-full font-medium',
                      theme === 'light' ? 'bg-white/20' : 'bg-blue-500'
                    )}
                  >
                    {t('common.menu.free')}
                  </span>
                </div>
                <div className="pt-8">
                  <h3 className="text-white font-medium text-lg mb-2">
                    {t('common.menu.faceSwapPhoto')}
                  </h3>
                  <p className="text-white/80 text-sm">
                    {t('common.menu.swapFacesInPhoto')}
                  </p>
                </div>
              </Link>

              {/* AI Headshot Generator */}
              <Link
                href={'/tools/ai-headshot-generator'}
                onClick={() => handleNavigation()}
                className={cn(
                  'group relative rounded-xl p-4 cursor-pointer transition-all',
                  theme === 'light'
                    ? 'bg-gradient-to-r from-indigo-500 to-purple-600 hover:shadow-lg hover:shadow-indigo-500/20'
                    : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:shadow-lg hover:shadow-purple-500/20'
                )}
              >
                <div className="absolute top-3 left-3">
                  <span
                    className={cn(
                      'text-white text-xs px-2 py-1 rounded-full font-medium',
                      theme === 'light' ? 'bg-white/20' : 'bg-blue-500'
                    )}
                  >
                    {t('common.menu.free')}
                  </span>
                </div>
                <div className="pt-8">
                  <h3 className="text-white font-medium text-lg mb-2">
                    {t('common.menu.aiHeadshotGenerator')}
                  </h3>
                  <p className="text-white/80 text-sm">
                    {t('common.menu.createProHeadshots')}
                  </p>
                </div>
              </Link>

              {/* AI Selfie Generator */}
              <Link
                href={'/tools/ghibli'}
                onClick={() => handleNavigation()}
                className={cn(
                  'group relative rounded-xl p-4 cursor-pointer transition-all',
                  theme === 'light'
                    ? 'bg-gradient-to-r from-pink-500 to-rose-600 hover:shadow-lg hover:shadow-pink-500/20'
                    : 'bg-gradient-to-r from-pink-600 to-purple-600 hover:shadow-lg hover:shadow-purple-500/20'
                )}
              >
                <div className="absolute top-3 left-3">
                  <span
                    className={cn(
                      'text-white text-xs px-2 py-1 rounded-full font-medium',
                      theme === 'light' ? 'bg-white/20' : 'bg-blue-500'
                    )}
                  >
                    {t('common.menu.free')}
                  </span>
                </div>
                <div className="pt-8">
                  <h3 className="text-white font-medium text-lg mb-2">
                    {t('common.menu.aiGhibliGenerator')}
                  </h3>
                  <p className="text-white/80 text-sm">
                    {t('common.menu.createGhibliImage')}
                  </p>
                </div>
              </Link>
            </div>
          </div>

          {/* Additional Image Tools Section */}
          <div>
            <h2
              className={cn(
                'text-xl font-semibold mb-6',
                theme === 'light' ? 'text-gray-900' : 'text-white'
              )}
            >
              {t('common.menu.additionalImageTools')}
            </h2>

            {/* Grid of additional tools */}
            <div className="grid grid-cols-4 gap-4">
              {/* 合并所有分类的工具，排除 coming: true 的项目，展示全部 */}
              {Object.entries(menuCategories)
                .flatMap(([categoryKey, category]) =>
                  category.items
                    .filter((item: MenuItem) => !item.hidden && !item.coming)
                    .map((item: MenuItem) => ({
                      ...item,
                      categoryKey,
                    }))
                )
                .map(
                  (item: MenuItem & { categoryKey: string }, index: number) => (
                    <Link
                      key={index}
                      href={item.seoHref || ''}
                      onClick={() => handleNavigation()}
                      className={cn(
                        'group flex items-center gap-3 p-4 rounded-lg transition-all cursor-pointer',
                        theme === 'light'
                          ? 'hover:bg-gray-100/60'
                          : 'hover:bg-gray-800/60'
                      )}
                    >
                      <div
                        className={cn(
                          'w-10 h-10 rounded-lg flex items-center justify-center',
                          theme === 'light' ? 'bg-gray-200' : 'bg-gray-800'
                        )}
                      >
                        {item.icon || getToolIcon(item.categoryKey)}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3
                            className={cn(
                              'font-medium text-sm',
                              theme === 'light' ? 'text-gray-900' : 'text-white'
                            )}
                          >
                            {item.title}
                          </h3>
                          <span
                            className={cn(
                              'text-white text-xs px-2 py-0.5 whitespace-nowrap rounded-full',
                              theme === 'light' ? 'bg-blue-500' : 'bg-blue-500'
                            )}
                          >
                            {t('common.menu.free')}
                          </span>
                        </div>
                      </div>
                    </Link>
                  )
                )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
