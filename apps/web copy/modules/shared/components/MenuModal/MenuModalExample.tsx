'use client'

import { Button } from '@ui/components/button'
import { useMenuModal, type CategoryId } from '@shared/hooks/useMenuModal'

/**
 * 菜单模态框使用示例组件
 */
export function MenuModalExample() {
  const { openMenuModal } = useMenuModal()

  const categories: Array<{ id: CategoryId; label: string; color: string }> = [
    {
      id: 'business',
      label: 'Business Tools',
      color: 'from-purple-500 to-purple-700',
    },
    {
      id: 'creative',
      label: 'Creative Tools',
      color: 'from-violet-500 to-fuchsia-700',
    },
    { id: 'memory', label: 'Memory Tools', color: 'from-pink-500 to-rose-700' },
    {
      id: 'imageTools',
      label: 'Image Tools',
      color: 'from-fuchsia-500 to-indigo-700',
    },
  ]

  return (
    <div className="p-6 space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          菜单模态框示例
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          点击下面的按钮打开不同分类的工具菜单
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {categories.map((category) => (
          <div
            key={category.id}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-200"
          >
            <div
              className={`w-12 h-12 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center mb-4`}
            >
              <span className="text-white font-bold text-lg">AI</span>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              {category.label}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              查看 {category.label.toLowerCase()} 相关的AI工具
            </p>
            <Button
              onClick={() => openMenuModal(category.id)}
              className={`w-full bg-gradient-to-r ${category.color} hover:opacity-90 text-white font-semibold`}
            >
              打开 {category.label}
            </Button>
          </div>
        ))}
      </div>

      <div className="bg-gray-100 dark:bg-gray-800 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
          使用方法
        </h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              1. 在组件中使用hook：
            </h4>
            <pre className="bg-white dark:bg-gray-900 p-3 rounded-lg text-sm overflow-x-auto">
              {`import { useMenuModal } from '@shared/hooks/useMenuModal'

function MyComponent() {
  const { openMenuModal } = useMenuModal()
  
  const handleClick = () => {
    openMenuModal('business') // 打开商业工具菜单
  }
  
  return <button onClick={handleClick}>打开菜单</button>
}`}
            </pre>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              2. 直接使用atom（在非组件中）：
            </h4>
            <pre className="bg-white dark:bg-gray-900 p-3 rounded-lg text-sm overflow-x-auto">
              {`import { store } from '@shared/stores'
import { showMenuModalAtom } from '@shared/stores/modalAtoms'

// 在任何地方调用
store.set(showMenuModalAtom, 'creative')`}
            </pre>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              3. 支持的分类ID：
            </h4>
            <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <li>
                <code>business</code> - 商业工具
              </li>
              <li>
                <code>creative</code> - 创意工具
              </li>
              <li>
                <code>memory</code> - 记忆工具
              </li>
              <li>
                <code>imageTools</code> - 图像工具
              </li>
              <li>
                <code>videoTools</code> - 视频工具
              </li>
              <li>
                <code>additionalTools</code> - 其他工具
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
