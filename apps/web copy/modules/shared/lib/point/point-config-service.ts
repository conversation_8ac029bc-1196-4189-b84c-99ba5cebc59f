/**
 * 积分配置服务 - 纯函数版本
 * 提供完整的积分计算和管理功能
 */

import {
  taskTypePointConfig,
  type PointCalculationParams,
  type PointCalculationResult,
  type TaskType,
  type ApiProvider,
  type PointCondition,
} from './page-point-config'

// 重新导出类型，方便外部使用
export type {
  PointCalculationParams,
  PointCalculationResult,
  TaskType,
  ApiProvider,
}

// ========== 核心计算函数 ==========

/**
 * 计算任务积分 - 系统的核心函数
 */
const calculateTaskPoints = (
  params: PointCalculationParams
): PointCalculationResult | null => {
  const { taskType, ...otherParams } = params
  const config = taskTypePointConfig[taskType]

  if (!config) {
    return null
  }

  // 寻找匹配的积分规则
  let matchedRule: PointCondition | null = null

  // 优先匹配条件规则
  if (Object.keys(otherParams).length > 0) {
    for (const rule of config.pointRules) {
      if (
        'conditions' in rule &&
        rule.conditions &&
        rule.conditions(otherParams)
      ) {
        matchedRule = rule
        break
      }
    }
  }

  // 如果没有匹配到条件规则，使用默认规则
  if (!matchedRule) {
    matchedRule =
      config.pointRules.find((rule) => rule.isDefault) || config.pointRules[0]
  }

  if (!matchedRule) {
    return null
  }

  // 计算批次倍数（如果有批次大小）
  const batchMultiplier = params.batchSize || 1
  const nVariants =
    typeof params.nVariants === 'string'
      ? parseInt(params.nVariants)
      : params.nVariants || 1
  const totalMultiplier = Math.max(batchMultiplier, nVariants)

  const finalPoints = matchedRule.points * totalMultiplier

  return {
    points: finalPoints,
    description:
      matchedRule.description || `${config.description} (${finalPoints}积分)`,
    rule: matchedRule,
  }
}

/**
 * 通过页面路径获取taskType
 */
const getPathnameTaskType = (pathname: string): TaskType | null => {
  for (const [taskType, config] of Object.entries(taskTypePointConfig)) {
    if (config.pathname === pathname) {
      return taskType as TaskType
    }
  }
  return null
}

// ========== 前端函数 ==========

/**
 * 通过页面路径获取积分
 * 主要用于前端Hook中的权限检查
 */
export const getPagePoints = (pathname: string, params?: any): number => {
  const taskType = getPathnameTaskType(pathname)
  if (!taskType) {
    return 0
  }

  const result = calculateTaskPoints({ taskType, ...params })
  return result?.points || 0
}

/**
 * 检查用户是否有足够积分
 */
export const checkPoints = (
  userPoints: number,
  pathname: string,
  params?: any
): boolean => {
  const requiredPoints = getPagePoints(pathname, params)
  return userPoints >= requiredPoints
}

// ========== 后端函数 ==========

/**
 * 获取API积分配置
 * 主要用于后端API中的积分扣除
 */
export const getApiPointConfig = (
  taskType: TaskType,
  inputParams: any = {}
): {
  pointsConfig: number
  pointsDescription: string
  batchMultiplier: number
  taskType: TaskType
} => {
  const result = calculateTaskPoints({ taskType, ...inputParams })

  if (!result) {
    throw new Error(`Unsupported task type: ${taskType}`)
  }
  return {
    pointsConfig: result.points,
    pointsDescription: result.description,
    batchMultiplier: 1, // 已经在calculateTaskPoints中计算过了
    taskType,
  }
}

/**
 * 验证任务类型有效性
 */
export const isValidTaskType = (taskType: TaskType): boolean => {
  const config = taskTypePointConfig[taskType]
  return config !== null
}

// ========== 便利函数 ==========

/**
 * 后端便利函数 - 获取API积分数量
 */
export const getApiPoints = (taskType: TaskType, inputParams?: any): number => {
  return getApiPointConfig(taskType, inputParams).pointsConfig
}

/**
 * 通过页面路径获取积分详情
 * 返回完整的积分信息，包括积分、描述和规则
 */
export const getPointDetails = (
  pathname: string,
  params?: any
): PointCalculationResult | null => {
  const taskType = getPathnameTaskType(pathname)
  if (!taskType) {
    return null
  }
  return calculateTaskPoints({ taskType, ...params })
}

/**
 * 通过taskType获取积分详情
 * 支持条件化积分计算
 */
export const getPointsByTaskType = (
  taskType: TaskType,
  params?: any
): PointCalculationResult | null => {
  return calculateTaskPoints({ taskType, ...params })
}

// ========== 使用示例 ==========

/**
 * 使用示例：
 *
 * // 前端Hook中使用
 * import { getPagePoints, checkPoints, getPointDetails } from '@/modules/shared/lib/point/point-config-service'
 *
 * const requiredPoints = getPagePoints('/ai/face-swap')
 * const canUse = checkPoints(userPoints, '/ai/face-swap')
 * const pointDetails = getPointDetails('/ai/face-swap', { duration: 8 })
 *
 * // 后端API中使用
 * import { getApiPoints, getApiPointConfig } from '@/modules/shared/lib/point/point-config-service'
 *
 * const points = getApiPoints('face-swap', input)
 * const config = getApiPointConfig('face-swap', input)
 *
 * // 验证任务类型
 * import { isValidTaskType } from '@/modules/shared/lib/point/point-config-service'
 *
 * const isValid = isValidTaskType('face-swap')
 *
 * // 通过taskType获取积分详情
 * import { getPointsByTaskType } from '@/modules/shared/lib/point/point-config-service'
 *
 * const taskPoints = getPointsByTaskType('imagetovideo', { duration: 8, quality: '1080p' })
 */
