'use client'

import { useCallback, useEffect } from 'react'
import { useAtom } from 'jotai'
import { userInfoAtom } from '@marketing/stores/userInfoAtom'
import { useModal } from './useModal'
import { usePathname } from 'next/navigation'
import {
  getPointDetails,
  getPointsByTaskType,
  TaskType,
  type PointCalculationResult,
} from '../lib/point/point-config-service'
import { fetchUserInfo } from '@/lib/user'

export interface PermissionCheckResult {
  success: boolean
  error?:
    | 'NOT_LOGGED_IN'
    | 'NOT_SUBSCRIBED'
    | 'INSUFFICIENT_POINTS'
    | 'BLACKLISTED'
    | 'PAGE_NOT_CONFIGURED'
  message?: string
  requiredPoints?: number
  currentPoints?: number
  scenarioName?: string
}

/**
 * 全局权限校验 Hook - 完全自动化版本
 *
 * 特性：
 * - 无需传递任何参数，基于当前页面pathname自动获取积分配置
 * - 自动获取功能名称，优化用户体验
 * - 完整的权限校验流程和错误处理
 *
 * 使用方式：
 * ```ts
 * const { checkPermissions, calculatePoints } = usePermissionCheck()
 *
 * // 基础使用 - 完全自动化
 * const result = await checkPermissions()
 *
 * // 动态积分计算
 * const points = calculatePoints({ duration: 8, quality: '1080p' })
 *
 * // 实时积分计算
 * const customPoints = calculatePoints({ duration: 5, quality: '720p' })
 * ```
 *
 * 校验流程：
 * 1. 自动获取当前页面积分配置
 * 2. 检查用户登录状态
 * 3. 检查用户订阅状态
 * 4. 检查用户积分是否充足
 * 5. 检查用户黑名单状态
 */
export function usePermissionCheck() {
  const [userInfo, setUserInfo] = useAtom(userInfoAtom)
  const pathname = usePathname()
  const { showLoginModal, showSubscriptionModal, showMaintenanceModal } =
    useModal()

  const updateUserInfo = useCallback(async () => {
    const userInfo = await fetchUserInfo()
    setUserInfo(userInfo)
  }, [setUserInfo])

  /**
   * 自动权限校验 - 基于当前页面和动态参数自动检查权限
   * 无需传递任何参数，完全自动化
   */
  const checkPermissions =
    useCallback(async (): Promise<PermissionCheckResult> => {
      try {
        // 获取当前页面的积分配置
        const pointDetails = getPointDetails(pathname)

        if (!pointDetails) {
          // 页面未配置积分，使用默认处理
          console.warn(`页面 ${pathname} 未找到积分配置`)
          return {
            success: false,
            error: 'PAGE_NOT_CONFIGURED',
            message: `页面 ${pathname} 未配置积分信息`,
            scenarioName: '该功能',
            requiredPoints: 0,
            currentPoints: userInfo?.points || 0,
          }
        }

        const requiredPoints = pointDetails.points
        const scenarioName = pointDetails.description || '该功能'

        // 1. 检查用户登录状态
        if (!userInfo) {
          showLoginModal({
            title: '需要登录',
            content: `使用${scenarioName}需要先登录`,
          })
          return {
            success: false,
            error: 'NOT_LOGGED_IN',
            message: '用户未登录',
            scenarioName,
            requiredPoints,
            currentPoints: 0,
          }
        }

        // 2. 检查用户订阅状态
        const isSubscribed =
          userInfo.is_membership_active &&
          userInfo.membership_status === 'active'

        // if (!isSubscribed) {
        //   showSubscriptionModal({
        //     title: '升级会员',
        //     content: `使用${scenarioName}需要升级为会员`,
        //     props: {
        //       showInsufficientCredits: false
        //     }
        //   })
        //   return {
        //     success: false,
        //     error: 'NOT_SUBSCRIBED',
        //     message: '用户未订阅',
        //     scenarioName,
        //     requiredPoints,
        //     currentPoints: userInfo.points,
        //   }
        // }

        // 3. 检查用户积分
        if (userInfo.points < requiredPoints) {
          showSubscriptionModal({
            title: '积分不足',
            content: `使用${scenarioName}需要 ${requiredPoints} 积分, 升级为会员以享受更多功能和权益`,
            props: {
              showInsufficientCredits: false,
              message: `积分不足！当前积分：${userInfo.points}，需要积分：${requiredPoints}`,
            },
          })
          return {
            success: false,
            error: 'INSUFFICIENT_POINTS',
            message: `积分不足，当前：${userInfo.points}，需要：${requiredPoints}`,
            scenarioName,
            requiredPoints,
            currentPoints: userInfo.points,
          }
        }

        // 4. 检查用户黑名单状态
        // 注意：这里假设黑名单用户的 membership_status 为 'suspended'
        // 你可以根据实际的黑名单逻辑调整这个判断
        if (userInfo.membership_status === 'suspended') {
          showMaintenanceModal({
            title: '账户受限',
            content: '您的账户已被限制使用，请联系管理员处理',
          })
          return {
            success: false,
            error: 'BLACKLISTED',
            message: '用户账户受限',
            scenarioName,
            requiredPoints,
            currentPoints: userInfo.points,
          }
        }

        // 所有检查通过
        return {
          success: true,
          scenarioName,
          requiredPoints,
          currentPoints: userInfo.points,
        }
      } catch (error) {
        console.error('Permission check failed:', error)

        // 发生错误时显示系统维护提示
        showMaintenanceModal({
          title: '系统异常',
          content: '系统检查权限时发生异常，请稍后再试',
        })

        return {
          success: false,
          error: 'BLACKLISTED', // 使用这个错误类型来表示系统异常
          message: '系统异常',
          currentPoints: userInfo?.points || 0,
        }
      }
    }, [
      userInfo,
      showLoginModal,
      showSubscriptionModal,
      showMaintenanceModal,
      pathname,
    ])

  /**
   * 实时积分计算 - 支持动态参数计算积分
   * 示例：calculatePoints({ duration: 8, quality: '1080p' })
   */
  const calculatePoints = useCallback(
    (dynamicParams?: any): number => {
      const pointDetails = getPointDetails(pathname, dynamicParams)

      console.log('pointDetails', pointDetails)
      return pointDetails?.points || 10
    },
    [pathname]
  )

  /**
   * 检查是否为有效的订阅用户
   */
  const isValidSubscriber = useCallback(() => {
    return (
      userInfo?.is_membership_active && userInfo?.membership_status === 'active'
    )
  }, [userInfo])

  /**
   * 检查是否有足够积分
   */
  const hasEnoughPoints = useCallback(
    (requiredPoints: number) => {
      return (userInfo?.points || 0) >= requiredPoints
    },
    [userInfo]
  )

  /**
   * 检查是否为黑名单用户
   */
  const isBlacklisted = useCallback(() => {
    return userInfo?.membership_status === 'suspended'
  }, [userInfo])

  /**
   * 通过taskType获取动态积分配置
   * 支持条件化积分计算，如imagetovideo的duration和quality参数
   */
  const getTaskTypePoints = useCallback(
    (taskType: TaskType, taskParams?: any): PointCalculationResult | null => {
      return getPointsByTaskType(taskType, taskParams)
    },
    []
  )

  /**
   * 获取当前页面的动态积分详情
   * 支持参数化积分计算
   */
  const getPagePointDetails = useCallback(
    (pageParams?: any): PointCalculationResult | null => {
      return getPointDetails(pathname, pageParams)
    },
    [pathname]
  )

  /**
   * 检查用户积分是否足够（支持动态积分计算）
   * 示例：checkTaskTypePoints('imagetovideo', { duration: 8, quality: '1080p' })
   */
  const checkTaskTypePoints = useCallback(
    (taskType: TaskType, taskParams?: any): boolean => {
      const pointDetails = getPointsByTaskType(taskType, taskParams)
      if (!pointDetails || !userInfo) return false
      return userInfo.points >= pointDetails.points
    },
    [userInfo]
  )

  return {
    // 主要方法
    checkPermissions,
    calculatePoints,
    updateUserInfo,

    // 便捷检查方法
    isValidSubscriber,
    hasEnoughPoints,
    isBlacklisted,

    // 动态积分计算方法
    getTaskTypePoints,
    getPagePointDetails,
    checkTaskTypePoints,

    // 用户状态
    isLoggedIn: !!userInfo,
    userInfo,
  }
}
