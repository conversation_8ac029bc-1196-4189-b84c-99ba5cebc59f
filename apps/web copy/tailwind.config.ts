import baseConfig from 'tailwind-config'
import type { Config } from 'tailwindcss'

// 吉卜力风格色彩系统
const colors = {
  // 天空与海洋色系 (替代原来的紫色)
  primary: {
    light: '#8ECAE6', // 天空蓝浅色
    DEFAULT: '#219EBC', // 湖水蓝
    dark: '#126782', // 深湖蓝
  },
  // 温暖与大地色系 (替代原来的粉色)
  secondary: {
    light: '#FFB703', // 金色阳光
    DEFAULT: '#FB8500', // 温暖橙色
    dark: '#CC5803', // 深橙棕色
  },
  // 背景色系
  background: {
    primary: '#F6F1E6', // 纸张质感色 (替代深黑色)
    secondary: '#FFF9E8', // 奶油色 (替代稍浅黑色)
    card: '#FFFFFF', // 卡片背景色
    input: 'rgba(246, 241, 230, 0.7)', // 输入框背景色
  },
  // 文字色系
  text: {
    primary: '#2B3A42', // 深墨色 (替代白色)
    secondary: '#3F4E5C', // 中性灰色
    disabled: '#6C7A89', // 淡灰色
  },
  // 边框色系
  border: {
    DEFAULT: '#E0E7EB', // 淡灰蓝边框
    light: '#F0F5F9', // 浅蓝白色边框
  },
  // 自然绿色系 (新增)
  accent: {
    light: '#B5E48C', // 嫩芽绿
    DEFAULT: '#76C893', // 草地绿
    dark: '#52B69A', // 深绿色
  },
  // 渐变色
  'gradient-totoro': {
    DEFAULT: 'linear-gradient(to right, #76C893, #B5E48C)', // 龙猫绿渐变
    bg: 'bg-gradient-to-r from-accent-dark to-accent-light', // 背景渐变
  },
  'gradient-spirited': {
    DEFAULT: 'linear-gradient(to right, #219EBC, #8ECAE6)', // 千与千寻蓝渐变
    bg: 'bg-gradient-to-r from-primary-dark to-primary-light', // 背景渐变
  },
  'gradient-howl': {
    DEFAULT: 'linear-gradient(to right, #FB8500, #FFB703)', // 哈尔的城堡金橙渐变
    bg: 'bg-gradient-to-r from-secondary-dark to-secondary-light', // 背景渐变
  },
}

export default {
  theme: {
    extend: {
      colors,
      backgroundColor: colors,
      borderColor: colors,
      textColor: colors,
      backgroundImage: {
        'custom-gradient':
          'linear-gradient(208deg, rgba(134,235,140,1) 0%, rgba(138,240,110,1) 35%, rgba(161,250,92,1) 100%)',
        'gradient-totoro': 'linear-gradient(to right, #76C893, #B5E48C)',
        'gradient-spirited': 'linear-gradient(to right, #219EBC, #8ECAE6)',
        'gradient-howl': 'linear-gradient(to right, #FB8500, #FFB703)',
        'gradient-mononoke': 'linear-gradient(to right, #52B69A, #168AAD)',
        'gradient-kiki': 'linear-gradient(135deg, #FF9999, #F08CAE)',
        clouds:
          'url(\'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000" preserveAspectRatio="none"%3E%3Cpath fill="%23F0F5F9" d="M0,0 C200,100 300,50 500,150 C700,250 800,200 1000,300 L1000,1000 L0,1000 Z"%3E%3C/path%3E%3C/svg%3E\')',
      },
      transitionProperty: {
        default:
          'color, background-color, border-color, text-decoration-color, fill, stroke',
      },
      transitionDuration: {
        default: '300ms',
        slow: '500ms',
      },
      transitionTimingFunction: {
        default: 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      keyframes: {
        rotate: {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        sway: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
        slideInLeft: {
          '0%': { transform: 'translateX(-100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInRight: {
          '0%': { transform: 'translateX(100%)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' },
        },
        slideInTop: {
          '0%': { transform: 'translateY(-100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideInBottom: {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        moveAnimation: {
          from: {
            opacity: '0',
            transform: 'translateX(var(--translate-x, 0)) translateY(100px)',
          },
          to: {
            opacity: '1',
            transform: 'translateX(var(--translate-x, 0)) translateY(0)',
          },
        },
      },
      animation: {
        dropdown: 'moveAnimation 0.5s ease-out forwards',
        'spin-slow': 'rotate 20s linear infinite',
        float: 'float 6s ease-in-out infinite',
        sway: 'sway 4s ease-in-out infinite',
        'slide-in-left': 'slideInLeft 1s ease-out forwards',
        'slide-in-right': 'slideInRight 1s ease-out forwards',
        'slide-in-top': 'slideInTop 1s ease-out forwards',
        'slide-in-bottom': 'slideInBottom 1s ease-out forwards',
      },
      boxShadow: {
        ghibli: '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04)',
        'ghibli-hover':
          '0 10px 25px rgba(0, 0, 0, 0.1), 0 6px 10px rgba(0, 0, 0, 0.04)',
      },
      borderRadius: {
        ghibli: '0.75rem',
      },
    },
  },
  presets: [baseConfig],
  content: ['./app/**/*.tsx', './modules/**/*.tsx'],
  safelist: [
    'ml-2',
    'ml-4',
    'ml-6',
    'ml-8',
    'ml-10',
    'bg-primary',
    'bg-secondary',
    'bg-accent',
    'text-primary',
    'text-secondary',
    'text-accent',
    'border-primary',
    'border-secondary',
    'border-accent',
    'bg-gradient-totoro',
    'bg-gradient-spirited',
    'bg-gradient-howl',
    'bg-gradient-mononoke',
    'bg-gradient-kiki',
  ],
} satisfies Config
