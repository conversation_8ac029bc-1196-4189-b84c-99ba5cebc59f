import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  try {
    const { 
      originalImageBase64, 
      removedBackgroundBase64, 
      blurIntensity = 20 
    } = await request.json();

    if (!originalImageBase64 || !removedBackgroundBase64) {
      return NextResponse.json(
        { success: false, error: 'Missing required image parameters' },
        { status: 400 }
      );
    }

    // Validate blur intensity
    const intensity = Math.max(5, Math.min(100, blurIntensity));

    // Always use client-side processing for now
    // Server-side canvas processing requires additional dependencies
    console.log('Using client-side processing for background blur');

    return NextResponse.json({
      success: true,
      fallbackToClient: true,
      originalImageBase64,
      removedBackgroundBase64,
      blurIntensity: intensity
    });

  } catch (error) {
    console.error('Background blur API error:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
