import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url)
  const id = searchParams.get('id')

  if (!id) {
    return NextResponse.json({ error: '记录ID必须提供' }, { status: 400 })
  }

  try {
    const { error } = await supabase
      .from('img4o_image_generation_history')
      .update({ is_deleted: true, updated_at: new Date().toISOString() })
      .eq('id', id)

    if (error) {
      console.error('删除记录失败:', error)
      return NextResponse.json({ error: '删除记录失败' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: '记录已成功删除',
    })
  } catch (error) {
    console.error('删除记录失败:', error)
    return NextResponse.json(
      { error: '服务器错误，请稍后再试' },
      { status: 500 }
    )
  }
}
