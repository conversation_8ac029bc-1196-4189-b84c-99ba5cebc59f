import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getUserFromServerCookies } from '../../../utils/server-cookies'
import { PointsService } from '../../../services/points'
import { createHistoryRecordGeneric } from '../../aiimage/utils/history'
import { getApiPoints } from '@shared/lib/point/point-config-service'
//
const API_URL = 'https://kieai.erweima.ai/api/v1/gpt4o-image/generate'
const AUTH_TOKEN = process.env.AI_AUTH_TOKEN

// 任务类型推断函数
function inferTaskType(
  body: any
): 'text-to-image' | 'image-to-image' | 'ghibli' | 'photo-colorizer' {
  const hasFilesUrl = body.filesUrl && body.filesUrl.length > 0
  const hasPrompt = body.prompt && body.prompt.trim()

  if (!hasFilesUrl && hasPrompt) {
    return 'text-to-image'
  }

  if (hasFilesUrl && hasPrompt) {
    // 检查是否为特定任务类型
    const prompt = body.prompt.toLowerCase()
    if (
      prompt.includes('ghibli') ||
      prompt.includes('宫崎骏') ||
      prompt.includes('吉卜力')
    ) {
      return 'ghibli'
    }
    if (
      prompt.includes('color') ||
      prompt.includes('上色') ||
      prompt.includes('coloring')
    ) {
      return 'photo-colorizer'
    }
    return 'image-to-image'
  }

  // 默认返回image-to-image
  return 'image-to-image'
}

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // 从 cookies 获取用户信息
    const cookieStore = await cookies()
    const user = await getUserFromServerCookies(cookieStore)

    if (!user) {
      return NextResponse.json(
        {
          code: 401000,
          message: {
            en: 'Please login first.',
          },
        },
        { status: 401 }
      )
    }

    // 推断任务类型
    const inferredTaskType = inferTaskType(body)
    console.log('Inferred task type:', inferredTaskType)

    // 验证用户积分
    const validationResult = await PointsService.validateAndConsumePoints(
      user,
      getApiPoints(inferredTaskType, body),
      'Image style conversion'
    )

    if (!validationResult.success) {
      return NextResponse.json(
        {
          code: validationResult.code,
          message: validationResult.message,
        },
        { status: validationResult.code === 401000 ? 401 : 400 }
      )
    }

    // 确保 nVariants 参数是有效的值
    if (body.nVariants && !['1', '2', '4'].includes(body.nVariants)) {
      body.nVariants = '1' // 默认为1
    }

    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify(body),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const data = await response.json()

    // 创建历史记录
    let historyRecord = null
    if (data.code === 200 && data.data?.taskId) {
      try {
        historyRecord = await createHistoryRecordGeneric(
          String(user.id || user.email),
          data.data.taskId,
          inferredTaskType,
          'KieAI/gpt4o-image',
          {
            prompt: body.prompt,
            filesUrl: body.filesUrl || [],
            size: body.size || '1024x1024',
            nVariants: body.nVariants || '1',
            mode: body.mode || 'text-to-image',
            callBackUrl: body.callBackUrl,
          }
        )
        console.log(
          `${inferredTaskType} history record created:`,
          historyRecord?.id
        )
      } catch (historyError) {
        console.error(
          `Failed to create ${inferredTaskType} history record:`,
          historyError
        )
        // 历史记录创建失败，但不中断主流程
      }
    }

    console.log('data', data.code, validationResult.data?.points)

    if (data.code === 200 && validationResult.data?.points !== undefined) {
      // 消耗积分
      const consumeResult = await PointsService.consumePoints(
        user,
        getApiPoints(inferredTaskType, body),
        `${inferredTaskType} conversion points consumption`,
        validationResult.data?.points
      )

      if (!consumeResult.success) {
        console.error('Failed to consume points:', consumeResult.message.en)
        // 即使积分扣除失败，我们仍然返回成功，因为生成任务已经创建
        // 这种情况已经记录日志，后续可以人工处理
      }
    }

    console.log(`${inferredTaskType} generation successful:`, {
      user_id: user.id || user.email,
      task_type: inferredTaskType,
      task_id: data.data?.taskId,
      history_record_id: historyRecord?.id,
      points_consumed: getApiPoints(inferredTaskType, body),
      variants_count: body.nVariants || '1',
    })

    // 返回响应，添加历史记录ID（如果创建成功）
    const responseData = {
      ...data,
      code: data.code === 200 ? 100000 : data.code,
      history_record_id: historyRecord?.id || null,
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Generation request failed:', error)
    return NextResponse.json(
      {
        code: 500000,
        message: {
          en: 'Generation request failed',
        },
      },
      { status: 500 }
    )
  }
}
