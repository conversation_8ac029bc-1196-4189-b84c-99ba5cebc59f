import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getUserFromServerCookies } from '../../../../utils/server-cookies'
import { PointsService } from '../../../../services/points'
import { createHistoryRecordGeneric } from '../../../aiimage/utils/history'
import { getStyleById } from '../../../../[locale]/(marketing)/ai/photo-to-anime/config'
import { getApiPoints } from '@shared/lib/point/point-config-service'

const API_URL = 'https://kieai.erweima.ai/api/v1/gpt4o-image/generate'
const AUTH_TOKEN = process.env.AI_AUTH_TOKEN

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // 验证必需参数
    if (!body.styleId) {
      return NextResponse.json(
        {
          code: 400000,
          message: {
            en: 'Style ID is required.',
          },
        },
        { status: 400 }
      )
    }

    // 从 cookies 获取用户信息
    const cookieStore = await cookies()
    const user = await getUserFromServerCookies(cookieStore)

    if (!user) {
      return NextResponse.json(
        {
          code: 401000,
          message: {
            en: 'Please login first.',
          },
        },
        { status: 401 }
      )
    }

    // 验证用户积分
    const validationResult = await PointsService.validateAndConsumePoints(
      user,
      getApiPoints('photo-to-anime', body),
      'Photo to anime conversion'
    )

    if (!validationResult.success) {
      return NextResponse.json(
        {
          code: validationResult.code,
          message: validationResult.message,
        },
        { status: validationResult.code === 401000 ? 401 : 400 }
      )
    }

    // 根据风格ID获取风格配置
    const style = getStyleById(body.styleId)
    if (!style) {
      return NextResponse.json(
        {
          code: 400000,
          message: {
            en: 'Invalid style ID.',
          },
        },
        { status: 400 }
      )
    }

    // 构建提示词（不区分性别，保持原图性别特征）
    let finalPrompt = style.promptTemplate
    if (body.customPrompt && body.customPrompt.trim()) {
      finalPrompt = `${style.promptTemplate}, ${body.customPrompt.trim()}`
    }
    console.log('Photo to anime style:', style.name)
    console.log('Generated prompt:', finalPrompt)

    // 确保始终只生成一张图片
    body.nVariants = '1'

    // 调用生成API
    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        ...body,
        prompt: finalPrompt,
        enableFallback: true,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const data = await response.json()

    // 创建历史记录
    let historyRecord = null
    if (data.code === 200 && data.data?.taskId) {
      try {
        historyRecord = await createHistoryRecordGeneric(
          String(user.id || user.email),
          data.data.taskId,
          'photo-to-anime',
          'KieAI/photo-to-anime',
          {
            styleId: body.styleId,
            styleName: style.name,
            customPrompt: body.customPrompt,
            finalPrompt: finalPrompt,
            aspectRatio: body.aspectRatio || '1:1',
            nVariants: '1',
            originalImageUrls: body.filesUrl || [],
          }
        )
        console.log('Photo to anime history record created:', historyRecord?.id)
      } catch (historyError) {
        console.error(
          'Failed to create photo-to-anime history record:',
          historyError
        )
        // 历史记录创建失败，但不中断主流程
      }
    }

    // 消耗积分（在任务成功创建后）
    if (data.code === 200 && validationResult.data?.points !== undefined) {
      const consumeResult = await PointsService.consumePoints(
        user,
        getApiPoints('photo-to-anime', body),
        'Photo to anime conversion points consumption',
        validationResult.data?.points
      )

      if (!consumeResult.success) {
        console.error('Failed to consume points:', consumeResult.message.en)
        // 即使积分扣除失败，我们仍然返回成功，因为生成任务已经创建
        // 这种情况已经记录日志，后续可以人工处理
      }
    }

    console.log('Photo to anime generation successful:', {
      user_id: user.id || user.email,
      style_id: body.styleId,
      style_name: style.name,
      task_id: data.data?.taskId,
      history_record_id: historyRecord?.id,
      points_consumed: getApiPoints('photo-to-anime', body),
      variants_count: 1,
    })

    // 返回响应，添加历史记录ID（如果创建成功）
    const responseData = {
      ...data,
      history_record_id: historyRecord?.id || null,
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Photo to anime generation request failed:', error)
    return NextResponse.json(
      {
        code: 500000,
        message: {
          en: 'Photo to anime generation request failed',
        },
      },
      { status: 500 }
    )
  }
}
