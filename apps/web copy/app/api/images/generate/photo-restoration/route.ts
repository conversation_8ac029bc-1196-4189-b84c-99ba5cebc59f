import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getUserFromServerCookies } from '../../../../utils/server-cookies'
import { PointsService } from '../../../../services/points'
import { createHistoryRecordGeneric } from '../../../aiimage/utils/history'
import { getApiPoints } from '@shared/lib/point/point-config-service'

const API_URL = 'https://kieai.erweima.ai/api/v1/gpt4o-image/generate'
const AUTH_TOKEN = process.env.AI_AUTH_TOKEN

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // 从 cookies 获取用户信息
    const cookieStore = await cookies()
    const user = await getUserFromServerCookies(cookieStore)

    if (!user) {
      return NextResponse.json(
        {
          code: 401000,
          message: {
            en: 'Please login first.',
          },
        },
        { status: 401 }
      )
    }

    // 验证用户积分
    const validationResult = await PointsService.validateAndConsumePoints(
      user,
      getApiPoints('photo-restoration', body),
      'Image style conversion'
    )

    if (!validationResult.success) {
      return NextResponse.json(
        {
          code: validationResult.code,
          message: validationResult.message,
        },
        { status: validationResult.code === 401000 ? 401 : 400 }
      )
    }

    // 确保 nVariants 参数是有效的值
    if (body.nVariants && !['1', '2', '4'].includes(body.nVariants)) {
      body.nVariants = '1' // 默认为1
    }

    // 根据用户选择的功能动态生成prompt
    const generateDynamicPrompt = (
      basePrompt: string,
      options: {
        colorize?: boolean
        restoration?: boolean
        removeScratchesAndCracks?: boolean
      }
    ): string => {
      const features: string[] = []
      console.log('options: ', options)
      if (options.restoration) {
        features.push('restore and enhance the image quality')
      }

      if (options.colorize) {
        features.push('add natural colors to black and white photos')
      }

      if (options.removeScratchesAndCracks) {
        features.push('remove scratches, cracks, and other damage')
      }

      if (features.length === 0) {
        return basePrompt
      }

      // Combine base prompt with selected features
      const featuresText = features.join(', ')
      const prompt = `${basePrompt}. Please ${featuresText}. Maintain the original composition and style while improving the overall quality.`
      console.log('Final prompt: ', prompt)
      return prompt
    }

    const basePrompt = body.prompt || 'Restore and enhance this old photo'
    const dynamicPrompt = generateDynamicPrompt(basePrompt, {
      colorize: body.colorize,
      restoration: body.restoration,
      removeScratchesAndCracks: body.removeScratchesAndCracks,
    })

    console.log('Base prompt: ', basePrompt)
    console.log('Generated dynamic prompt: ', dynamicPrompt)
    console.log('Photo restoration options: ', {
      colorize: body.colorize,
      restoration: body.restoration,
      removeScratchesAndCracks: body.removeScratchesAndCracks,
    })

    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        ...body,
        prompt: dynamicPrompt, // 使用动态生成的prompt
        enableFallback: true,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const data = await response.json()

    // 创建历史记录
    let historyRecord = null
    if (data.code === 200 && data.data?.taskId) {
      try {
        historyRecord = await createHistoryRecordGeneric(
          String(user.id || user.email),
          data.data.taskId,
          'photo-restoration',
          'KieAI/photo-restoration',
          {
            colorize: body.colorize,
            restoration: body.restoration,
            removeScratchesAndCracks: body.removeScratchesAndCracks,
            basePrompt: basePrompt,
            dynamicPrompt: dynamicPrompt,
            aspectRatio: body.aspectRatio || '1:1',
            nVariants: body.nVariants || '1',
            originalImageUrls: body.filesUrl || [],
          }
        )
        console.log(
          'Photo restoration history record created:',
          historyRecord?.id
        )
      } catch (historyError) {
        console.error(
          'Failed to create photo-restoration history record:',
          historyError
        )
        // 历史记录创建失败，但不中断主流程
      }
    }

    if (data.code === 200 && validationResult.data?.points !== undefined) {
      // 消耗积分
      const consumeResult = await PointsService.consumePoints(
        user,
        getApiPoints('photo-restoration', body),
        'Photo restoration conversion points consumption',
        validationResult.data?.points
      )

      if (!consumeResult.success) {
        console.error('Failed to consume points:', consumeResult.message.en)
        // 即使积分扣除失败，我们仍然返回成功，因为生成任务已经创建
        // 这种情况已经记录日志，后续可以人工处理
      }
    }

    console.log('Photo restoration generation successful:', {
      user_id: user.id || user.email,
      colorize: body.colorize,
      restoration: body.restoration,
      removeScratchesAndCracks: body.removeScratchesAndCracks,
      task_id: data.data?.taskId,
      history_record_id: historyRecord?.id,
      points_consumed: getApiPoints('photo-restoration', body),
      variants_count: body.nVariants || '1',
    })

    // 返回响应，添加历史记录ID（如果创建成功）
    const responseData = {
      ...data,
      code: data.code === 200 ? 100000 : data.code,
      history_record_id: historyRecord?.id || null,
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Generation request failed:', error)
    return NextResponse.json(
      {
        code: 500000,
        message: {
          en: 'Generation request failed',
        },
      },
      { status: 500 }
    )
  }
}
