import { NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getUserFromServerCookies } from '../../../../utils/server-cookies'
import { PointsService } from '../../../../services/points'
import { createHistoryRecordGeneric } from '../../../aiimage/utils/history'
import { getApiPoints } from '@shared/lib/point/point-config-service'
import { OLD_FILTER_PROMPT } from '../const'

const API_URL = 'https://kieai.erweima.ai/api/v1/gpt4o-image/generate'
const AUTH_TOKEN = process.env.AI_AUTH_TOKEN

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // 从 cookies 获取用户信息
    const cookieStore = await cookies()
    const user = await getUserFromServerCookies(cookieStore)

    if (!user) {
      return NextResponse.json(
        {
          code: 401000,
          message: {
            en: 'Please login first.',
          },
        },
        { status: 401 }
      )
    }

    // 验证用户积分
    const validationResult = await PointsService.validateAndConsumePoints(
      user,
      getApiPoints('old-filter', body),
      'Image style conversion'
    )

    if (!validationResult.success) {
      return NextResponse.json(
        {
          code: validationResult.code,
          message: validationResult.message,
        },
        { status: validationResult.code === 401000 ? 401 : 400 }
      )
    }

    // 确保 nVariants 参数是有效的值
    // if (body.nVariants && !['1', '2', '4'].includes(body.nVariants)) {
    //   body.nVariants = '1' // 默认为1
    // }

    const prompt = OLD_FILTER_PROMPT[body.age as keyof typeof OLD_FILTER_PROMPT]
    console.log('OLD_FILTER_PROMPT: ', prompt)

    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
      body: JSON.stringify({
        ...body,
        prompt,
        enableFallback: true,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const data = await response.json()

    // 创建历史记录
    let historyRecord = null
    if (data.code === 200 && data.data?.taskId) {
      try {
        historyRecord = await createHistoryRecordGeneric(
          String(user.id || user.email),
          data.data.taskId,
          'old-filter',
          'KieAI/old-filter',
          {
            age: body.age,
            prompt: prompt,
            aspectRatio: body.aspectRatio || '1:1',
            nVariants: body.nVariants || '1',
            originalImageUrls: body.filesUrl || [],
          }
        )
        console.log('Old filter history record created:', historyRecord?.id)
      } catch (historyError) {
        console.error(
          'Failed to create old-filter history record:',
          historyError
        )
        // 历史记录创建失败，但不中断主流程
      }
    }

    if (data.code === 200 && validationResult.data?.points !== undefined) {
      // 消耗积分
      const consumeResult = await PointsService.consumePoints(
        user,
        getApiPoints('old-filter', body),
        'Old filter conversion points consumption',
        validationResult.data?.points
      )

      if (!consumeResult.success) {
        console.error('Failed to consume points:', consumeResult.message.en)
        // 即使积分扣除失败，我们仍然返回成功，因为生成任务已经创建
        // 这种情况已经记录日志，后续可以人工处理
      }
    }

    console.log('Old filter generation successful:', {
      user_id: user.id || user.email,
      age: body.age,
      task_id: data.data?.taskId,
      history_record_id: historyRecord?.id,
      points_consumed: getApiPoints('old-filter', body),
      variants_count: body.nVariants || '1',
    })

    // 返回响应，添加历史记录ID（如果创建成功）
    const responseData = {
      ...data,
      code: data.code === 200 ? 100000 : data.code,
      history_record_id: historyRecord?.id || null,
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Generation request failed:', error)
    return NextResponse.json(
      {
        code: 500000,
        message: {
          en: 'Generation request failed',
        },
      },
      { status: 500 }
    )
  }
}
