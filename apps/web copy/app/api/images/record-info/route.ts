import { NextResponse } from 'next/server'
import { syncImageHistoryWithKieAI } from '../../../utils/history-sync'

const API_URL = 'https://kieai.erweima.ai/api/v1/gpt4o-image/record-info'
const AUTH_TOKEN = process.env.AI_AUTH_TOKEN

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const taskId = searchParams.get('taskId')

    if (!taskId) {
      return NextResponse.json(
        { error: 'Task ID is required' },
        { status: 400 }
      )
    }

    console.log('Image status check request:', { taskId })

    const response = await fetch(`${API_URL}?taskId=${taskId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${AUTH_TOKEN}`,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    const data = await response.json()

    console.log('Image status retrieved successfully:', {
      taskId,
      status: data.data?.status,
      successFlag: data.data?.successFlag,
      code: data.code,
    })

    // 使用公共工具函数进行异步历史记录同步（先返回结果，后更新历史记录）
    syncImageHistoryWithKieAI(taskId, data)

    return NextResponse.json(data)
  } catch (error) {
    console.error('Status check failed:', error)
    return NextResponse.json({ error: 'Status check failed' }, { status: 500 })
  }
}
