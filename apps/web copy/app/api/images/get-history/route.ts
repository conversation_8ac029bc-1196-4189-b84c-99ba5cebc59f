// apps/web/app/api/images/get-history/route.ts
import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '12')

  if (!userId) {
    return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
  }

  try {
    // 计算分页
    const from = (page - 1) * limit
    const to = from + limit - 1

    // 计算14天前的日期
    const fourteenDaysAgo = new Date()
    fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14)
    const fourteenDaysAgoStr = fourteenDaysAgo.toISOString()

    // 获取用户的生成记录（过滤掉标记为已删除的记录和超过14天的记录）
    const { data, error, count } = await supabase
      .from('img4o_image_generation_history')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .eq('is_deleted', false) // 只获取未删除的记录
      .gte('created_at', fourteenDaysAgoStr) // 只获取14天内的记录
      .order('created_at', { ascending: false })
      .range(from, to)

    if (error) {
      console.error('Failed to get generation history:', error)
      return NextResponse.json(
        { error: 'Failed to get generation history' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        total: count,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit),
      },
    })
  } catch (error) {
    console.error('Failed to get generation history:', error)
    return NextResponse.json(
      { error: 'Server error, please try again later' },
      { status: 500 }
    )
  }
}
