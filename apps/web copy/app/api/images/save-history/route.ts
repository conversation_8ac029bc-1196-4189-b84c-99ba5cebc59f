// apps/web/app/api/images/save-history/route.ts
import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const {
      originalImageUrl,
      generatedImageUrl, // 接收生成的图片URL(可能是单个URL或多个URL的字符串)
      prompt,
      ratio,
      userId,
      extraData,
    } = await req.json()

    // 将生成记录保存到数据库
    const { data, error } = await supabase
      .from('img4o_image_generation_history')
      .insert({
        user_id: userId,
        original_image_url: originalImageUrl, // 可能包含多个URL，用 | 分隔
        generated_image_url: generatedImageUrl, // 可能包含多个URL，用 | 分隔
        prompt,
        ratio,
        created_at: new Date().toISOString(),
        extra_data: extraData,
        is_deleted: false,
      })
      .select()

    if (error) {
      console.error('保存生成记录失败:', error)
      return NextResponse.json({ error: '保存生成记录失败' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: data[0],
    })
  } catch (error) {
    console.error('保存生成记录失败:', error)
    return NextResponse.json(
      { error: '服务器错误，请稍后再试' },
      { status: 500 }
    )
  }
}
