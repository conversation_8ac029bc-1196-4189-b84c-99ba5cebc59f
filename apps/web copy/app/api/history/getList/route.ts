import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const {
      userId,
      page = 1,
      pageSize = 20,
      taskType,
      status,
      startDate,
      endDate,
      sortBy = 'created_at',
      sortOrder = 'desc',
      includeDeleted = false,
    } = await req.json()

    // 验证必填字段
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // 验证分页参数
    const validPage = Math.max(1, parseInt(page))
    const validPageSize = Math.min(100, Math.max(1, parseInt(pageSize)))
    const offset = (validPage - 1) * validPageSize

    // 构建查询
    let query = supabase
      .from('img4o_history')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)

    // 是否包含已删除的记录
    if (!includeDeleted) {
      query = query.is('deleted_at', null)
    }

    // 按任务类型筛选
    if (taskType) {
      query = query.eq('task_type', taskType)
    }

    // 按状态筛选
    if (status) {
      if (Array.isArray(status)) {
        query = query.in('status', status)
      } else {
        query = query.eq('status', status)
      }
    }

    // 按时间范围筛选
    if (startDate) {
      query = query.gte('created_at', startDate)
    }
    if (endDate) {
      query = query.lte('created_at', endDate)
    }

    // 排序
    const validSortBy = [
      'created_at',
      'updated_at',
      'completed_at',
      'status',
      'task_type',
    ]
    const validSortOrder = ['asc', 'desc']

    const finalSortBy = validSortBy.includes(sortBy) ? sortBy : 'created_at'
    const finalSortOrder = validSortOrder.includes(sortOrder)
      ? sortOrder
      : 'desc'

    query = query.order(finalSortBy, { ascending: finalSortOrder === 'asc' })

    // 分页
    query = query.range(offset, offset + validPageSize - 1)

    const { data, error, count } = await query

    if (error) {
      console.error('Failed to get history list:', error)
      return NextResponse.json(
        { error: 'Failed to get history list' },
        { status: 500 }
      )
    }

    // 计算分页信息
    const totalPages = count ? Math.ceil(count / validPageSize) : 0
    const hasMore = validPage < totalPages

    return NextResponse.json({
      success: true,
      data: data || [],
      pagination: {
        page: validPage,
        pageSize: validPageSize,
        total: count || 0,
        totalPages,
        hasMore,
      },
    })
  } catch (error) {
    console.error('Failed to get history list:', error)
    return NextResponse.json(
      { error: 'Server error, please try again later' },
      { status: 500 }
    )
  }
}
