import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const {
      userId,
      taskType,
      externalTaskId,
      status = 'PENDING',
      inputParams,
      resultData,
      metadata,
      errorMessage,
      completedAt,
    } = await req.json()

    // 验证必填字段
    if (!userId || !taskType) {
      return NextResponse.json(
        { error: 'User ID and task type are required' },
        { status: 400 }
      )
    }

    // 插入新的历史记录
    const { data, error } = await supabase
      .from('img4o_history')
      .insert({
        user_id: userId,
        task_type: taskType,
        external_task_id: externalTaskId,
        status,
        input_params: inputParams,
        result_data: resultData,
        metadata,
        error_message: errorMessage,
        completed_at: completedAt,
      })
      .select()

    if (error) {
      console.error('Failed to create history record:', error)
      return NextResponse.json(
        { error: 'Failed to create history record' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'History record created successfully',
      data: data[0],
    })
  } catch (error) {
    console.error('Failed to create history record:', error)
    return NextResponse.json(
      { error: 'Server error, please try again later' },
      { status: 500 }
    )
  }
}
