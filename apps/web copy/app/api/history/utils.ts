/**
 * 历史记录API工具函数
 */

/**
 * 将ID转换为有效的正整数
 * 支持数字和字符串类型的输入
 * @param id - 要转换的ID（可以是数字或字符串）
 * @returns 转换后的正整数
 * @throws Error - 如果ID无效
 */
export function parseValidId(id: any): number {
  // 处理null、undefined、空字符串等情况
  if (id === null || id === undefined || id === '') {
    throw new Error('ID is required')
  }

  // 转换为数字
  const numericId = parseInt(id, 10)

  // 验证是否为有效的正整数
  if (isNaN(numericId) || numericId <= 0 || !Number.isInteger(numericId)) {
    throw new Error(`Invalid ID: "${id}". ID must be a valid positive integer.`)
  }

  return numericId
}

/**
 * 批量转换ID数组
 * @param ids - ID或ID数组
 * @returns 转换后的数字ID数组
 * @throws Error - 如果任何ID无效
 */
export function parseValidIds(ids: any): number[] {
  if (!ids) {
    throw new Error('IDs are required')
  }

  // 确保是数组格式
  const idsArray = Array.isArray(ids) ? ids : [ids]

  // 批量转换
  return idsArray.map((id, index) => {
    try {
      return parseValidId(id)
    } catch (error) {
      throw new Error(
        `Invalid ID at position ${index}: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      )
    }
  })
}

/**
 * 验证用户ID
 * @param userId - 用户ID
 * @throws Error - 如果用户ID无效
 */
export function validateUserId(userId: any): void {
  if (!userId || (typeof userId === 'string' && userId.trim() === '')) {
    throw new Error('User ID is required')
  }
}
