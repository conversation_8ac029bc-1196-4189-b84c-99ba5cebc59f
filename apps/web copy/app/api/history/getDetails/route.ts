import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const { id, userId, includeDeleted = false } = await req.json()

    // 验证必填字段
    if (!id || !userId) {
      return NextResponse.json(
        { error: 'ID and user ID are required' },
        { status: 400 }
      )
    }

    // 将id转换为数字类型（兼容字符串和数字）
    const numericId = parseInt(id, 10)
    if (isNaN(numericId) || numericId <= 0) {
      return NextResponse.json(
        { error: 'ID must be a valid positive number' },
        { status: 400 }
      )
    }

    // 构建查询
    let query = supabase
      .from('img4o_history')
      .select('*')
      .eq('id', numericId)
      .eq('user_id', userId)

    // 是否包含已删除的记录
    if (!includeDeleted) {
      query = query.is('deleted_at', null)
    }

    const { data, error } = await query.single()

    if (error) {
      if (error.code === 'PGRST116') {
        // 记录不存在
        return NextResponse.json(
          { error: 'Record not found or no permission to access' },
          { status: 404 }
        )
      }
      console.error('Failed to get history details:', error)
      return NextResponse.json(
        { error: 'Failed to get history details' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data,
    })
  } catch (error) {
    console.error('Failed to get history details:', error)
    return NextResponse.json(
      { error: 'Server error, please try again later' },
      { status: 500 }
    )
  }
}

// 也支持GET方法（从URL参数获取）
export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url)

    const id = searchParams.get('id')
    const userId = searchParams.get('userId')
    const includeDeleted = searchParams.get('includeDeleted') === 'true'

    if (!id || !userId) {
      return NextResponse.json(
        { error: 'ID and user ID are required' },
        { status: 400 }
      )
    }

    // 构建请求体并调用POST方法的逻辑
    const requestBody = {
      id,
      userId,
      includeDeleted,
    }

    // 创建新的Request对象
    const postRequest = new Request(req.url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody),
    })

    return await POST(postRequest)
  } catch (error) {
    console.error('Failed to get history details:', error)
    return NextResponse.json(
      { error: 'Server error, please try again later' },
      { status: 500 }
    )
  }
}
