import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getUserFromServerCookies } from '../../../../utils/server-cookies'
import { supabase } from '@/lib/supabaseClient'
import {
  queryKlingTryOnTask,
  mapKlingErrorToHttpStatus,
  mapKlingStatusToHistoryFormat,
} from '../../util'

// 查找Kling虚拟试穿历史记录
async function findKlingTryOnHistory(taskId: string, userId: string) {
  try {
    const { data, error } = await supabase
      .from('img4o_history')
      .select('*')
      .eq('external_task_id', taskId)
      .eq('task_type', 'ai_try_on')
      .eq('user_id', userId)
      .is('deleted_at', null)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw error
    }

    return data || null
  } catch (error) {
    console.error('查找Kling虚拟试穿历史记录失败:', error)
    return null
  }
}

// 更新历史记录状态
async function updateKlingTryOnHistory(recordId: number, klingData: any) {
  try {
    const updateData: any = {
      status: mapKlingStatusToHistoryFormat(klingData.task_status),
      result_data: {
        task_id: klingData.task_id,
        task_status: klingData.task_status,
        task_status_msg: klingData.task_status_msg,
        task_result: klingData.task_result,
        imageUrl: klingData.task_result?.images?.[0]?.url,
      },
      metadata: {
        kling_response: klingData,
        updated_at: new Date().toISOString(),
      },
      updated_at: new Date().toISOString(),
    }

    // 如果任务完成或失败，设置完成时间
    if (
      klingData.task_status === 'succeed' ||
      klingData.task_status === 'failed'
    ) {
      updateData.completed_at = new Date().toISOString()
    }

    // 如果有错误信息，设置error_message
    if (klingData.task_status_msg && klingData.task_status === 'failed') {
      updateData.error_message = klingData.task_status_msg
    }

    const { data, error } = await supabase
      .from('img4o_history')
      .update(updateData)
      .eq('id', recordId)
      .select()

    if (error) {
      throw error
    }

    return data?.[0]
  } catch (error) {
    console.error('更新Kling虚拟试穿历史记录失败:', error)
    throw error
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: taskId } = await params

    // 验证任务ID参数
    if (!taskId || typeof taskId !== 'string') {
      return NextResponse.json(
        {
          code: 400,
          data: null,
          message: '无效的任务ID',
        },
        { status: 400 }
      )
    }

    // 获取用户信息进行权限验证
    const cookieStore = await cookies()
    const user = await getUserFromServerCookies(cookieStore)

    if (!user) {
      return NextResponse.json(
        {
          code: 401,
          data: null,
          message: '需要登录',
        },
        { status: 401 }
      )
    }

    // 查找本地历史记录
    const historyRecord = await findKlingTryOnHistory(
      taskId,
      String(user.id || user.email)
    )

    // 调用Kling API获取最新状态
    let klingResponse
    try {
      klingResponse = await queryKlingTryOnTask(taskId)
    } catch (klingError) {
      console.error('Kling API查询失败:', klingError)

      // 如果有本地历史记录，返回本地数据
      if (historyRecord) {
        const taskDetail = {
          task_id: historyRecord.external_task_id,
          status: historyRecord.status?.toLowerCase() || 'unknown',
          output: {
            imageUrl: historyRecord.result_data?.images?.[0]?.url,
          },
          meta: {
            created_at: new Date(historyRecord.created_at).getTime(),
            updated_at: new Date(historyRecord.updated_at).getTime(),
            model:
              historyRecord.input_params?.model_name ||
              'kolors-virtual-try-on-v1',
            task_type: 'ai_try_on',
            source: 'local_history',
          },
          error: historyRecord.error_message
            ? {
                message: historyRecord.error_message,
              }
            : null,
          input: {
            human_image:
              historyRecord.input_params?.model_input ||
              historyRecord.input_params?.human_image,
            cloth_image:
              historyRecord.input_params?.dress_input ||
              historyRecord.input_params?.cloth_image,
            model_name: historyRecord.input_params?.model_name,
          },
        }

        return NextResponse.json({
          code: 200,
          data: taskDetail,
          message: 'success',
        })
      }

      // 没有本地记录且API调用失败
      return NextResponse.json(
        {
          code: 404,
          data: null,
          message: '任务不存在',
        },
        { status: 404 }
      )
    }

    // 检查Kling API响应
    if (klingResponse.code !== 0) {
      const httpStatus = mapKlingErrorToHttpStatus(klingResponse.code)

      // 如果是权限问题且有本地记录，返回本地数据
      if (httpStatus === 403 && historyRecord) {
        const taskDetail = {
          task_id: historyRecord.external_task_id,
          status: historyRecord.status?.toLowerCase() || 'unknown',
          output: {
            imageUrl: historyRecord.result_data?.images?.[0]?.url,
          },
          meta: {
            created_at: new Date(historyRecord.created_at).getTime(),
            updated_at: new Date(historyRecord.updated_at).getTime(),
            model:
              historyRecord.input_params?.model_name ||
              'kolors-virtual-try-on-v1',
            task_type: 'ai_try_on',
            source: 'local_history',
          },
          error: historyRecord.error_message
            ? {
                message: historyRecord.error_message,
              }
            : null,
          input: {
            human_image:
              historyRecord.input_params?.model_input ||
              historyRecord.input_params?.human_image,
            cloth_image:
              historyRecord.input_params?.dress_input ||
              historyRecord.input_params?.cloth_image,
            model_name: historyRecord.input_params?.model_name,
          },
        }

        return NextResponse.json({
          code: 200,
          data: taskDetail,
          message: 'success',
        })
      }

      return NextResponse.json(
        {
          code: httpStatus,
          data: null,
          message: klingResponse.message || 'Kling API查询失败',
        },
        { status: httpStatus }
      )
    }

    // 如果有本地历史记录且状态有变化，更新本地记录
    if (historyRecord) {
      const localStatus = mapKlingStatusToHistoryFormat(
        klingResponse.data.task_status
      )
      if (true) {
        try {
          await updateKlingTryOnHistory(historyRecord.id, {
            ...klingResponse.data,
            task_type: 'ai_try_on',
          })
          console.log('本地历史记录已更新:', {
            taskId,
            oldStatus: historyRecord.status,
            newStatus: localStatus,
          })
        } catch (updateError) {
          console.error('更新本地历史记录失败，但不影响查询结果:', updateError)
        }
      }
    } else {
      console.warn('未找到本地历史记录，任务可能是通过其他方式创建的:', taskId)
    }

    // 构建符合项目标准的任务详情响应
    const taskDetail = {
      task_id: klingResponse.data.task_id,
      status: klingResponse.data.task_status,
      output: {
        imageUrl: klingResponse.data.task_result?.images?.[0]?.url,
      },
      meta: {
        created_at: klingResponse.data.created_at,
        updated_at: klingResponse.data.updated_at,
        model:
          historyRecord?.input_params?.model_name || 'kolors-virtual-try-on-v1',
        task_type: 'ai_try_on',
        source: 'kling_api',
      },
      error:
        klingResponse.data.task_status === 'failed'
          ? {
              message: klingResponse.data.task_status_msg || 'Task failed',
            }
          : null,
      input: historyRecord
        ? {
            human_image:
              historyRecord.input_params?.model_input ||
              historyRecord.input_params?.human_image,
            cloth_image:
              historyRecord.input_params?.dress_input ||
              historyRecord.input_params?.cloth_image,
            model_name: historyRecord.input_params?.model_name,
          }
        : null,
    }

    // 返回符合PiAPI规范的响应格式
    return NextResponse.json({
      code: 200,
      data: taskDetail,
      message: 'success',
    })
  } catch (error) {
    console.error('Kling虚拟试穿任务查询失败:', error)
    return NextResponse.json(
      {
        code: 500,
        data: null,
        message: '服务器内部错误',
      },
      { status: 500 }
    )
  }
}

// 支持CORS预检请求
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
