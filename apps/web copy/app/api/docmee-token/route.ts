// apps/web/app/api/docmee-token/route.ts
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // 从环境变量获取API密钥
    const apiKey = process.env.DOCMEE_API_KEY
    //
    console.log('faith=============apiKey-apiKey', apiKey)

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key is not configured' },
        { status: 500 }
      )
    }

    // 从请求中获取用户ID，如果没有则使用默认值
    // 您可以根据您的应用程序逻辑修改这部分
    const { uid = process.env.DOCMEE_DEFAULT_UID || 'anonymous-user' } =
      await request.json().catch(() => ({}))

    // 如果没有提供uid且没有默认值，返回错误
    if (!uid) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // 准备请求体
    const requestBody = {
      uid: uid,
      limit: 100, // 根据您的需求设置token使用限制
    }

    // 调用Docmee的API生成token
    const response = await fetch('https://docmee.cn/api/user/createApiToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Api-Key': apiKey,
      },
      body: JSON.stringify(requestBody),
    })

    // 获取响应数据
    const responseData = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        {
          error: 'Failed to generate token',
          details: responseData,
        },
        { status: response.status }
      )
    }

    // 返回生成的token
    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Error generating Docmee token:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

// 添加GET方法以便从客户端更容易获取token
export async function GET() {
  try {
    // 从环境变量获取API密钥和默认用户ID
    const apiKey = process.env.DOCMEE_API_KEY
    const defaultUid = process.env.DOCMEE_DEFAULT_UID

    if (!apiKey) {
      return NextResponse.json(
        { error: 'API key is not configured' },
        { status: 500 }
      )
    }

    if (!defaultUid) {
      return NextResponse.json(
        { error: 'Default user ID is not configured' },
        { status: 500 }
      )
    }

    // 准备请求体
    const requestBody = {
      uid: defaultUid,
      limit: 10, // 根据您的需求设置token使用限制
    }

    // 调用Docmee的API生成token
    const response = await fetch('https://docmee.cn/api/user/createApiToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Api-Key': apiKey,
      },
      body: JSON.stringify(requestBody),
    })

    // 获取响应数据
    const responseData = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        {
          error: 'Failed to generate token',
          details: responseData,
        },
        { status: response.status }
      )
    }

    // 返回生成的token
    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Error generating Docmee token:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
