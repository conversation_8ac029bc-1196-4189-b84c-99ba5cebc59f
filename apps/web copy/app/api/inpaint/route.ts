import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

type AIProvider = 'iopaint';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const provider = formData.get('provider') as AIProvider;
    const image = formData.get('image') as File;
    const mask = formData.get('mask') as File;
    const prompt = formData.get('prompt') as string;

    if (!provider || !image || !mask) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Validate provider - only IOPaint is supported
    if (provider !== 'iopaint') {
      return NextResponse.json(
        { success: false, error: `Unsupported provider: ${provider}. Only IOPaint is supported.` },
        { status: 400 }
      );
    }

    // Convert files to base64 for IOPaint
    const imageBuffer = await image.arrayBuffer();
    const maskBuffer = await mask.arrayBuffer();

    // Get IOPaint server URL from environment variable or formData as fallback
    const baseUrl = process.env.IOPAINT_BASE_URL || formData.get('baseUrl') as string || 'https://faith1314666-imggen-magic-wand.hf.space';

    try {
      // IOPaint expects JSON with base64 encoded images (without data URL prefix)
      const imageBase64 = Buffer.from(imageBuffer).toString('base64');
      const maskBase64 = Buffer.from(maskBuffer).toString('base64');

      // Debug: Log image info
      console.log('Image size:', imageBuffer.byteLength, 'bytes');
      console.log('Mask size:', maskBuffer.byteLength, 'bytes');
      console.log('Image base64 length:', imageBase64.length);
      console.log('Mask base64 length:', maskBase64.length);

      // Server-side secure configuration for IOPaint
      const requestBody = {
        image: imageBase64,
        mask: maskBase64,
        // Secure server-side constants - not exposed to client
        ldm_steps: 20,
        ldm_sampler: 'plms',
        hd_strategy: 'Crop',
        hd_strategy_crop_trigger_size: 800,
        hd_strategy_crop_margin: 128,
        hd_strategy_resize_limit: 1280,
        use_croper: false,
        croper_x: 0,
        croper_y: 0,
        croper_height: 512, // Will be overridden by actual image dimensions
        croper_width: 512,  // Will be overridden by actual image dimensions
        use_extender: false,
        extender_x: 0,
        extender_y: 0,
        extender_height: 512,
        extender_width: 512,
        sd_mask_blur: 12,
        sd_strength: 1.0,
        sd_steps: 50,
        sd_guidance_scale: 7.5,
        sd_sampler: "DPM++ 2M",
        sd_seed: -1,
        sd_match_histograms: false,
        sd_lcm_lora: false,
        enable_controlnet: false,
        controlnet_conditioning_scale: 0.4,
        controlnet_method: "",
        enable_brushnet: false,
        brushnet_method: "",
        brushnet_conditioning_scale: 1.0,
        enable_powerpaint_v2: false,
        powerpaint_task: "object-remove"
      };

      const response = await fetch(`${baseUrl}/api/v1/inpaint`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const contentType = response.headers.get('content-type');
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        if (contentType?.includes('application/json')) {
          try {
            const errorData = await response.json();
            // Handle IOPaint specific error format
            if (errorData.error && errorData.errors) {
              errorMessage = `${errorData.error}: ${errorData.errors}`;

              // Check for common IOPaint errors and provide helpful messages
              if (errorData.errors.includes('Padding size should be less than')) {
                errorMessage = 'Image is too small for IOPaint. Please use an image that is at least 64x64 pixels.';
              } else if (errorData.errors.includes('cannot identify image file')) {
                errorMessage = 'Invalid image format. Please use a valid PNG or JPEG image.';
              }
            } else {
              errorMessage = errorData.error || errorData.message || errorMessage;
            }
          } catch {
            errorMessage = await response.text();
          }
        } else {
          errorMessage = await response.text();
        }

        return NextResponse.json(
          { success: false, error: `IOPaint: ${errorMessage}` },
          { status: response.status }
        );
      }

      const contentType = response.headers.get('content-type');
      let result;

      // IOPaint API returns JSON or image depending on the endpoint
      if (contentType?.includes('application/json')) {
        // Parse JSON response
        const jsonResponse = await response.json();

        // Check if it contains base64 image data
        if (jsonResponse && typeof jsonResponse === 'string') {
          // Response is base64 image data
          result = {
            success: true,
            imageUrl: `data:image/png;base64,${jsonResponse}`
          };
        } else if (jsonResponse.image) {
          // Response contains image field
          result = {
            success: true,
            imageUrl: `data:image/png;base64,${jsonResponse.image}`
          };
        } else {
          // Unexpected JSON format
          return NextResponse.json(
            { success: false, error: `IOPaint returned unexpected JSON format: ${JSON.stringify(jsonResponse).substring(0, 200)}...` },
            { status: 500 }
          );
        }
      } else if (contentType?.includes('image/')) {
        // Direct image response
        const blob = await response.blob();
        const buffer = await blob.arrayBuffer();
        const base64 = Buffer.from(buffer).toString('base64');
        result = {
          success: true,
          imageUrl: `data:image/png;base64,${base64}`
        };
      } else {
        // Unexpected content type
        const responseText = await response.text();
        return NextResponse.json(
          { success: false, error: `IOPaint returned unexpected content type: ${contentType}. Response: ${responseText.substring(0, 200)}...` },
          { status: 500 }
        );
      }

      return NextResponse.json(result);

    } catch (error) {
      return NextResponse.json(
        { success: false, error: `IOPaint API request failed: ${error instanceof Error ? error.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('API route error:', error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
