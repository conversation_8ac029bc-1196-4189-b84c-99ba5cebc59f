// 重构后的模块化导出文件
// 将原有的大文件拆分为多个功能模块，提高可维护性

// 导入验证函数模块
export {
  validateImageUrl,
  validateBase64Image,
  validateImageInput,
  validateFaceSwapInput,
  validateVirtualTryOnInput,
  validateHugVideoInput,
  validateMemoryVideoInput,
} from './utils/validators'

// 导入状态映射模块
export {
  mapHistoryStatusToPiApiFormat,
  mapPiApiStatusToHistoryFormat,
} from './utils/status-mappers'

// 导入历史记录管理模块
export { createHistoryRecordGeneric } from './utils/history'

// 导入接口定义模块
export type { ITaskProcessor } from './utils/interfaces'

// 导入任务处理器模块
export { FaceSwapTaskProcessor } from './utils/processors/face-swap'
export { VirtualTryOnTaskProcessor } from './utils/processors/virtual-try-on'
export { HugVideoTaskProcessor } from './utils/processors/hug-video'
export { MemoryVideoTaskProcessor } from './utils/processors/memory-video'
export { TaskProcessorFactory } from './utils/processors/factory'
