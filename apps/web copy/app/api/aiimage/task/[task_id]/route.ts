import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getUserFromServerCookies } from '../../../../utils/server-cookies'
import { supabase } from '@/lib/supabaseClient'
import { updateHistoryRecord } from '../../../history/update/route'
import {
  TaskProcessorFactory,
  mapPiApiStatusToHistoryFormat,
} from '../../utils'

// PiAPI配置
const PIAPI_BASE_URL = process.env.NEXT_PUBLIC_PIAPI_URL + '/api/v1'
const PIAPI_KEY = process.env.NEXT_PUBLIC_PIAPI_KEY || ''

// 调用PiAPI获取任务详情
async function fetchTaskFromPiAPI(taskId: string): Promise<any> {
  try {
    const response = await fetch(`${PIAPI_BASE_URL}/task/${taskId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${PIAPI_KEY}`,
      },
    })

    if (!response.ok) {
      throw new Error(
        `PiAPI request failed: ${response.status} ${response.statusText}`
      )
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Failed to fetch task from PiAPI:', error)
    throw error
  }
}

// 查找历史记录（支持多任务类型）
async function findHistoryRecord(taskId: string, userId: string) {
  // 查询所有支持的任务类型
  const supportedTaskTypes = TaskProcessorFactory.getSupportedTaskTypes()

  let historyRecord = null
  let taskType = null

  console.log('supportedTaskTypes', supportedTaskTypes)
  console.log('taskId', taskId)
  console.log('userId', userId)

  // 先查询upscale任务
  const { data: upscaleData, error: upscaleError } = await supabase
    .from('img4o_history')
    .select('*')
    .eq('external_task_id', taskId)
    .eq('task_type', 'upscale')
    .eq('user_id', userId)
    .is('deleted_at', null)
    .single()

  console.log('upscaleData', upscaleData)
  console.log('upscaleError', upscaleError)

  // 依次尝试每种任务类型
  for (const type of supportedTaskTypes) {
    const { data, error } = await supabase
      .from('img4o_history')
      .select('*')
      .eq('external_task_id', taskId)
      .eq('task_type', type)
      .eq('user_id', userId)
      .is('deleted_at', null)
      .single()

    if (!error && data) {
      historyRecord = data
      taskType = type
      break
    }
  }

  return { historyRecord, taskType }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { task_id: string } }
) {
  try {
    const { task_id } = await params

    // 验证task_id参数
    if (!task_id || typeof task_id !== 'string') {
      return NextResponse.json(
        {
          code: 400,
          data: null,
          message: 'Invalid task ID',
        },
        { status: 400 }
      )
    }

    // 获取用户信息进行权限验证
    const cookieStore = await cookies()
    const user = await getUserFromServerCookies(cookieStore)

    if (!user) {
      return NextResponse.json(
        {
          code: 401,
          data: null,
          message: 'Authentication required',
        },
        { status: 401 }
      )
    }

    // 从历史记录表查询任务信息（支持所有任务类型）
    const { historyRecord, taskType } = await findHistoryRecord(
      task_id,
      String(user.id || user.email)
    )

    // 检查权限：确保用户只能查询自己的任务
    if (!historyRecord) {
      // 记录不存在，尝试直接调用PiAPI（可能是用户直接调用PiAPI创建的任务）
      try {
        const piApiResponse = await fetchTaskFromPiAPI(task_id)

        // 返回PiAPI的原始响应，但添加权限警告
        return NextResponse.json({
          code: 200,
          data: piApiResponse.data || piApiResponse,
          message: 'Task found in PiAPI but not in local history',
        })
      } catch (piApiError) {
        // PiAPI也找不到，任务确实不存在
        return NextResponse.json(
          {
            code: 404,
            data: null,
            message: 'Task not found',
          },
          { status: 404 }
        )
      }
    }

    // 使用对应的任务处理器构建任务详情
    const processor = TaskProcessorFactory.getProcessor(taskType!)
    let taskDetail = processor.buildTaskDetailFromHistory(historyRecord)

    console.log('taskDetail', taskDetail)

    // 如果任务还在进行中，尝试从PiAPI获取最新状态
    if (taskDetail.status === 'pending' || taskDetail.status === 'processing') {
      try {
        const piApiResponse = await fetchTaskFromPiAPI(task_id)

        if (piApiResponse && piApiResponse.data) {
          if (piApiResponse.data.status !== taskDetail.status) {
            // 合并PiAPI的最新数据
            taskDetail = {
              ...taskDetail,
              status: piApiResponse.data.status,
              output: piApiResponse.data.output || taskDetail.output,
              meta: {
                ...taskDetail.meta,
                ...piApiResponse.data.meta,
              },
              error: piApiResponse.data.error || taskDetail.error,
            }

            console.log('Task status updated in PiAPI:', {
              taskId: task_id,
              taskType: taskType,
              localStatus: taskDetail.status,
              piApiStatus: piApiResponse.data.status,
            })

            const data = {
              id: historyRecord.id,
              userId: historyRecord.user_id,
              status: mapPiApiStatusToHistoryFormat(piApiResponse.data.status),
              resultData: piApiResponse.data.output,
              metadata: piApiResponse.data.meta
                ? {
                    ...historyRecord.metadata,
                    ...piApiResponse.data.meta,
                  }
                : undefined,
              errorMessage: piApiResponse.data.error?.message || undefined,
            }

            // 异步执行更新，不阻塞主请求
            updateHistoryRecord(data).catch((error: any) => {
              console.error('Failed to update local history record:', error)
            })
          }
        }
      } catch (piApiError) {
        // PiAPI调用失败，使用本地数据
        console.warn(
          'Failed to fetch latest status from PiAPI, using local data:',
          piApiError
        )
      }
    }

    // 返回符合PiAPI规范的响应
    return NextResponse.json({
      code: 200,
      data: taskDetail,
      message: 'success',
    })
  } catch (error) {
    console.error('Get task detail error:', error)
    return NextResponse.json(
      {
        code: 500,
        data: null,
        message: 'Internal server error',
      },
      { status: 500 }
    )
  }
}

// 健康检查方法
export async function OPTIONS() {
  const supportedTaskTypes = TaskProcessorFactory.getSupportedTaskTypes()
  const taskTypeInfo = TaskProcessorFactory.getAllTaskTypeInfo()

  return new NextResponse(
    JSON.stringify({
      message: 'Multi-task type task detail endpoint is active',
      supported_task_types: supportedTaskTypes,
      task_info: taskTypeInfo,
      version: '2.0.0',
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
      },
    }
  )
}
