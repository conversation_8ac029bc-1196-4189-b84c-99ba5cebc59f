// 状态映射：History表状态 → PiAPI状态
export function mapHistoryStatusToPiApiFormat(historyStatus: string): string {
  switch (historyStatus) {
    case 'PENDING':
      return 'pending'
    case 'PROCESSING':
      return 'processing'
    case 'SUCCESS':
      return 'completed'
    case 'FAILED':
      return 'failed'
    default:
      return 'pending'
  }
}

// 状态映射：PiAPI状态 → History表状态
export function mapPiApiStatusToHistoryFormat(piApiStatus: string): string {
  switch (piApiStatus) {
    case 'pending':
      return 'PENDING'
    case 'processing':
      return 'PROCESSING'
    case 'completed':
      return 'SUCCESS'
    case 'failed':
      return 'FAILED'
    default:
      return 'PENDING'
  }
}
