import { supabase } from '@/lib/supabaseClient'

// 通用历史记录创建函数
export async function createHistoryRecordGeneric(
  userId: string,
  taskId: string,
  taskType: string,
  model: string,
  inputParams: any
): Promise<any> {
  try {
    const { data, error } = await supabase
      .from('img4o_history')
      .insert({
        user_id: userId,
        task_type: taskType,
        external_task_id: taskId,
        status: 'PENDING',
        input_params: inputParams,
        result_data: null,
        metadata: {
          model: model,
          task_type: taskType,
          created_via: 'api',
        },
        created_at: new Date().toISOString(),
      })
      .select()

    if (error) {
      console.error('Failed to create history record:', error)
      throw error
    }

    return data?.[0]
  } catch (error) {
    console.error('Error creating history record:', error)
    throw error
  }
}
