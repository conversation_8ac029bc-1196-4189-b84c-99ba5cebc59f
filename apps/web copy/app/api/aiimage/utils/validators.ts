// 验证图片URL格式
export function validateImageUrl(url: string): boolean {
  if (!url || typeof url !== 'string') return false

  // 检查是否是有效的URL
  try {
    new URL(url)
  } catch {
    return false
  }

  // 检查是否是支持的图片格式
  const supportedFormats = ['.jpg', '.jpeg', '.png', '.webp']
  const urlLower = url.toLowerCase()
  return supportedFormats.some((format) => urlLower.includes(format))
}

// 验证base64图片格式
export function validateBase64Image(base64: string): boolean {
  if (!base64 || typeof base64 !== 'string') return false

  // 检查base64格式
  const base64Regex = /^data:image\/(jpeg|jpg|png|webp);base64,/
  return base64Regex.test(base64)
}

// 验证图片输入（URL或base64）
export function validateImageInput(imageInput: string): boolean {
  if (!imageInput) return false

  // 如果是base64格式
  if (imageInput.startsWith('data:image/')) {
    return validateBase64Image(imageInput)
  }

  // 否则当作URL处理
  return validateImageUrl(imageInput)
}

// 验证Face Swap输入参数
export function validateFaceSwapInput(input: any): {
  isValid: boolean
  error?: string
} {
  const { swap_image, target_image } = input

  // 验证必需的图片参数
  if (!swap_image || !target_image) {
    return {
      isValid: false,
      error: 'Both swap_image and target_image are required for face-swap',
    }
  }

  // 验证图片格式
  if (!validateImageInput(swap_image)) {
    return {
      isValid: false,
      error:
        'Invalid swap_image format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  if (!validateImageInput(target_image)) {
    return {
      isValid: false,
      error:
        'Invalid target_image format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  return { isValid: true }
}

// 验证Virtual Try-On输入参数
export function validateVirtualTryOnInput(input: any): {
  isValid: boolean
  error?: string
} {
  // 验证model_input必需参数
  if (!input.model_input) {
    return {
      isValid: false,
      error: 'model_input is required for Virtual Try-On',
    }
  }

  if (!validateImageInput(input.model_input)) {
    return {
      isValid: false,
      error:
        'Invalid model_input format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  // 验证服装输入互斥逻辑
  const hasDressInput = !!input.dress_input
  const hasUpperInput = !!input.upper_input
  const hasLowerInput = !!input.lower_input

  if (hasDressInput && (hasUpperInput || hasLowerInput)) {
    return {
      isValid: false,
      error:
        'Cannot use dress_input together with upper_input or lower_input. Use either dress_input OR upper_input/lower_input combination',
    }
  }

  if (!hasDressInput && !hasUpperInput && !hasLowerInput) {
    return {
      isValid: false,
      error: 'Must provide either dress_input OR upper_input/lower_input',
    }
  }

  // 验证服装图片格式
  if (hasDressInput && !validateImageInput(input.dress_input)) {
    return {
      isValid: false,
      error:
        'Invalid dress_input format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  if (hasUpperInput && !validateImageInput(input.upper_input)) {
    return {
      isValid: false,
      error:
        'Invalid upper_input format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  if (hasLowerInput && !validateImageInput(input.lower_input)) {
    return {
      isValid: false,
      error:
        'Invalid lower_input format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  // 验证batch_size
  const batchSize = input.batch_size || 1
  if (!Number.isInteger(batchSize) || batchSize < 1 || batchSize > 4) {
    return {
      isValid: false,
      error: 'batch_size must be an integer between 1 and 4',
    }
  }

  return { isValid: true }
}

// 验证AI Hug视频生成输入参数
export function validateHugVideoInput(input: any): {
  isValid: boolean
  error?: string
} {
  const { image_url } = input

  // 验证必需的图片参数
  if (!image_url) {
    return {
      isValid: false,
      error: 'image_url is required for AI Hug video generation',
    }
  }

  // 验证图片格式
  if (!validateImageInput(image_url)) {
    return {
      isValid: false,
      error:
        'Invalid image_url format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  return { isValid: true }
}

// 验证图片放大输入参数
export function validateUpscaleInput(input: any): {
  isValid: boolean
  error?: string
} {
  const { image, scale, face_enhance } = input

  // 验证必需的图片参数
  if (!image) {
    return {
      isValid: false,
      error: 'image is required for image upscaling',
    }
  }

  // 验证图片格式
  if (!validateImageInput(image)) {
    return {
      isValid: false,
      error:
        'Invalid image format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  // 验证scale参数
  if (scale !== undefined) {
    const validScales = [2, 4, 8]
    if (!validScales.includes(Number(scale))) {
      return {
        isValid: false,
        error: 'Scale must be 2, 4, or 8',
      }
    }
  }

  // 验证face_enhance参数（可选）
  if (face_enhance !== undefined && typeof face_enhance !== 'boolean') {
    return {
      isValid: false,
      error: 'face_enhance must be a boolean value',
    }
  }

  return { isValid: true }
}

// 验证背景移除输入参数
export function validateBackgroundRemoveInput(input: any): {
  isValid: boolean
  error?: string
} {
  const { image } = input

  // 验证必需的图片参数
  if (!image) {
    return {
      isValid: false,
      error: 'image is required for background removal',
    }
  }

  // 验证图片格式
  if (!validateImageInput(image)) {
    return {
      isValid: false,
      error:
        'Invalid image format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  return { isValid: true }
}

// 验证Memory Video输入参数
export function validateMemoryVideoInput(input: any): {
  isValid: boolean
  error?: string
} {
  const { image_url } = input

  // 验证必需的图片参数
  if (!image_url) {
    return {
      isValid: false,
      error: 'image_url is required for Memory Video generation',
    }
  }

  // 验证图片格式
  if (!validateImageInput(image_url)) {
    return {
      isValid: false,
      error:
        'Invalid image_url format. Must be a valid URL or base64 image (jpg/jpeg/png/webp)',
    }
  }

  return { isValid: true }
}
