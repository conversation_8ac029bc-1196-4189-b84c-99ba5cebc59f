import { supabase } from '@/lib/supabaseClient'
import { validateHugVideoInput } from '../validators'
import {
  mapHistoryStatusToPiApiFormat,
  mapPiApiStatusToHistoryFormat,
} from '../status-mappers'
import { createHistoryRecordGeneric } from '../history'
import { getApiPoints } from '@shared/lib/point/point-config-service'

// AI Hug视频任务处理器
export class HugVideoTaskProcessor {
  static validateInput(input: any) {
    return validateHugVideoInput(input)
  }

  static getPointsConfig() {
    return {
      pointsConfig: getApiPoints('ai_hug'),
      pointsDescription: 'AI Hug video generation',
      batchMultiplier: 1,
    }
  }

  static buildPiApiRequestBody(
    input: any,
    webhookEndpoint: string,
    webhookSecret: string
  ) {
    return {
      model: 'Qubico/hug-video',
      task_type: 'image_to_video',
      input: {
        image_url: input.image_url,
      },
      config: {
        webhook_config: {
          endpoint: webhookEndpoint,
          secret: webhookSecret,
        },
        service_mode: 'public',
      },
    }
  }

  static async createHistoryRecord(userId: string, taskId: string, input: any) {
    return createHistoryRecordGeneric(
      userId,
      taskId,
      'ai_hug',
      'Qubico/hug-video',
      {
        image_url: input.image_url,
      }
    )
  }

  static getTaskTypeInfo() {
    return {
      taskType: 'ai_hug',
      model: 'Qubico/hug-video',
      displayName: 'AI Hug Video',
    }
  }

  static buildTaskDetailFromHistory(historyRecord: any) {
    const piApiStatus = mapHistoryStatusToPiApiFormat(historyRecord.status)

    return {
      task_id: historyRecord.external_task_id,
      model: 'Qubico/hug-video',
      task_type: 'image_to_video',
      status: piApiStatus,
      input: historyRecord.input_params || {},
      output: historyRecord.result_data?.raw_output || {},
      meta: {
        created_at: historyRecord.created_at,
        started_at: historyRecord.metadata?.started_at || null,
        ended_at: historyRecord.completed_at || null,
        usage: historyRecord.metadata?.usage || null,
        is_using_private_pool: false,
      },
      detail: null,
      logs: [],
      error: {
        code: historyRecord.result_data?.error_code || 0,
        message: historyRecord.error_message || '',
      },
    }
  }

  static validateWebhookData(webhookData: any): boolean {
    // 验证是否为ai hug任务
    return (
      webhookData.model === 'Qubico/hug-video' &&
      webhookData.task_type === 'image_to_video'
    )
  }

  static async processWebhookUpdate(webhookData: any) {
    try {
      // 查找现有记录
      const { data: existingRecord, error: findError } = await supabase
        .from('img4o_history')
        .select('*')
        .eq('external_task_id', webhookData.task_id)
        .eq('task_type', 'ai_hug')
        .is('deleted_at', null)
        .single()

      if (findError && findError.code !== 'PGRST116') {
        throw findError
      }

      if (!existingRecord) {
        console.warn(
          'No existing history record found for ai hug task_id:',
          webhookData.task_id
        )
        return null
      }

      // 构建更新数据
      const mappedStatus = mapPiApiStatusToHistoryFormat(webhookData.status)

      const updateData: any = {
        status: mappedStatus,
        input_params: {
          image_url:
            webhookData.input?.image_url ||
            existingRecord.input_params?.image_url,
        },
        result_data: {
          result_url: webhookData.output?.result_url || null,
          status: webhookData.status,
          error_message: webhookData.error?.message || null,
          error_code: webhookData.error?.code || null,
          raw_output: webhookData.output || null,
        },
        metadata: {
          ...existingRecord.metadata,
          piapi_response: webhookData,
          model: webhookData.model,
          created_at: webhookData.meta?.created_at,
          started_at: webhookData.meta?.started_at,
          ended_at: webhookData.meta?.ended_at,
          usage: webhookData.meta?.usage,
        },
        updated_at: new Date().toISOString(),
      }

      // 如果任务完成或失败，设置完成时间
      if (mappedStatus === 'SUCCESS' || mappedStatus === 'FAILED') {
        updateData.completed_at =
          webhookData.meta?.ended_at || new Date().toISOString()
      }

      // 如果有错误信息，设置error_message字段
      if (webhookData.error?.message) {
        updateData.error_message = webhookData.error.message
      }

      // 更新记录
      const { data, error } = await supabase
        .from('img4o_history')
        .update(updateData)
        .eq('id', existingRecord.id)
        .select()

      if (error) {
        throw error
      }

      return data?.[0]
    } catch (error) {
      console.error('Failed to process ai hug webhook update:', error)
      throw error
    }
  }
}
