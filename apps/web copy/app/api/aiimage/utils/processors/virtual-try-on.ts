import { supabase } from '@/lib/supabaseClient'
import { validateVirtualTryOnInput } from '../validators'
import {
  mapHistoryStatusToPiApiFormat,
  mapPiApiStatusToHistoryFormat,
} from '../status-mappers'
import { createHistoryRecordGeneric } from '../history'
import { getApiPoints } from '@shared/lib/point/point-config-service'

// Virtual Try-On任务处理器
export class VirtualTryOnTaskProcessor {
  static validateInput(input: any) {
    return validateVirtualTryOnInput(input)
  }

  static getPointsConfig(input: any) {
    return {
      pointsConfig: getApiPoints('ai_try_on', input),
      pointsDescription: 'Virtual Try-On generation',
      batchMultiplier: input.batch_size || 1,
    }
  }

  static buildPiApiRequestBody(
    input: any,
    webhookEndpoint: string,
    webhookSecret: string
  ) {
    return {
      model: 'kling',
      task_type: 'ai_try_on',
      input: {
        model_input: input.model_input,
        ...(input.dress_input && { dress_input: input.dress_input }),
        ...(input.upper_input && { upper_input: input.upper_input }),
        ...(input.lower_input && { lower_input: input.lower_input }),
        batch_size: input.batch_size || 1,
      },
      config: {
        webhook_config: {
          endpoint: webhookEndpoint,
          secret: webhookSecret,
        },
        service_mode: 'public',
      },
    }
  }

  static async createHistoryRecord(userId: string, taskId: string, input: any) {
    return createHistoryRecordGeneric(
      userId,
      taskId,
      'ai_try_on',
      'kling',
      input
    )
  }

  static getTaskTypeInfo() {
    return {
      taskType: 'ai_try_on',
      model: 'kling',
      displayName: 'Virtual Try-On',
    }
  }

  /**
   * 
   * @param historyRecord {
  "code": 200,
  "data": {
    "task_id": "cdba9704-5e1c-47aa-9340-9fa7f63182e8",
    "model": "kling",
    "task_type": "ai_try_on",
    "status": "completed",
    "config": {
      "service_mode": "",
      "webhook_config": {
        "endpoint": "",
        "secret": ""
      }
    },
    "input": {
      "batch_size": 1,
      "dress_input": "https://piapi.ai/workspace/try-on/dress.png",
      "model_input": "https://piapi.ai/workspace/try-on/input.png"
    },
    "output": {
      "type": "mmu_img2img_aitryon",
      "status": 99,
      "works": [
        {
          "content_type": "image",
          "status": 99,
          "type": "mmu_img2img_aitryon",
          "image": {
            "resource": "https://s21-kling.klingai.com/kimg/EMXN1y8qbQoGdXBsb2FkEg55bGFiLXN0dW50LXNncBpTc2UvYWlfcG9ydGFsX3NncF9tbXVfaW1nMmltZ19haXRyeW9uL2E3YWI2ZjhhLTY5ZGEtNDgxNC1iZTkzLWEwYmRiM2I5MDAxOV9pbWFnZS5wbmc.origin?x-kcdn-pid=112372",
            "resource_without_watermark": "https://storage.theapi.app/images/283396684320078.png",
            "height": 1456,
            "width": 816,
            "duration": 0
          }
        }
      ]
    },
    "meta": {
      "created_at": "2025-07-02T02:59:31.548012332Z",
      "started_at": "2025-07-02T02:59:31.873896997Z",
      "ended_at": "2025-07-02T03:00:28.463938072Z",
      "usage": {
        "type": "point",
        "frozen": 70000,
        "consume": 70000
      },
      "is_using_private_pool": false
    },
    "detail": null,
    "logs": [],
    "error": {
      "code": 0,
      "raw_message": "",
      "message": "",
      "detail": null
    }
  },
  "message": "success"
}
   * @returns 
   */
  static buildTaskDetailFromHistory(historyRecord: any) {
    const piApiStatus = mapHistoryStatusToPiApiFormat(historyRecord.status)

    // 解析输出数据，支持新格式
    const rawOutput = historyRecord.result_data?.raw_output || {}
    let processedOutput = { ...rawOutput }

    // 如果存在新格式的works数组，提取主要图片URL用于向后兼容
    if (
      rawOutput.works &&
      Array.isArray(rawOutput.works) &&
      rawOutput.works.length > 0
    ) {
      const firstWork = rawOutput.works[0]
      if (firstWork?.image?.resource_without_watermark) {
        processedOutput.image_url = firstWork.image.resource_without_watermark
        processedOutput.image_with_watermark = firstWork.image.resource
        processedOutput.image_width = firstWork.image.width
        processedOutput.image_height = firstWork.image.height
      }
    }
    // 向后兼容：如果已经有image_url字段（旧格式），保持不变
    else if (historyRecord.result_data?.result_url) {
      processedOutput.image_url = historyRecord.result_data.result_url
    }

    return {
      task_id: historyRecord.external_task_id,
      model: 'kling',
      task_type: 'ai_try_on',
      status: piApiStatus,
      input: historyRecord.input_params || {},
      output: processedOutput,
      meta: {
        created_at: historyRecord.created_at,
        started_at: historyRecord.metadata?.started_at || null,
        ended_at: historyRecord.completed_at || null,
        usage: historyRecord.metadata?.usage || null,
        is_using_private_pool: false,
        batch_size: historyRecord.input_params?.batch_size || 1,
      },
      detail: null,
      logs: [],
      error: {
        code: historyRecord.result_data?.error_code || 0,
        message: historyRecord.error_message || '',
      },
    }
  }

  static validateWebhookData(webhookData: any): boolean {
    // 验证是否为virtual try-on任务
    return (
      webhookData.model === 'kling' && webhookData.task_type === 'ai_try_on'
    )
  }

  static async processWebhookUpdate(webhookData: any) {
    try {
      // 查找现有记录
      const { data: existingRecord, error: findError } = await supabase
        .from('img4o_history')
        .select('*')
        .eq('external_task_id', webhookData.task_id)
        .eq('task_type', 'ai_try_on')
        .is('deleted_at', null)
        .single()

      if (findError && findError.code !== 'PGRST116') {
        throw findError
      }

      if (!existingRecord) {
        console.warn(
          'No existing history record found for virtual try-on task_id:',
          webhookData.task_id
        )
        return null
      }

      // 构建更新数据
      const mappedStatus = mapPiApiStatusToHistoryFormat(webhookData.status)

      const updateData: any = {
        status: mappedStatus,
        input_params: {
          model_input:
            webhookData.input?.model_input ||
            existingRecord.input_params?.model_input,
          dress_input:
            webhookData.input?.dress_input ||
            existingRecord.input_params?.dress_input,
          upper_input:
            webhookData.input?.upper_input ||
            existingRecord.input_params?.upper_input,
          lower_input:
            webhookData.input?.lower_input ||
            existingRecord.input_params?.lower_input,
          batch_size:
            webhookData.input?.batch_size ||
            existingRecord.input_params?.batch_size ||
            1,
        },
        result_data: {
          result_url: webhookData.output?.result_url || null,
          result_urls: webhookData.output?.result_urls || null, // 支持多图片输出
          status: webhookData.status,
          error_message: webhookData.error?.message || null,
          error_code: webhookData.error?.code || null,
          raw_output: webhookData.output || null,
        },
        metadata: {
          ...existingRecord.metadata,
          piapi_response: webhookData,
          model: webhookData.model,
          created_at: webhookData.meta?.created_at,
          started_at: webhookData.meta?.started_at,
          ended_at: webhookData.meta?.ended_at,
          usage: webhookData.meta?.usage,
          batch_size: webhookData.input?.batch_size || 1,
        },
        updated_at: new Date().toISOString(),
      }

      // 如果任务完成或失败，设置完成时间
      if (mappedStatus === 'SUCCESS' || mappedStatus === 'FAILED') {
        updateData.completed_at =
          webhookData.meta?.ended_at || new Date().toISOString()
      }

      // 如果有错误信息，设置error_message字段
      if (webhookData.error?.message) {
        updateData.error_message = webhookData.error.message
      }

      // 更新记录
      const { data, error } = await supabase
        .from('img4o_history')
        .update(updateData)
        .eq('id', existingRecord.id)
        .select()

      if (error) {
        throw error
      }

      return data?.[0]
    } catch (error) {
      console.error('Failed to process virtual try-on webhook update:', error)
      throw error
    }
  }
}
