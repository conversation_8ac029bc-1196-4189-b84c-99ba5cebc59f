import { supabase } from '@/lib/supabaseClient'
import { validateBackgroundRemoveInput } from '../validators'
import {
  mapHistoryStatusToPiApiFormat,
  mapPiApiStatusToHistoryFormat,
} from '../status-mappers'
import { createHistoryRecordGeneric } from '../history'
import { getApiPoints } from '@shared/lib/point/point-config-service'

// Background Remove
export class BackgroundRemoveTaskProcessor {
  static validateInput(input: any) {
    return validateBackgroundRemoveInput(input)
  }

  static getPointsConfig() {
    return {
      pointsConfig: getApiPoints('background-remove'),
      pointsDescription: 'Background removal',
      batchMultiplier: 1,
    }
  }

  static buildPiApiRequestBody(
    input: any,
    webhookEndpoint: string,
    webhookSecret: string
  ) {
    const requestBody: any = {
      model: 'Qubico/image-toolkit',
      task_type: 'background-remove',
      input: {
        image: input.image,
      },
      config: {
        webhook_config: {
          endpoint: webhookEndpoint,
          secret: webhookSecret,
        },
        service_mode: 'public',
      },
    }

    return requestBody
  }

  static async createHistoryRecord(userId: string, taskId: string, input: any) {
    return createHistoryRecordGeneric(
      userId,
      taskId,
      'background-remove',
      'Qubico/image-toolkit',
      {
        image: input.image,
      }
    )
  }

  static getTaskTypeInfo() {
    return {
      taskType: 'background-remove',
      model: 'Qubico/image-toolkit',
      displayName: 'Background Removal',
    }
  }

  static buildTaskDetailFromHistory(historyRecord: any) {
    const piApiStatus = mapHistoryStatusToPiApiFormat(historyRecord.status)

    return {
      task_id: historyRecord.external_task_id,
      model: 'Qubico/image-toolkit',
      task_type: 'background-remove',
      status: piApiStatus,
      input: historyRecord.input_params || {},
      output: historyRecord.result_data?.raw_output || {},
      meta: {
        created_at: historyRecord.created_at,
        started_at: historyRecord.metadata?.started_at || null,
        ended_at: historyRecord.completed_at || null,
        usage: historyRecord.metadata?.usage || null,
        is_using_private_pool: false,
      },
      detail: null,
      logs: [],
      error: {
        code: historyRecord.result_data?.error_code || 0,
        message: historyRecord.error_message || '',
      },
    }
  }

  static validateWebhookData(webhookData: any): boolean {
    return (
      webhookData.model === 'Qubico/image-toolkit' &&
      webhookData.task_type === 'background-remove'
    )
  }

  static async processWebhookUpdate(webhookData: any) {
    try {
      const { data: existingRecord, error: findError } = await supabase
        .from('img4o_history')
        .select('*')
        .eq('external_task_id', webhookData.task_id)
        .eq('task_type', 'background-remove')
        .is('deleted_at', null)
        .single()

      if (findError && findError.code !== 'PGRST116') {
        throw findError
      }

      if (!existingRecord) {
        console.warn(
          'No existing history record found for background-remove task_id:',
          webhookData.task_id
        )
        return null
      }

      const mappedStatus = mapPiApiStatusToHistoryFormat(webhookData.status)

      const updateData: any = {
        status: mappedStatus,
        input_params: {
          image: webhookData.input?.image || existingRecord.input_params?.image,
        },
        result_data: {
          result_url: webhookData.output?.image_url || null,
          status: webhookData.status,
          error_message: webhookData.error?.message || null,
          error_code: webhookData.error?.code || null,
          raw_output: webhookData.output || null,
        },
        metadata: {
          ...existingRecord.metadata,
          piapi_response: webhookData,
          model: webhookData.model,
          created_at: webhookData.meta?.created_at,
          started_at: webhookData.meta?.started_at,
          ended_at: webhookData.meta?.ended_at,
          usage: webhookData.meta?.usage,
        },
        updated_at: new Date().toISOString(),
      }

      if (mappedStatus === 'SUCCESS' || mappedStatus === 'FAILED') {
        updateData.completed_at =
          webhookData.meta?.ended_at || new Date().toISOString()
      }

      if (webhookData.error?.message) {
        updateData.error_message = webhookData.error.message
      }

      const { data, error } = await supabase
        .from('img4o_history')
        .update(updateData)
        .eq('id', existingRecord.id)
        .select()

      if (error) {
        throw error
      }

      return data?.[0]
    } catch (error) {
      console.error(
        'Failed to process background-remove webhook update:',
        error
      )
      throw error
    }
  }
}
