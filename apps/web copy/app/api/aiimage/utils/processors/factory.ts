import { FaceSwapTaskProcessor } from './face-swap'
import { VirtualTryOnTaskProcessor } from './virtual-try-on'
import { HugVideoTaskProcessor } from './hug-video'
import { MemoryVideoTaskProcessor } from './memory-video'
import { UpscaleTaskProcessor } from './upscale'
import { BackgroundRemoveTaskProcessor } from './background-remove'

// 任务处理器工厂
export class TaskProcessorFactory {
  static getProcessor(taskType: string) {
    switch (taskType) {
      case 'face-swap':
        return FaceSwapTaskProcessor
      case 'ai_try_on':
        return VirtualTryOnTaskProcessor
      case 'ai_hug':
        return HugVideoTaskProcessor
      case 'memory_video':
        return MemoryVideoTaskProcessor
      case 'upscale':
        return UpscaleTaskProcessor
      case 'background-remove':
        return BackgroundRemoveTaskProcessor
      default:
        throw new Error(`Unsupported task type: ${taskType}`)
    }
  }

  static getSupportedTaskTypes(): string[] {
    return [
      'face-swap',
      'ai_try_on',
      'ai_hug',
      'memory_video',
      'upscale',
      'background-remove',
    ]
  }

  static getProcessorByWebhookData(webhookData: any) {
    // 根据webhook数据自动识别任务类型
    const supportedTypes = this.getSupportedTaskTypes()

    for (const taskType of supportedTypes) {
      const processor = this.getProcessor(taskType)
      if (processor.validateWebhookData(webhookData)) {
        return { processor, taskType }
      }
    }

    throw new Error(
      `No processor found for webhook data: model=${webhookData.model}, task_type=${webhookData.task_type}`
    )
  }

  static getAllTaskTypeInfo() {
    return this.getSupportedTaskTypes().map((taskType) => {
      const processor = this.getProcessor(taskType)
      return processor.getTaskTypeInfo()
    })
  }
}
