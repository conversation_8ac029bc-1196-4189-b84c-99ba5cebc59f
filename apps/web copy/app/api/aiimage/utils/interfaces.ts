// 任务处理器接口
export interface ITaskProcessor {
  validateInput(input: any): { isValid: boolean; error?: string }
  getPointsConfig(input?: any): {
    pointsConfig: number
    pointsDescription: string
    batchMultiplier: number
  }
  buildPiApiRequestBody(
    input: any,
    webhookEndpoint: string,
    webhookSecret: string
  ): any
  createHistoryRecord(userId: string, taskId: string, input: any): Promise<any>
  getTaskTypeInfo(): {
    taskType: string
    model: string
    displayName: string
  }
  buildTaskDetailFromHistory(historyRecord: any): any
  validateWebhookData(webhookData: any): boolean
  processWebhookUpdate(webhookData: any): Promise<any>
}
