import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { getUserFromServerCookies } from '../../../utils/server-cookies'
import { PointsService } from '../../../services/points'
import { TaskProcessorFactory } from '../utils'
import { getApiPoints } from '@shared/lib/point/point-config-service'

// PiAPI配置
const PIAPI_BASE_URL = process.env.NEXT_PUBLIC_PIAPI_URL + '/api/v1'
const PIAPI_KEY = process.env.NEXT_PUBLIC_PIAPI_KEY || ''
const WEBHOOK_ENDPOINT = process.env.NEXT_PUBLIC_WEBHOOK_BASE_URL
  ? `${process.env.NEXT_PUBLIC_WEBHOOK_BASE_URL}/api/aiimage/webhook`
  : 'https://www.imggen.org/api/aiimage/webhook' // 生产环境需要配置正确的域名
const WEBHOOK_SECRET = process.env.PIAPI_WEBHOOK_SECRET || ''

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // 验证请求体结构
    if (!body.task_type || !body.input) {
      return NextResponse.json(
        {
          code: 400,
          data: null,
          msg: 'Invalid request body: missing required fields (task_type, input)',
        },
        { status: 400 }
      )
    }

    const { task_type, input } = body

    // 验证支持的任务类型
    const supportedTaskTypes = TaskProcessorFactory.getSupportedTaskTypes()
    if (!supportedTaskTypes.includes(task_type)) {
      return NextResponse.json(
        {
          code: 400,
          data: null,
          msg: `Unsupported task_type. Supported types: ${supportedTaskTypes.join(
            ', '
          )}`,
        },
        { status: 400 }
      )
    }

    // 获取任务处理器
    const taskProcessor = TaskProcessorFactory.getProcessor(task_type)

    // 验证输入参数
    const validation = taskProcessor.validateInput(input)
    if (!validation.isValid) {
      return NextResponse.json(
        {
          code: 400,
          data: null,
          msg: validation.error,
        },
        { status: 400 }
      )
    }

    // 获取用户信息进行认证
    const cookieStore = await cookies()
    const user = await getUserFromServerCookies(cookieStore)

    if (!user) {
      return NextResponse.json(
        {
          code: 401,
          data: null,
          msg: 'Authentication required',
        },
        { status: 401 }
      )
    }

    // 获取积分配置
    const { pointsConfig, pointsDescription, batchMultiplier } =
      taskProcessor.getPointsConfig(input)
    const totalPoints = pointsConfig * batchMultiplier

    // 验证用户积分
    const validationResult = await PointsService.validateAndConsumePoints(
      user,
      totalPoints,
      pointsDescription
    )

    if (!validationResult.success) {
      return NextResponse.json(
        {
          code: validationResult.code === 401000 ? 401 : 402,
          data: null,
          msg: validationResult.message.en || 'Insufficient credits',
        },
        { status: validationResult.code === 401000 ? 401 : 402 }
      )
    }

    // 构建PiAPI请求体
    const piApiRequestBody = taskProcessor.buildPiApiRequestBody(
      input,
      WEBHOOK_ENDPOINT,
      WEBHOOK_SECRET
    )

    console.log('piApiRequestBody', piApiRequestBody)

    // 调用PiAPI创建任务
    const piApiResponse = await fetch(`${PIAPI_BASE_URL}/task`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'x-api-key': PIAPI_KEY,
      },
      body: JSON.stringify(piApiRequestBody),
    })

    if (!piApiResponse.ok) {
      const errorText = await piApiResponse.text()
      console.error('PiAPI request failed:', {
        status: piApiResponse.status,
        statusText: piApiResponse.statusText,
        response: errorText,
      })

      throw new Error(
        `PiAPI request failed: ${piApiResponse.status} ${piApiResponse.statusText}`
      )
    }

    const piApiData = await piApiResponse.json()

    // 验证PiAPI响应
    if (!piApiData.data?.task_id) {
      console.error('Invalid PiAPI response:', piApiData)
      throw new Error('Invalid response from PiAPI: missing task_id')
    }

    const taskId = piApiData.data.task_id

    // 创建本地历史记录
    let historyRecord = null
    try {
      historyRecord = await taskProcessor.createHistoryRecord(
        String(user.id || user.email),
        taskId,
        input
      )
    } catch (historyError) {
      console.error('Failed to create history record:', historyError)
      // 历史记录创建失败，但PiAPI任务已创建，记录错误但不中断流程
    }

    // 消耗积分（在任务成功创建后）
    if (validationResult.data?.points !== undefined) {
      const consumeResult = await PointsService.consumePoints(
        user,
        getApiPoints(task_type, input),
        `${pointsDescription} points consumption`,
        validationResult.data?.points
      )

      if (!consumeResult.success) {
        console.error('Failed to consume points:', consumeResult.message.en)
        // 即使积分扣除失败，我们仍然返回成功，因为生成任务已经创建
        // 这种情况已经记录日志，后续可以人工处理
      }
    }

    // 构建响应（完全符合PiAPI规范）
    const response = {
      code: 200,
      data: {
        task_id: taskId,
        // model: piApiData.data.model || piApiRequestBody.model,
        task_type: piApiData.data.task_type || piApiRequestBody.task_type,
        status: piApiData.data.status || 'pending',
        input: piApiData.data.input || piApiRequestBody.input,
        output: piApiData.data.output || null,
        meta: piApiData.data.meta || {},
        detail: piApiData.data.detail || null,
        logs: piApiData.data.logs || [],
        error: piApiData.data.error || {
          code: 0,
          message: '',
        },
        // 添加本地扩展字段
        history_record_id: historyRecord?.id || null,
      },
      msg: piApiData.message || 'success',
    }

    console.log(`${task_type} task created successfully:`, {
      task_id: taskId,
      user_id: user.id || user.email,
      history_record_id: historyRecord?.id,
      points_consumed: totalPoints,
      batch_size: task_type === 'ai_try_on' ? input.batch_size || 1 : 1,
    })

    return NextResponse.json(response)
  } catch (error) {
    console.error('Generation error:', error)

    return NextResponse.json(
      {
        code: 500,
        data: null,
        msg: error instanceof Error ? error.message : 'Internal server error',
      },
      { status: 500 }
    )
  }
}

// 健康检查方法
export async function GET() {
  const supportedTaskTypes = TaskProcessorFactory.getSupportedTaskTypes()

  return NextResponse.json({
    message: `PiAPI generation endpoint is active (supports ${supportedTaskTypes.join(
      ' and '
    )})`,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    supported_task_types: supportedTaskTypes,
  })
}

// 支持OPTIONS方法用于CORS预检
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
