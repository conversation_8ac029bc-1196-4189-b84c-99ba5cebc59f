import { NextRequest, NextResponse } from 'next/server'
import { TaskProcessorFactory } from '../utils'

// Webhook安全配置
const WEBHOOK_SECRET = process.env.PIAPI_WEBHOOK_SECRET || ''
const MAX_TIMESTAMP_DIFF = 5 * 60 * 1000 // 5分钟时间窗口

// 存储已处理的webhook请求，用于去重
const processedWebhooks = new Set<string>()

interface WebhookRequest {
  timestamp: number
  data: {
    task_id: string
    model: string
    task_type: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    config?: {
      webhook_config?: {
        endpoint: string
        secret: string
      }
    }
    input?: {
      [key: string]: any // 支持多种输入格式
    }
    output?: {
      result_url?: string
      result_urls?: string[] // 支持批量输出
      [key: string]: any
    }
    meta?: {
      created_at: string
      started_at?: string
      ended_at?: string
      usage?: any
    }
    error?: {
      code: number
      message: string
    }
    logs?: any[]
  }
}

// 验证webhook签名
function verifyWebhookSignature(
  secret: string,
  providedSecret?: string
): boolean {
  if (!WEBHOOK_SECRET) {
    console.warn(
      'PIAPI_WEBHOOK_SECRET not configured, skipping signature verification'
    )
    return true // 在开发环境中允许跳过验证
  }

  return secret === providedSecret
}

// 验证时间戳，防止重放攻击
function verifyTimestamp(timestamp: number): boolean {
  const now = Date.now()
  const requestTime = timestamp * 1000 // 转换为毫秒
  const timeDiff = Math.abs(now - requestTime)

  return timeDiff <= MAX_TIMESTAMP_DIFF
}

// 生成请求唯一标识符，用于去重
function generateRequestId(timestamp: number, taskId: string): string {
  return `${timestamp}_${taskId}`
}

// 处理webhook通知（支持多任务类型）
async function processWebhookNotification(webhookData: WebhookRequest['data']) {
  try {
    // 使用工厂模式自动识别任务类型并获取对应的处理器
    const { processor, taskType } =
      TaskProcessorFactory.getProcessorByWebhookData(webhookData)

    console.log(`Processing webhook for task type: ${taskType}`, {
      task_id: webhookData.task_id,
      model: webhookData.model,
      status: webhookData.status,
    })

    // 使用对应的处理器更新历史记录
    await processor.processWebhookUpdate(webhookData)

    // 这里可以添加其他处理逻辑，比如：
    // 1. 发送实时通知给前端（WebSocket/SSE）
    // 2. 发送邮件或推送通知
    // 3. 触发后续处理流程
    // 4. 更新用户积分或使用记录

    console.log('Webhook notification processed successfully:', {
      task_id: webhookData.task_id,
      task_type: taskType,
      status: webhookData.status,
      timestamp: new Date().toISOString(),
    })

    return { success: true, taskType }
  } catch (error) {
    // 如果是不支持的任务类型，记录警告但不抛出错误
    if (
      error instanceof Error &&
      error.message.includes('No processor found')
    ) {
      console.warn('Received webhook for unsupported task type:', {
        model: webhookData.model,
        task_type: webhookData.task_type,
        task_id: webhookData.task_id,
        error: error.message,
      })
      return {
        success: false,
        reason: 'unsupported_task_type',
        error: error.message,
      }
    }

    // 其他错误继续抛出
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    // 解析请求体
    const body: WebhookRequest = await request.json()

    console.log('WebhookRequest', body)

    if (!body.timestamp || !body.data) {
      return NextResponse.json(
        { error: 'Invalid webhook payload: missing timestamp or data' },
        { status: 400 }
      )
    }

    const { timestamp, data } = body
    const webhookSecret = request.headers.get('x-webhook-secret')

    // 验证webhook签名
    if (!verifyWebhookSignature(WEBHOOK_SECRET, webhookSecret || undefined)) {
      console.warn('Webhook signature verification failed')
      return NextResponse.json(
        { error: 'Invalid webhook signature' },
        { status: 401 }
      )
    }

    // 验证时间戳，防止重放攻击
    if (!verifyTimestamp(timestamp)) {
      console.warn('Webhook timestamp verification failed:', {
        timestamp,
        currentTime: Math.floor(Date.now() / 1000),
      })
      return NextResponse.json(
        { error: 'Invalid timestamp: request too old or too new' },
        { status: 400 }
      )
    }

    // 生成请求ID用于去重
    const requestId = generateRequestId(timestamp, data.task_id)

    // 检查是否已处理过此请求
    if (processedWebhooks.has(requestId)) {
      console.log('Duplicate webhook request detected, skipping:', requestId)
      return NextResponse.json({
        success: true,
        message: 'Webhook already processed',
      })
    }

    // 标记为已处理
    processedWebhooks.add(requestId)

    // 处理webhook通知（自动识别任务类型）
    const result = await processWebhookNotification(data)

    if (!result.success) {
      if (result.reason === 'unsupported_task_type') {
        return NextResponse.json({
          success: true,
          message: `Unsupported task type ignored: ${data.model}/${data.task_type}`,
          warning: result.error,
        })
      }
    }

    // 清理旧的已处理请求记录（保留最近1小时的记录）
    const oneHourAgo = Date.now() - 60 * 60 * 1000
    const idsToDelete: string[] = []
    processedWebhooks.forEach((id) => {
      const [reqTimestamp] = id.split('_')
      if (parseInt(reqTimestamp) * 1000 < oneHourAgo) {
        idsToDelete.push(id)
      }
    })
    idsToDelete.forEach((id) => processedWebhooks.delete(id))

    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully',
      task_type: result.taskType,
    })
  } catch (error) {
    console.error('Multi-task webhook error:', error)

    // 返回500错误，触发PiAPI重试机制
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

// 健康检查端点
export async function GET() {
  const supportedTaskTypes = TaskProcessorFactory.getSupportedTaskTypes()
  const taskTypeInfo = TaskProcessorFactory.getAllTaskTypeInfo()

  return NextResponse.json({
    message: 'Multi-task type webhook endpoint is active',
    status: 'healthy',
    supported_task_types: supportedTaskTypes,
    task_info: taskTypeInfo,
    timestamp: new Date().toISOString(),
    version: '2.0.0',
  })
}

// 支持OPTIONS方法用于CORS预检
export async function OPTIONS() {
  const supportedTaskTypes = TaskProcessorFactory.getSupportedTaskTypes()

  return new NextResponse(
    JSON.stringify({
      message: 'Multi-task type webhook endpoint CORS preflight',
      supported_task_types: supportedTaskTypes,
      version: '2.0.0',
    }),
    {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, x-webhook-secret',
        'Access-Control-Max-Age': '86400',
      },
    }
  )
}
