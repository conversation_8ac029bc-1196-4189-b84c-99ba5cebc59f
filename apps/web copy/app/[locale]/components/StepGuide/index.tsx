import { Link } from '@i18n/routing'
import React from 'react'
import { getTranslations } from 'next-intl/server'

// 通用步骤组件，可用于展示任何类型的操作流程
const StepGuide = async ({ title, subtitle, steps, actionButton }) => {
  const t = await getTranslations()
  return (
    <section className="py-16 px-4 bg-gradient-to-b from-[#E2ECFC] to-[#EEF2FF]">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-3 bg-gradient-to-r from-[#4B6BFB] to-[#2F4BFB] bg-clip-text text-transparent">
            {title ||
              'How to Make a PowerPoint With Our AI IMAGE Generator in 3 Easy Steps?'}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {subtitle ||
              'From Topic to Slides in Seconds -- Powered by the Best AI Presentation Maker'}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow duration-300 border border-[#4B6BFB]/10 flex flex-col items-center text-center"
            >
              <div className="flex items-center justify-center w-16 h-16 mb-5 rounded-full bg-gradient-to-r from-[#4B6BFB] to-[#2F4BFB] text-white">
                <span className="text-xl font-bold">{step.number}</span>
              </div>
              <div className="text-[#4B6BFB] mb-4">{step.icon}</div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">
                {t('step')} {step.number}: {step.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {step.description}
              </p>
            </div>
          ))}
        </div>

        {actionButton && (
          <div className="mt-12 text-center">
            <Link href="/ai-create-ppt">
              <button className="px-8 py-4 bg-[#4B6BFB] hover:bg-[#4B6BFB]/90 text-white font-semibold rounded-full transition-colors text-lg">
                {actionButton}
              </button>
            </Link>
          </div>
        )}
      </div>
    </section>
  )
}

// 使用示例 - PPT生成步骤
const PPTGenerationSteps = () => {
  const stepsData = [
    {
      number: '1',
      title: 'Start with Your Topic',
      description:
        'Choose a topic, upload a document, or paste an outline. Select the number of slides, scene, audience, and language. Our free artificial intelligence powerpoint analyzes your input and generates a structured PPT outline, making it easy to create professional presentations.',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          />
        </svg>
      ),
    },
    {
      number: '2',
      title: 'Pick a Template',
      description:
        'Browse through 1000+ free powerpoint templates with different kinds of powerpoint background tailored for business, education, marketing, and more. Whether you need a modern design or a classic layout, our AI-powered slideshow maker has the perfect template for your needs.',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
          />
        </svg>
      ),
    },
    {
      number: '3',
      title: 'Generate & Download',
      description:
        'Click "Generate" and let our AI create your slides in seconds. Once ready, download your PowerPoint in PPTX, PDF, or video format. It\'s that simple to create stunning presentations with the best free AI PowerPoint generator!',
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-12 h-12"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
          />
        </svg>
      ),
    },
  ]

  return (
    <StepGuide
      title="How to Make a PowerPoint With Our AI IMAGE Generator in 3 Easy Steps?"
      subtitle="From Topic to Slides in Seconds -- Powered by the Best AI Presentation Maker"
      steps={stepsData}
      actionButton="Create PPT with AI"
    />
  )
}

export default StepGuide
