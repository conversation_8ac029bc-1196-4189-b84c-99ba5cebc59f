import React from 'react'
import { getTranslations } from 'next-intl/server'

const ProductIntroduction = async () => {
  const t = await getTranslations()
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-[#EEF2FF] to-white text-gray-800">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* 左侧内容 */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold leading-tight">
                {t('whatIs')}{' '}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-600">
                  {t('AIImageGenerator')}
                </span>
                ?
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {t('AIImageGeneratorDesc')}
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-2xl md:text-3xl font-bold leading-tight">
                {t('whatMakes')}{' '}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-blue-600">
                  {t('AIImageGenerator')}
                </span>{' '}
                {t('unique')}?
              </h3>
              <p className="text-gray-700 mb-4">{t('uniqueIntro')}</p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  {
                    title: t('smartContent'),
                    description: t('smartContentDesc'),
                    icon: (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                        />
                      </svg>
                    ),
                  },
                  {
                    title: t('designAuto'),
                    description: t('designAutoDesc'),
                    icon: (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
                        />
                      </svg>
                    ),
                  },
                  {
                    title: t('multiFormat'),
                    description: t('multiFormatDesc'),
                    icon: (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                        />
                      </svg>
                    ),
                  },
                  {
                    title: t('freeStart'),
                    description: t('freeStartDesc'),
                    icon: (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    ),
                  },
                ].map((feature, index) => (
                  <div
                    key={index}
                    className="bg-white/60 backdrop-blur-lg rounded-xl p-5 border border-indigo-100 hover:bg-white/80 transition-all duration-300 hover:shadow-lg hover:shadow-indigo-200 hover:-translate-y-1"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="bg-gradient-to-br from-indigo-600 to-blue-600 p-2 rounded-lg text-white">
                        {feature.icon}
                      </div>
                      <div>
                        <h4 className="font-bold text-lg text-indigo-700 mb-1">
                          {feature.title}
                        </h4>
                        <p className="text-sm text-gray-700">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧大图 */}
          <div className="relative">
            {/* 这里放置图片，添加动画效果 */}
            <div className="relative rounded-2xl overflow-hidden shadow-2xl shadow-indigo-200 hover:shadow-blue-300 transition-all duration-500 transform hover:-rotate-1 border-4 border-white/60">
              {/* 这里本应放置用户提供的实际图片，我使用一个模拟的彩色块 */}
              <div className="relative h-[500px] bg-gradient-to-r from-indigo-500 to-blue-500 overflow-hidden">
                {/* 模拟PPT界面效果 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-3/4 h-3/4 bg-white rounded-lg shadow-xl flex flex-col">
                    <div className="h-12 bg-indigo-600 rounded-t-lg flex items-center px-4">
                      <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="flex-1 p-6 bg-gradient-to-br from-indigo-50 to-white">
                      <div className="h-8 w-3/4 bg-indigo-100 rounded mb-4"></div>
                      <div className="h-4 w-1/2 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 w-4/6 bg-gray-200 rounded mb-6"></div>
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="h-24 bg-indigo-100 rounded"></div>
                        <div className="h-24 bg-blue-100 rounded"></div>
                      </div>
                      <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 w-4/6 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                </div>

                {/* 添加一些浮动元素，增加炫酷感 */}
                <div className="absolute top-10 right-20 w-16 h-16 bg-blue-400 rounded-full opacity-60 animate-pulse"></div>
                <div className="absolute bottom-20 left-10 w-24 h-24 bg-indigo-500 rounded-full opacity-40 animate-bounce delay-100"></div>
                <div className="absolute top-1/2 left-1/3 w-12 h-12 bg-blue-300 rounded-lg opacity-70 animate-spin"></div>

                {/* 添加科技感线条 */}
                <div className="absolute inset-0">
                  <svg
                    width="100%"
                    height="100%"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <defs>
                      <linearGradient
                        id="grid-gradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <stop
                          offset="0%"
                          stopColor="#6366F1"
                          stopOpacity="0.3"
                        />
                        <stop
                          offset="100%"
                          stopColor="#3B82F6"
                          stopOpacity="0.3"
                        />
                      </linearGradient>
                    </defs>
                    <pattern
                      id="grid"
                      width="40"
                      height="40"
                      patternUnits="userSpaceOnUse"
                    >
                      <path
                        d="M 40 0 L 0 0 0 40"
                        fill="none"
                        stroke="url(#grid-gradient)"
                        strokeWidth="0.5"
                      />
                    </pattern>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                  </svg>
                </div>
              </div>
            </div>

            {/* 添加一些装饰元素 */}
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-full blur-3xl opacity-20"></div>
            <div className="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full blur-3xl opacity-20"></div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ProductIntroduction
