'use client'

import { useState, useEffect, useRef } from 'react'
import {
  <PERSON>rkles,
  Eye,
  Heart,
  Download,
  ImageIcon,
  AlertCircle,
  ArrowRight,
} from 'lucide-react'
import { Button } from '@ui/components/button'
import { downloadImage } from './utils'
import { useTranslations } from 'next-intl'

interface ImageData {
  title: string
  prompt: string
  technique: string
  url: string
}

interface PromptDatabase {
  [key: string]: ImageData[]
}

interface PhotoGalleryProps {
  className?: string
  promptDatabase: PromptDatabase
  defaultCategory?: string
  toolUrl?: string
}

const PhotoGallery = ({
  className = '',
  promptDatabase,
  defaultCategory,
  toolUrl,
}: PhotoGalleryProps) => {
  const t = useTranslations('aiTextToImage')
  // 添加CSS动画样式
  const animationStyles = `
    @keyframes heartBeat {
      0% {
        transform: scale(0);
        opacity: 1;
      }
      50% {
        transform: scale(1.2);
        opacity: 0.8;
      }
      100% {
        transform: scale(1);
        opacity: 0;
      }
    }

    @keyframes floatHeart {
      0% {
        transform: translate(-50%, -50%) rotate(var(--rotation)) translateY(-40px) scale(0);
        opacity: 1;
      }
      50% {
        transform: translate(-50%, -50%) rotate(var(--rotation)) translateY(-80px) scale(1);
        opacity: 0.8;
      }
      100% {
        transform: translate(-50%, -50%) rotate(var(--rotation)) translateY(-120px) scale(0);
        opacity: 0;
      }
    }
  `
  // 从 promptDatabase 生成 categories
  const categories = Object.keys(promptDatabase).map((key, index) => {
    const colorOptions = [
      'from-purple-500 to-blue-500',
      'from-blue-500 to-cyan-500',
      'from-cyan-500 to-teal-500',
      'from-teal-500 to-green-500',
      'from-green-500 to-lime-500',
      'from-lime-500 to-yellow-500',
      'from-yellow-500 to-orange-500',
      'from-orange-500 to-red-500',
      'from-red-500 to-pink-500',
      'from-pink-500 to-purple-500',
      'from-indigo-500 to-purple-500',
      'from-emerald-500 to-green-500',
    ]

    // 尝试获取翻译的分类标签，如果没有则使用默认格式
    const translationKey = `${key}CategoryLabel` as any
    let label: string

    try {
      const translatedLabel = t(translationKey)
      // 如果翻译键不存在，t() 会返回键名本身，所以我们检查是否等于键名
      label =
        translatedLabel !== translationKey
          ? translatedLabel
          : key
              .split('_')
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ')
    } catch {
      // 如果翻译失败，使用默认格式
      label = key
        .split('_')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    }

    return {
      key,
      label,
      color: colorOptions[index % colorOptions.length],
    }
  })

  const [activeCategory, setActiveCategory] = useState(
    defaultCategory || categories[0]?.key || ''
  )
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null)
  const [isVisible, setIsVisible] = useState(false)
  const [hoveredImage, setHoveredImage] = useState<string | null>(null)
  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set())
  const [failedImages, setFailedImages] = useState<Set<string>>(new Set())
  const [modalImageFailed, setModalImageFailed] = useState(false)
  const [modalImageLoading, setModalImageLoading] = useState(false)
  const [likedImages, setLikedImages] = useState<Set<string>>(new Set())
  const [likeAnimations, setLikeAnimations] = useState<Set<string>>(new Set())
  const galleryRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (galleryRef.current) {
      observer.observe(galleryRef.current)
    }

    return () => observer.disconnect()
  }, [])

  // 重置模态框图片状态
  useEffect(() => {
    if (selectedImage) {
      setModalImageFailed(false)
      setModalImageLoading(true)
    }
  }, [selectedImage])

  // 禁用/恢复页面滚动
  useEffect(() => {
    if (selectedImage) {
      // 禁用页面滚动
      document.body.style.overflow = 'hidden'
      document.documentElement.style.overflow = 'hidden'
    } else {
      // 恢复页面滚动
      document.body.style.overflow = ''
      document.documentElement.style.overflow = ''
    }

    // 清理函数：组件卸载时恢复滚动
    return () => {
      document.body.style.overflow = ''
      document.documentElement.style.overflow = ''
    }
  }, [selectedImage])

  const currentImages = promptDatabase[activeCategory] || []

  const handleImageLoad = (imageId: string) => {
    setLoadingImages((prev) => {
      const newSet = new Set(prev)
      newSet.delete(imageId)
      return newSet
    })
    // 成功加载时，从失败列表中移除
    setFailedImages((prev) => {
      const newSet = new Set(prev)
      newSet.delete(imageId)
      return newSet
    })
  }

  const handleImageLoadStart = (imageId: string) => {
    setLoadingImages((prev) => new Set(prev).add(imageId))
    // 开始加载时，从失败列表中移除
    setFailedImages((prev) => {
      const newSet = new Set(prev)
      newSet.delete(imageId)
      return newSet
    })
  }

  const handleImageError = (imageId: string) => {
    setLoadingImages((prev) => {
      const newSet = new Set(prev)
      newSet.delete(imageId)
      return newSet
    })
    // 加载失败时，添加到失败列表
    setFailedImages((prev) => new Set(prev).add(imageId))
  }

  // 处理点赞功能
  const handleLike = (imageId: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation()
    }

    const isLiked = likedImages.has(imageId)

    if (isLiked) {
      // 取消点赞
      setLikedImages((prev) => {
        const newSet = new Set(prev)
        newSet.delete(imageId)
        return newSet
      })
    } else {
      // 添加点赞
      setLikedImages((prev) => new Set(prev).add(imageId))

      // 触发点赞动画
      setLikeAnimations((prev) => new Set(prev).add(imageId))

      // 1.5秒后移除动画状态
      setTimeout(() => {
        setLikeAnimations((prev) => {
          const newSet = new Set(prev)
          newSet.delete(imageId)
          return newSet
        })
      }, 1500)
    }
  }

  // 使用 useEffect 添加全局样式
  useEffect(() => {
    const styleId = 'photo-gallery-animations'
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style')
      style.id = styleId
      style.textContent = animationStyles
      document.head.appendChild(style)
    }

    return () => {
      const existingStyle = document.getElementById(styleId)
      if (existingStyle) {
        document.head.removeChild(existingStyle)
      }
    }
  }, [animationStyles])

  return (
    <div
      ref={galleryRef}
      className={`w-full mx-auto px-4 md:px-6 lg:px-8 ${className}`}
    >
      {/* 标题部分 */}

      {/* 分类标签 */}
      <div
        className={`flex container flex-wrap justify-center gap-2 md:gap-3 mb-8 md:mb-12 px-4 transition-all duration-1000 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}
        style={{ transitionDelay: '200ms' }}
      >
        {categories.map((category, index) => (
          <button
            key={category.key}
            onClick={() => setActiveCategory(category.key)}
            className={`group relative px-4 md:px-6 py-2 md:py-3 rounded-full font-semibold text-sm md:text-base transition-all duration-300 transform hover:scale-105 ${
              activeCategory === category.key
                ? `bg-gradient-to-r ${category.color} text-white shadow-lg`
                : 'bg-slate-800/50 text-gray-300 hover:text-white border border-slate-700/50 hover:border-purple-500/30'
            }`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <span className="relative z-10">{category.label}</span>
            {activeCategory === category.key && (
              <div
                className={`absolute inset-0 bg-gradient-to-r ${category.color} rounded-full blur-lg opacity-50 group-hover:opacity-70 transition-opacity`}
              />
            )}
          </button>
        ))}
      </div>

      {/* 照片墙 - 渲染所有分类的图片以优化SEO */}
      <div
        className={`transition-all duration-1000 ${
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
        }`}
        style={{ transitionDelay: '400ms' }}
      >
        {categories.map((category) => {
          const categoryImages = promptDatabase[category.key] || []
          const isActiveCategory = category.key === activeCategory

          return (
            <div
              key={category.key}
              className={`${isActiveCategory ? 'block' : 'hidden'} ${
                categoryImages.length <= 4 ? 'max-w-6xl mx-auto' : ''
              }`}
            >
              <div
                className={`
                  gap-4 md:gap-6
                  ${
                    categoryImages.length <= 2
                      ? 'flex flex-wrap justify-center items-start'
                      : categoryImages.length <= 4
                      ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 place-items-center'
                      : categoryImages.length <= 8
                      ? 'columns-1 sm:columns-2 lg:columns-3 xl:columns-4 space-y-4 md:space-y-6'
                      : 'columns-1 sm:columns-2 lg:columns-4 xl:columns-4 space-y-4 md:space-y-6'
                  }
                `}
              >
                {categoryImages.map((image: ImageData, index: number) => {
                  // 为不同的图片创建不同的高度，创建真正的瀑布流效果
                  const heights = [
                    'aspect-[3/4]', // 竖图
                    'aspect-[4/3]', // 横图
                    'aspect-[2/3]', // 长竖图
                    'aspect-[5/4]', // 宽横图
                    'aspect-[3/5]', // 超长竖图
                    'aspect-[16/9]', // 宽屏横图
                    'aspect-[1/1]', // 正方形
                    'aspect-[4/5]', // 略长竖图
                  ]
                  const randomHeight = heights[index % heights.length]

                  return (
                    <div
                      key={`${category.key}-${index}`}
                      className={`
                  group relative bg-slate-800/30 backdrop-blur-sm border border-slate-700/50 rounded-xl md:rounded-2xl overflow-hidden hover:border-purple-500/50 transition-all duration-500 hover:scale-[1.02] hover:shadow-2xl hover:shadow-purple-500/20
                  ${
                    currentImages.length <= 2
                      ? 'w-full max-w-sm mx-2 mb-4 md:mb-6'
                      : currentImages.length <= 4
                      ? 'w-full max-w-md mb-4 md:mb-6'
                      : 'break-inside-avoid mb-4 md:mb-6'
                  }
                `}
                      onMouseEnter={() =>
                        setHoveredImage(`${category.key}-${index}`)
                      }
                      onMouseLeave={() => setHoveredImage(null)}
                      style={{
                        animationDelay: `${index * 100}ms`,
                        transform: 'translateZ(0)', // 优化性能
                      }}
                    >
                      {/* 图片容器 */}
                      <div
                        className={`relative ${randomHeight} overflow-hidden`}
                      >
                        {/* 加载状态 */}
                        {loadingImages.has(`${category.key}-${index}`) && (
                          <div className="absolute inset-0 bg-slate-800/50 flex items-center justify-center z-10">
                            <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                          </div>
                        )}

                        {/* 加载失败状态 */}
                        {failedImages.has(`${category.key}-${index}`) && (
                          <div className="absolute inset-0 bg-slate-800/80 backdrop-blur-sm flex flex-col items-center justify-center z-10 border-2 border-dashed border-slate-600/50">
                            <div className="text-center p-4">
                              <div className="mb-3">
                                <ImageIcon className="w-8 h-8 md:w-10 md:h-10 text-slate-500 mx-auto" />
                              </div>
                              <div className="mb-2">
                                <AlertCircle className="w-4 h-4 text-red-400 mx-auto" />
                              </div>
                              <p className="text-slate-400 text-xs md:text-sm font-medium mb-1">
                                Image failed to load
                              </p>
                              <p className="text-slate-500 text-xs">
                                {image.title}
                              </p>
                            </div>
                          </div>
                        )}

                        <img
                          src={image.url}
                          alt={image.title}
                          className={`w-full h-full object-cover transition-transform duration-700 group-hover:scale-110 ${
                            failedImages.has(`${category.key}-${index}`)
                              ? 'opacity-0'
                              : 'opacity-100'
                          }`}
                          loading="lazy"
                          onLoadStart={() =>
                            handleImageLoadStart(`${category.key}-${index}`)
                          }
                          onLoad={() =>
                            handleImageLoad(`${category.key}-${index}`)
                          }
                          onError={() =>
                            handleImageError(`${category.key}-${index}`)
                          }
                        />

                        {/* 悬停遮罩 */}
                        <div
                          className={`absolute inset-0 bg-gradient-to-t from-black/90 via-black/40 to-transparent transition-opacity duration-300 ${
                            hoveredImage === `${category.key}-${index}`
                              ? 'opacity-100'
                              : 'opacity-0'
                          }`}
                        >
                          <div className="absolute bottom-3 md:bottom-4 left-3 md:left-4 right-3 md:right-4">
                            {image.title && (
                              <h4 className="text-white font-bold text-base md:text-lg mb-1 md:mb-2">
                                {image.title}
                              </h4>
                            )}
                            {image.technique && (
                              <p className="text-gray-300 text-xs md:text-sm mb-2 line-clamp-2">
                                {image.technique}
                              </p>
                            )}

                            {/* AI Prompt 显示区域 */}
                            {image.prompt && (
                              <div className="mb-2 md:mb-3">
                                <div className="bg-gradient-to-r from-purple-300/20 to-pink-300/20 backdrop-blur-sm border border-purple-300/30 rounded-lg px-2 md:px-3 py-1.5 md:py-2">
                                  <div className="flex items-start gap-1.5 md:gap-2">
                                    <Sparkles className="w-3 h-3 md:w-3.5 md:h-3.5 text-purple-300 mt-0.5 flex-shrink-0" />
                                    <div className="min-w-0 flex-1">
                                      <p className="text-purple-200 text-xs md:text-xs font-medium mb-0.5">
                                        {t('galleryAiPrompt')}
                                      </p>
                                      <p className="text-gray-200 text-xs leading-relaxed line-clamp-2 font-mono">
                                        {image.prompt}
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* 交互按钮 */}
                            <div className="flex items-center gap-1 md:gap-2">
                              <Button
                                size="sm"
                                className="bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm text-xs md:text-sm px-2 md:px-3 py-1 md:py-2"
                                onClick={() => setSelectedImage(image)}
                              >
                                <Eye className="w-3 h-3 md:w-4 md:h-4 mr-1" />
                                {t('galleryView')}
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                className={`relative text-white hover:bg-white/20 p-1 md:p-2 transition-all duration-300 ${
                                  likedImages.has(`${category.key}-${index}`)
                                    ? 'text-red-500 hover:text-red-400 scale-110'
                                    : 'hover:scale-105'
                                }`}
                                onClick={(e) =>
                                  handleLike(`${category.key}-${index}`, e)
                                }
                              >
                                <Heart
                                  className={`w-3 h-3 md:w-4 md:h-4 transition-all duration-300 ${
                                    likedImages.has(`${category.key}-${index}`)
                                      ? 'fill-current'
                                      : ''
                                  }`}
                                />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                className="text-white hover:bg-white/20 p-1 md:p-2"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  downloadImage(image.url, image.title)
                                }}
                              >
                                <Download className="w-3 h-3 md:w-4 md:h-4" />
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* 点赞特效动画 */}
                        {likeAnimations.has(`${category.key}-${index}`) && (
                          <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-20">
                            <div className="relative">
                              {/* 主要爱心 */}
                              <Heart
                                className="w-16 h-16 text-red-500 fill-current animate-bounce"
                                style={{
                                  animation: 'heartBeat 1.5s ease-out forwards',
                                }}
                              />
                              {/* 周围的小爱心 */}
                              {[...Array(6)].map((_, i) => (
                                <Heart
                                  key={i}
                                  className="absolute w-4 h-4 text-pink-400 fill-current"
                                  style={{
                                    top: '50%',
                                    left: '50%',
                                    transform: `translate(-50%, -50%) rotate(${
                                      i * 60
                                    }deg) translateY(-40px)`,
                                    animation: `floatHeart 1.5s ease-out ${
                                      i * 0.1
                                    }s forwards`,
                                  }}
                                />
                              ))}
                            </div>
                          </div>
                        )}

                        {/* 分类标签 */}
                        <div className="absolute top-4 left-4">
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-semibold text-white bg-gradient-to-r ${
                              categories.find((c) => c.key === activeCategory)
                                ?.color || 'from-purple-500 to-pink-500'
                            }`}
                          >
                            {
                              categories.find((c) => c.key === activeCategory)
                                ?.label
                            }
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>

      {/* 模态框 */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-in fade-in duration-300"
          onClick={() => setSelectedImage(null)}
        >
          <div
            className="bg-slate-800 rounded-2xl max-w-5xl w-full max-h-[75vh] overflow-hidden shadow-2xl border border-slate-700/50 animate-in zoom-in-95 duration-300"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative">
              {/* 模态框图片容器 */}
              <div className="relative min-h-[20vh] bg-slate-900 flex items-center justify-center">
                {/* 加载状态 */}
                {modalImageLoading && !modalImageFailed && (
                  <div className="absolute inset-0 flex items-center justify-center z-10">
                    <div className="text-center">
                      <div className="w-12 h-12 border-2 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                      <p className="text-slate-400 text-sm">Loading image...</p>
                    </div>
                  </div>
                )}

                {/* 加载失败状态 */}
                {modalImageFailed && (
                  <div className="absolute inset-0 flex items-center justify-center z-10">
                    <div className="text-center p-8">
                      <div className="mb-4">
                        <ImageIcon className="w-16 h-16 text-slate-500 mx-auto mb-3" />
                        <AlertCircle className="w-8 h-8 text-red-400 mx-auto" />
                      </div>
                      <h3 className="text-lg font-semibold text-slate-300 mb-2">
                        Failed to load image
                      </h3>
                      <p className="text-slate-500 text-sm mb-4">
                        The image could not be displayed
                      </p>
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-slate-600 text-slate-400 hover:bg-slate-700"
                        onClick={() => {
                          setModalImageFailed(false)
                          setModalImageLoading(true)
                          // 强制重新加载图片
                          const img = document.querySelector(
                            '#modal-image'
                          ) as HTMLImageElement
                          if (img) {
                            img.src = img.src
                          }
                        }}
                      >
                        Try Again
                      </Button>
                    </div>
                  </div>
                )}
                <div className="h-[45vh] relative w-full">
                  <img
                    id="modal-image"
                    src={selectedImage.url}
                    alt={selectedImage.title}
                    className={`w-full pointer-events-none absolute blur-sm inset-0 h-auto max-h-[45vh] object-cover transition-opacity duration-300 ${
                      modalImageFailed ? 'opacity-0' : 'opacity-100'
                    }`}
                  />
                  <img
                    id="modal-image"
                    src={selectedImage.url}
                    alt={selectedImage.title}
                    className={`w-full pointer-events-none absolute z-10 inset-0 h-auto max-h-[45vh] object-contain transition-opacity duration-300 ${
                      modalImageFailed ? 'opacity-0' : 'opacity-100'
                    }`}
                    onLoad={() => {
                      setModalImageLoading(false)
                      setModalImageFailed(false)
                    }}
                    onError={() => {
                      setModalImageLoading(false)
                      setModalImageFailed(true)
                    }}
                  />

                  {/* 模态框点赞特效动画 */}
                  {likeAnimations.has(`modal-${selectedImage.url}`) && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-20">
                      <div className="relative">
                        {/* 主要爱心 */}
                        <Heart
                          className="w-20 h-20 text-red-500 fill-current"
                          style={{
                            animation: 'heartBeat 1.5s ease-out forwards',
                          }}
                        />
                        {/* 周围的小爱心 */}
                        {[...Array(8)].map((_, i) => (
                          <Heart
                            key={i}
                            className="absolute w-6 h-6 text-pink-400 fill-current"
                            style={{
                              top: '50%',
                              left: '50%',
                              transform: `translate(-50%, -50%) rotate(${
                                i * 45
                              }deg) translateY(-60px)`,
                              animation: `floatHeart 1.5s ease-out ${
                                i * 0.1
                              }s forwards`,
                            }}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-4 right-4 bg-black/70 hover:bg-black/90 text-white rounded-full w-10 h-10 flex items-center justify-center transition-all duration-200 hover:scale-110 z-20"
              >
                <span className="text-xl leading-none">×</span>
              </button>

              {/* 图片操作按钮 */}
              <div className="absolute z-[11] bottom-4 right-4 flex gap-2">
                <Button
                  size="sm"
                  className={`bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm transition-all duration-300 ${
                    likedImages.has(`modal-${selectedImage.url}`)
                      ? 'bg-red-500/30 hover:bg-red-500/40 scale-110'
                      : 'hover:scale-105'
                  }`}
                  onClick={() => handleLike(`modal-${selectedImage.url}`)}
                >
                  <Heart
                    className={`w-4 h-4 mr-1 transition-all duration-300 ${
                      likedImages.has(`modal-${selectedImage.url}`)
                        ? 'fill-current text-red-400'
                        : ''
                    }`}
                  />
                </Button>
                <Button
                  size="sm"
                  className="bg-white/20 hover:bg-white/30 text-white border-0 backdrop-blur-sm"
                  onClick={() =>
                    downloadImage(selectedImage.url, selectedImage.title)
                  }
                >
                  <Download className="w-4 h-4 mr-1" />
                  {t('gallerySave')}
                </Button>
                <Button
                  size="sm"
                  className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white border-0 backdrop-blur-sm transition-all duration-300 hover:scale-105"
                  onClick={() => {
                    if (toolUrl) {
                      window.open(toolUrl, '_blank')
                    }
                  }}
                >
                  {t('galleryUseNow')}
                  <ArrowRight className="w-4 h-4 mr-1" />
                </Button>
              </div>
            </div>

            <div className="p-6 max-h-[30vh] overflow-y-auto">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-2">
                    {selectedImage.title}
                  </h3>
                  <p className="text-purple-300 text-sm font-medium">
                    {categories.find((c) => c.key === activeCategory)?.label}{' '}
                    Style
                  </p>
                </div>
                <span
                  className={`px-3 py-1 rounded-full text-xs font-semibold text-white bg-gradient-to-r ${
                    categories.find((c) => c.key === activeCategory)?.color ||
                    'from-purple-500 to-pink-500'
                  }`}
                >
                  {t('galleryAiGenerated')}
                </span>
              </div>

              <p className="text-gray-300 mb-6 leading-relaxed">
                {selectedImage.technique}
              </p>

              <div className="bg-slate-900/50 rounded-xl p-4 border border-slate-700/50">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-white font-semibold flex items-center">
                    <Sparkles className="w-4 h-4 mr-2 text-purple-400" />
                    {t('galleryAiPrompt')}
                  </h4>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-purple-300 hover:text-purple-200 hover:bg-purple-500/20"
                    onClick={() =>
                      navigator.clipboard.writeText(selectedImage.prompt)
                    }
                  >
                    {t('galleryCopy')}
                  </Button>
                </div>
                <p className="text-gray-300 text-sm font-mono leading-relaxed bg-slate-800/50 p-3 rounded-lg border border-slate-700/30">
                  {selectedImage.prompt}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PhotoGallery
