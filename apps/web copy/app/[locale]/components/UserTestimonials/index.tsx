import React from 'react'
import Image from 'next/image'
import { getTranslations } from 'next-intl/server'
import Link from 'next/link'

const UserTestimonials = async () => {
  const t = await getTranslations()
  // Testimonials data
  const testimonials = [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      role: 'Business Analyst',
      quote:
        "AI IMAGE Generator streamlines presentation creation, enhances data visualization, and boosts productivity with its AI-powered automation and professional templates. It's a game-changer for business reports and client meetings!",
      avatar: '/users/user_1.png', // Replace with actual path
      rating: 5,
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Product Designer',
      quote:
        'AI IMAGE leverages advanced AI technology to streamline slide creation. Its intuitive design features and effortless customization save invaluable time and resources, making it my go-to tool for product pitches.',
      avatar: '/users/user_2.png',
      rating: 5,
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'College Teacher',
      quote:
        'AI IMAGE transforms lesson materials into visually engaging presentations, saving busy educators hours of work. My students are more engaged, and my lessons are more impactful -- thank you, AI IMAGE!',
      avatar: '/users/user_3.png',
      rating: 5,
    },
    {
      id: 4,
      name: '<PERSON>',
      role: 'Marketing Manager',
      quote:
        "Creating marketing decks has never been easier! AI IMAGE's AI-generated content and modern templates help me deliver stunning presentations that impress clients and stakeholders every time.",
      avatar: '/users/user_4.png',
      rating: 5,
    },
    {
      id: 5,
      name: 'Alex Carter',
      role: 'Startup Founder',
      quote:
        "As a startup founder, time is my most valuable resource. AI IMAGE's one-click slide generation and pitch deck templates have been a lifesaver for investor meetings and product launches.",
      avatar: '/users/user_5.png',
      rating: 5,
    },
    {
      id: 6,
      name: 'Emily Zhang',
      role: 'Freelance Consultant',
      quote:
        "AI IMAGE's AI-powered design suggestions and easy export options make it the perfect tool for freelancers. I can now focus on my content while AI handles the design -- highly recommend!",
      avatar: '/users/user_6.png',
      rating: 5,
    },
    {
      id: 7,
      name: 'David Lee',
      role: 'HR Manager',
      quote:
        "From onboarding decks to training materials, AI IMAGE's customizable templates and AI-driven layouts have revolutionized how we create internal presentations. It's a must-have for HR teams!",
      avatar: '/users/user_7.png',
      rating: 5,
    },
    {
      id: 8,
      name: 'Maria Gonzalez',
      role: 'Nonprofit Director',
      quote:
        'AI IMAGE helps us create impactful presentations for fundraising and awareness campaigns. The AI-generated visuals and donor-friendly templates are a perfect fit for our mission.',
      avatar: '/users/user_8.png',
      rating: 5,
    },
    {
      id: 9,
      name: 'Jake Miller',
      role: 'Student',
      quote:
        'As a student, AI IMAGE has made group projects and class presentations so much easier. The free templates and AI-powered editing save me hours while making my slides look professional.',
      avatar: '/users/user_9.png',
      rating: 5,
    },
  ]

  const StarRating = ({ rating }) => {
    return (
      <div className="flex gap-1">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill={i < rating ? '#4B6BFB' : '#E5E7EB'}
            className="w-5 h-5"
          >
            <path
              fillRule="evenodd"
              d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
              clipRule="evenodd"
            />
          </svg>
        ))}
      </div>
    )
  }

  return (
    <section className="py-24 bg-gradient-to-b from-[#EEF2FF] to-white relative overflow-hidden">
      {/* Decorative background elements */}
      <div className="absolute top-0 left-0 right-0 h-64 bg-gradient-to-b from-[#EEF2FF] to-transparent opacity-70"></div>
      <div className="absolute inset-0">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-9xl font-bold text-[#4B6BFB] opacity-10 rotate-12">
            5★
          </div>
          <div className="absolute bottom-10 right-20 text-9xl font-bold text-[#4B6BFB] opacity-10 -rotate-12">
            5★
          </div>
          <div className="absolute top-1/3 right-1/4 text-7xl font-bold text-[#4B6BFB] opacity-10 rotate-45">
            5★
          </div>
          <div className="absolute bottom-1/4 left-1/4 text-7xl font-bold text-[#4B6BFB] opacity-10 -rotate-[30deg]">
            5★
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-[#4B6BFB] to-[#2F4BFB]">
            {t('testimonialTitle')}
          </h2>
          <p className="text-xl text-gray-600">{t('testimonialDesc')}</p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.id}
              className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden border border-[#4B6BFB]/10 flex flex-col h-full"
            >
              <div className="px-6 pt-6 pb-4">
                <div className="flex items-center mb-4">
                  <div className="relative h-16 w-16 rounded-full overflow-hidden mr-4 border-2 border-[#4B6BFB]/20">
                    <Image
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      fill
                      sizes="64px"
                      className="object-cover"
                    />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg text-gray-900">
                      {testimonial.name}
                    </h3>
                    <p className="text-[#4B6BFB]">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed italic">
                  "{testimonial.quote}"
                </p>
              </div>
              <div className="px-6 py-4 mt-auto border-t border-[#4B6BFB]/10">
                <StarRating rating={testimonial.rating} />
              </div>
            </div>
          ))}
        </div>

        {/* CTA at the bottom */}
        <div className="mt-16 text-center">
          <Link href="/ai-create-ppt" target="_blank">
            <button className="px-8 py-4 rounded-full text-white font-medium bg-[#4B6BFB] hover:bg-[#4B6BFB]/90 shadow-lg shadow-[#4B6BFB]/30 hover:shadow-xl hover:shadow-[#4B6BFB]/40 transition-all">
              {t('joinUsersText')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}
export default UserTestimonials
