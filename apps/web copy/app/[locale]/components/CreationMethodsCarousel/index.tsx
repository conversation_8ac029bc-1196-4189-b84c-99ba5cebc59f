'use client'
import React, { useState, useEffect, useRef } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useTranslations } from 'next-intl'

const CreationMethodsCarousel = () => {
  const t = useTranslations()
  // Creation methods data
  const creationMethods = [
    {
      id: 1,
      title: t('topicTitle'),
      description: t('topicDesc'),
      buttonText: t('topicButton'),
      imageSrc: '/create_methods/AI Generates from Your Topic.png', // 替换为实际图片路径
      imageAlt: 'AI generation illustration',
    },
    {
      id: 2,
      title: t('uploadTitle'),
      description: t('uploadDesc'),
      buttonText: t('uploadButton'),
      imageSrc: '/create_methods/upload_file.png', // 替换为实际图片路径
      imageAlt: 'File upload illustration',
    },
    {
      id: 3,
      title: t('pasteTitle'),
      description: t('pasteDesc'),
      buttonText: t('pasteButton'),
      imageSrc: '/create_methods/paste_file.png', // 替换为实际图片路径
      imageAlt: 'Text paste illustration',
    },
    {
      id: 4,
      title: t('linkTitle'),
      description: t('linkDesc'),
      buttonText: t('linkButton'),
      imageSrc: '/create_methods/AI Generates from Your Topic.png', // 替换为实际图片路径
      imageAlt: 'Link sharing illustration',
    },
  ]

  // State for active slide
  const [activeSlide, setActiveSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const autoPlayRef = useRef(null)

  // Auto-play functionality with cleanup
  useEffect(() => {
    if (isAutoPlaying) {
      autoPlayRef.current = setInterval(() => {
        setActiveSlide((prev) => (prev + 1) % creationMethods.length)
      }, 5000) // Change slide every 5 seconds
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current)
      }
    }
  }, [isAutoPlaying, creationMethods.length])

  // Pause auto-play on hover
  const handleMouseEnter = () => setIsAutoPlaying(false)
  const handleMouseLeave = () => setIsAutoPlaying(true)

  // Handle manual navigation
  const goToSlide = (index) => {
    setActiveSlide(index)
    // Reset timer when manually changing slides
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current)
    }
    setIsAutoPlaying(true)
  }

  return (
    <section
      className="py-20 px-4 bg-gradient-to-b from-[#EEF2FF] to-white"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="max-w-6xl mx-auto">
        {/* Section header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-[#4B6BFB] to-[#2F4BFB]">
            {t('mainTitle')}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('mainDesc')}
          </p>
        </div>

        {/* Carousel container */}
        <div className="relative bg-white rounded-2xl shadow-xl overflow-hidden border border-[#4B6BFB]/10">
          <div className="relative min-h-[400px] md:min-h-[450px]">
            {creationMethods.map((method, index) => (
              <div
                key={method.id}
                className={`absolute inset-0 transition-opacity duration-500 ${
                  index === activeSlide ? 'opacity-100 z-10' : 'opacity-0 z-0'
                }`}
                aria-hidden={index !== activeSlide}
              >
                <div className="flex flex-col md:flex-row h-full">
                  {/* Left side - Illustration */}
                  <div className="w-full md:w-1/2 bg-gradient-to-br from-[#EEF2FF] to-[#E8ECFF] relative flex items-center justify-center py-8 md:py-0">
                    <div className="relative w-full h-full max-w-[90%] max-h-[90%] flex items-center justify-center">
                      <Image
                        src={method.imageSrc}
                        alt={method.imageAlt}
                        width={400}
                        height={400}
                        className="object-contain w-full h-full"
                        style={{ maxWidth: '100%', maxHeight: '100%' }}
                        priority={index === 0}
                      />
                    </div>
                  </div>

                  {/* Right side - Content */}
                  <div className="w-full md:w-1/2 p-8 md:p-12 flex flex-col justify-center">
                    <h3 className="text-2xl font-bold text-[#4B6BFB] mb-4">
                      {method.title}
                    </h3>
                    <p className="text-gray-600 mb-6 leading-relaxed">
                      {method.description}
                    </p>
                    <Link href="/ai-create-ppt">
                      <button className="self-start px-6 py-3 bg-[#4B6BFB] hover:bg-[#4B6BFB]/90 text-white font-medium rounded-full transition-colors">
                        {method.buttonText}
                      </button>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation dots */}
          <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-20">
            {creationMethods.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all ${
                  index === activeSlide
                    ? 'bg-[#4B6BFB] w-6'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Go to slide ${index + 1}`}
                aria-current={index === activeSlide ? 'true' : 'false'}
              />
            ))}
          </div>

          {/* Left/Right arrows */}
          <button
            className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white flex items-center justify-center shadow-md z-20 hover:bg-[#EEF2FF] transition-colors hidden md:flex border border-[#4B6BFB]/10"
            onClick={() =>
              goToSlide(
                (activeSlide - 1 + creationMethods.length) %
                  creationMethods.length
              )
            }
            aria-label="Previous slide"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={2}
              stroke="currentColor"
              className="w-5 h-5 text-[#4B6BFB]"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.75 19.5L8.25 12l7.5-7.5"
              />
            </svg>
          </button>

          <button
            className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white items-center justify-center shadow-md z-20 hover:bg-[#EEF2FF] transition-colors hidden md:flex border border-[#4B6BFB]/10"
            onClick={() =>
              goToSlide((activeSlide + 1) % creationMethods.length)
            }
            aria-label="Next slide"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={2}
              stroke="currentColor"
              className="w-5 h-5 text-[#4B6BFB]"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M8.25 4.5l7.5 7.5-7.5 7.5"
              />
            </svg>
          </button>
        </div>

        {/* Hidden but present full content for SEO */}
        <div className="sr-only" aria-hidden="true">
          <h3>5. 生成PPT的不同方式</h3>
          {creationMethods.map((method) => (
            <div key={`seo-${method.id}`}>
              <h4>{method.title}</h4>
              <p>{method.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CreationMethodsCarousel
