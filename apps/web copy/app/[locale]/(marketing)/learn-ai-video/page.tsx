import { PostListItem } from '@marketing/blog/components/PostListItem'
import { allLearnAiVideoPosts } from 'content-collections'
import { getLocale } from 'next-intl/server'

export async function generateMetadata() {
  return {
    title: 'Learn AI Video Generation',
    description:
      'Master AI video generation with our comprehensive tutorials and guides.',
  }
}

export default async function LearnAiVideoListPage() {
  const locale = await getLocale()

  return (
    <div className="container max-w-6xl pt-32 pb-16">
      <div className="mb-12 text-center">
        <h1 className="font-bold text-4xl md:text-5xl leading-tight tracking-tight mb-4">
          Learn AI Video Generation
        </h1>
        <p className="text-xl text-muted-foreground">
          Master the art of AI-powered video creation
        </p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {allLearnAiVideoPosts
          .filter((post) => post.published && locale === post.locale)
          .sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          )
          .map((post) => (
            <PostListItem
              post={post}
              key={post.path}
              basePath="/learn-ai-video"
            />
          ))}
      </div>
    </div>
  )
}
