'use client'

import VideoWithBackgroundMusic, { VideoWithBackgroundMusicRef } from '@/[locale]/components/VideoWithBackgroundMusic'
import React, { useRef, useState } from 'react'


export default function TestVideoMusicPage() {
  const videoRef = useRef<VideoWithBackgroundMusicRef>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [audioVolume, setAudioVolume] = useState(0.5)
  const [videoVolume, setVideoVolume] = useState(0)
  const [logs, setLogs] = useState<string[]>([])
  const [selectedVideo, setSelectedVideo] = useState(
    '/videos/face-creative.mp4'
  )
  const [selectedAudio, setSelectedAudio] = useState(
    'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
  )

  // 可用的测试视频
  const testVideos = [
    { name: 'Face Creative', url: '/videos/face-creative.mp4' },
    { name: 'Face Swap', url: '/videos/face-swap.mp4' },
    { name: 'Face TK', url: '/videos/face-tk.mp4' },
    { name: 'Image Remove Object', url: '/videos/image-removeobj.mp4' },
    { name: 'AI Shield', url: '/videos/ai-image-to-video/ai-shield.mp4' },
    {
      name: 'Butterfly Animate',
      url: '/videos/ai-image-to-video/butterfly-animate.mp4',
    },
  ]

  // 可用的测试音频
  const testAudios = [
    {
      name: 'Bell Ring',
      url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    },
    {
      name: 'Kalimba',
      url: 'https://www.learningcontainer.com/wp-content/uploads/2020/02/Kalimba.mp3',
    },
    {
      name: 'Simple Beep',
      url: 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT',
    },
  ]

  // 添加日志
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs((prev) => [...prev.slice(-9), `[${timestamp}] ${message}`])
  }

  // 播放/暂停
  const handlePlayPause = async () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
      addLog('暂停播放')
    } else {
      await videoRef.current.play()
      addLog('开始播放')
    }
  }

  // 跳转到指定时间
  const handleSeek = (time: number) => {
    if (!videoRef.current) return
    videoRef.current.setCurrentTime(time)
    setCurrentTime(time)
    addLog(`跳转到 ${time.toFixed(1)}s`)
  }

  // 调整音频音量
  const handleAudioVolumeChange = (volume: number) => {
    if (!videoRef.current) return
    videoRef.current.setAudioVolume(volume)
    setAudioVolume(volume)
    addLog(`背景音乐音量: ${(volume * 100).toFixed(0)}%`)
  }

  // 调整视频音量
  const handleVideoVolumeChange = (volume: number) => {
    if (!videoRef.current) return
    videoRef.current.setVideoVolume(volume)
    setVideoVolume(volume)
    addLog(`视频原声音量: ${(volume * 100).toFixed(0)}%`)
  }

  // 更新播放时间
  const updateTime = () => {
    if (!videoRef.current) return
    const time = videoRef.current.getCurrentTime()
    const dur = videoRef.current.getDuration()
    setCurrentTime(time)
    setDuration(dur)
  }

  // 定时更新播放时间
  React.useEffect(() => {
    const interval = setInterval(updateTime, 1000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">
          视频背景音乐组件测试
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 视频播放区域 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">视频播放器</h2>

            {/* 资源选择 */}
            <div className="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  选择视频:
                </label>
                <select
                  value={selectedVideo}
                  onChange={(e) => setSelectedVideo(e.target.value)}
                  className="w-full p-2 border rounded"
                >
                  {testVideos.map((video) => (
                    <option key={video.url} value={video.url}>
                      {video.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">
                  选择音频:
                </label>
                <select
                  value={selectedAudio}
                  onChange={(e) => setSelectedAudio(e.target.value)}
                  className="w-full p-2 border rounded"
                >
                  {testAudios.map((audio) => (
                    <option key={audio.url} value={audio.url}>
                      {audio.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <VideoWithBackgroundMusic
              ref={videoRef}
              videoUrl={selectedVideo}
              audioUrl={selectedAudio}
              autoPlay={false}
              controls={true}
              muted={true}
              audioVolume={audioVolume}
              videoVolume={videoVolume}
              audioLoop={true}
              className="w-full rounded-lg overflow-hidden"
              style={{ aspectRatio: '16/9' }}
              onVideoLoad={() => addLog('视频加载完成')}
              onAudioLoad={() => addLog('音频加载完成')}
              onPlayStateChange={(playing) => {
                setIsPlaying(playing)
                addLog(playing ? '播放状态: 播放中' : '播放状态: 已暂停')
              }}
              onError={(error) => addLog(`错误: ${error}`)}
            />

            {/* 播放信息 */}
            <div className="mt-4 text-sm text-gray-600">
              <p>
                播放时间: {currentTime.toFixed(1)}s / {duration.toFixed(1)}s
              </p>
              <p>播放状态: {isPlaying ? '播放中' : '已暂停'}</p>
            </div>
          </div>

          {/* 控制面板 */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4">控制面板</h2>

            {/* 播放控制 */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">播放控制</h3>
              <div className="flex gap-2 mb-4">
                <button
                  onClick={handlePlayPause}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  {isPlaying ? '暂停' : '播放'}
                </button>
                <button
                  onClick={() => handleSeek(0)}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  重新开始
                </button>
                <button
                  onClick={() => handleSeek(currentTime + 10)}
                  className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                >
                  快进10s
                </button>
              </div>
            </div>

            {/* 音量控制 */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">音量控制</h3>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  背景音乐音量: {(audioVolume * 100).toFixed(0)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={audioVolume}
                  onChange={(e) =>
                    handleAudioVolumeChange(parseFloat(e.target.value))
                  }
                  className="w-full"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">
                  视频原声音量: {(videoVolume * 100).toFixed(0)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={videoVolume}
                  onChange={(e) =>
                    handleVideoVolumeChange(parseFloat(e.target.value))
                  }
                  className="w-full"
                />
              </div>
            </div>

            {/* 快速跳转 */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">快速跳转</h3>
              <div className="grid grid-cols-4 gap-2">
                {[0, 25, 50, 75].map((percent) => (
                  <button
                    key={percent}
                    onClick={() => handleSeek((duration * percent) / 100)}
                    className="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
                  >
                    {percent}%
                  </button>
                ))}
              </div>
            </div>

            {/* 日志区域 */}
            <div>
              <h3 className="text-lg font-medium mb-3">操作日志</h3>
              <div className="bg-gray-50 rounded p-3 h-40 overflow-y-auto">
                {logs.map((log, index) => (
                  <div key={index} className="text-xs text-gray-700 mb-1">
                    {log}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">组件特性说明</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">核心功能</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 视频原声自动静音</li>
                <li>• 背景音乐自动循环播放</li>
                <li>• 视频和音频同步播放/暂停</li>
                <li>• 支持时间跳转同步</li>
                <li>• 独立的音量控制</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">可配置选项</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 自动播放设置</li>
                <li>• 循环播放控制</li>
                <li>• 控制条显示/隐藏</li>
                <li>• 音量级别调节</li>
                <li>• 完整的事件回调</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
