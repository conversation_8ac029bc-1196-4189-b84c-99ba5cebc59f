'use client'

import { useState, useEffect, useCallback } from 'react'
import { useTranslations, useLocale } from 'next-intl'
import { useInView } from 'react-intersection-observer'
import Masonry from 'react-masonry-css'
import { Download, Info } from 'lucide-react'
import { useRouter, <PERSON> } from '@i18n/routing'
import { getUserIdFromCookie } from '@/utils/lib'
import toast from 'react-hot-toast'

// 定义历史记录类型
type ImageGenerationRecord = {
  id: string
  user_id: string
  original_image_url: string // 可能包含多个URL，用 | 分隔
  generated_image_url: string // 可能包含多个URL，用 | 分隔
  prompt: string
  ratio: string
  created_at: string
  updated_at: string
  is_deleted: boolean
  extra_data: string | null
}

// 定义分页类型
type Pagination = {
  total: number
  page: number
  limit: number
  totalPages: number
}

export default function ImageHistoryClient() {
  const t = useTranslations()
  const router = useRouter()
  const [records, setRecords] = useState<ImageGenerationRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState<Pagination>({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 0,
  })
  const [hasMore, setHasMore] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 使用 Intersection Observer 检测何时加载更多图片
  const { ref, inView } = useInView({
    threshold: 0,
  })

  // 加载数据
  const loadData = useCallback(
    async (page: number) => {
      try {
        setLoading(true)

        // 获取用户ID
        const userId = getUserIdFromCookie()

        const response = await fetch(
          `/api/images/get-history?userId=${userId}&page=${page}&limit=${pagination.limit}`
        )

        if (!response.ok) {
          throw new Error(t('ImageHistory.failedToGetHistory'))
        }

        const result = await response.json()

        if (result.success) {
          // 如果是首页，替换数据，否则追加数据
          if (page === 1) {
            setRecords(result.data)
          } else {
            setRecords((prev) => [...prev, ...result.data])
          }

          setPagination(result.pagination)

          // 检查是否还有更多数据
          setHasMore(result.pagination.page < result.pagination.totalPages)
        } else {
          throw new Error(result.error || t('ImageHistory.failedToGetHistory'))
        }
      } catch (err) {
        console.error('加载历史记录失败:', err)
        setError(err instanceof Error ? err.message : '未知错误')
      } finally {
        setLoading(false)
      }
    },
    [pagination.limit]
  )

  // 初始加载
  useEffect(() => {
    loadData(1)
  }, [loadData])

  // 当用户滚动到底部且有更多数据时加载更多
  useEffect(() => {
    if (inView && !loading && hasMore) {
      loadData(pagination.page + 1)
    }
  }, [inView, loading, hasMore, pagination.page, loadData])

  // 处理下载图片 - 使用 NextJS 路由 (会触发 TopLoader)
  const handleDownload = (imageUrl: string) => {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = `/api/download?url=${encodeURIComponent(imageUrl)}`
    link.download = `generated-image-${Date.now()}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 处理下载图片 - 直接下载，不使用 NextJS 路由 (避免触发 TopLoader)
  const downloadImageWithoutNavigation = (
    imageUrl: string,
    fileName?: string
  ) => {
    // 显示下载开始提示
    const toastId = toast.loading('Downloading image...')

    // 创建一个新的 XMLHttpRequest
    const xhr = new XMLHttpRequest()
    xhr.open('GET', imageUrl, true)
    xhr.responseType = 'blob'

    // 添加进度事件
    xhr.onprogress = function (event) {
      if (event.lengthComputable) {
        const percentComplete = Math.round((event.loaded / event.total) * 100)
        // 更新下载进度提示
        toast.loading(`Downloading: ${percentComplete}%`, { id: toastId })
      }
    }

    xhr.onload = function () {
      if (this.status === 200) {
        // 创建一个 blob URL
        const blob = new Blob([this.response])
        const url = URL.createObjectURL(blob)

        // 创建下载链接
        const link = document.createElement('a')
        link.href = url
        link.download = fileName || `generated-image-${Date.now()}.png`
        document.body.appendChild(link)
        link.click()

        // 清理
        setTimeout(() => {
          document.body.removeChild(link)
          URL.revokeObjectURL(url)
        }, 100)

        // 显示下载成功提示
        toast.success('Download successful', { id: toastId })
      } else {
        // 显示下载失败提示
        toast.error('Download failed', { id: toastId })
      }
    }

    xhr.onerror = function () {
      // 显示下载失败提示
      toast.error('Download failed', { id: toastId })
    }

    xhr.send()
  }

  // 处理查看详情
  const handleViewDetails = (id: string) => {
    router.push(`/image-history/${id}`)
  }

  // 瀑布流的响应式断点
  const breakpointColumnsObj = {
    default: 4,
    1400: 3,
    1100: 2,
    700: 1,
  }

  // 根据比例获取图片容器的样式
  const getImageContainerStyle = (ratio: string) => {
    switch (ratio) {
      case '2:3':
        return 'aspect-[2/3]'
      case '1:1':
        return 'aspect-square'
      case '3:2':
      default:
        return 'aspect-[3/2]'
    }
  }

  return (
    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          {t('ImageHistory.title')}
        </h1>
        <p className="text-gray-500 mt-2">{t('ImageHistory.description')}</p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-lg">
          {error}
        </div>
      )}

      {records.length === 0 && !loading ? (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
          <h2 className="mt-4 text-lg font-medium text-gray-900">
            {t('ImageHistory.noRecords.title')}
          </h2>
          <p className="mt-1 text-gray-500">
            {t('ImageHistory.noRecords.description')}
          </p>
          <div className="mt-6">
            <Link
              href="/ai-generate-image"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {t('ImageHistory.noRecords.startGeneratingButton')}
            </Link>
          </div>
        </div>
      ) : (
        <Masonry
          breakpointCols={breakpointColumnsObj}
          className="flex -ml-4 w-auto"
          columnClassName="pl-4 bg-clip-padding"
        >
          {records.map((record) => (
            <div
              key={record.id}
              className="mb-4 group overflow-hidden bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
            >
              <div
                className={`relative ${getImageContainerStyle(
                  record.ratio
                )} overflow-hidden cursor-pointer`}
                onClick={() => handleViewDetails(record.id)}
              >
                {/* 检查是否有多张图片 */}
                {record.generated_image_url.includes('|') ? (
                  // 多图模式 - 显示网格
                  <div className="w-full h-full">
                    {(() => {
                      // 解析所有图片URL
                      const imageUrls = record.generated_image_url.split('|')
                      const imageCount = imageUrls.length

                      // 根据图片数量决定布局
                      if (imageCount === 2) {
                        // 1x2 网格
                        return (
                          <div className="grid grid-cols-2 h-full">
                            {imageUrls.map((url: string, idx: number) => (
                              <div key={idx} className="relative">
                                <img
                                  src={url}
                                  alt={`${record.prompt} ${idx + 1}`}
                                  className="w-full h-full object-cover"
                                  loading="lazy"
                                />
                              </div>
                            ))}
                          </div>
                        )
                      } else if (imageCount >= 4) {
                        // 2x2 网格
                        return (
                          <div className="grid grid-cols-2 grid-rows-2 h-full">
                            {imageUrls
                              .slice(0, 4)
                              .map((url: string, idx: number) => (
                                <div key={idx} className="relative">
                                  <img
                                    src={url}
                                    alt={`${record.prompt} ${idx + 1}`}
                                    className="w-full h-full object-cover"
                                    loading="lazy"
                                  />
                                </div>
                              ))}
                          </div>
                        )
                      } else {
                        // 默认显示第一张
                        return (
                          <img
                            src={imageUrls[0]}
                            alt={record.prompt}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                        )
                      }
                    })()}
                  </div>
                ) : (
                  // 单图模式 - 显示单张图片
                  <img
                    src={record.generated_image_url}
                    alt={record.prompt}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                )}

                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200"></div>

                {/* 操作按钮 */}
                <div className="absolute bottom-0 right-0 p-2 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button
                    onClick={(e) => {
                      e.stopPropagation() // 阻止事件冒泡
                      // 检查是否有多张图片
                      if (record.generated_image_url.includes('|')) {
                        // 下载所有图片
                        const imageUrls = record.generated_image_url.split('|')
                        const totalImages = imageUrls.length

                        // 显示开始下载提示
                        toast.success(`Downloading ${totalImages} images`)

                        // 下载所有图片
                        imageUrls.forEach((url, index) => {
                          // 添加延迟，避免浏览器阻止多次下载
                          setTimeout(() => {
                            downloadImageWithoutNavigation(
                              url,
                              `generated-image-${index + 1}-${Date.now()}.png`
                            )
                          }, index * 800) // 增加延迟，确保提示信息不会重叠
                        })
                      } else {
                        // 下载单张图片
                        downloadImageWithoutNavigation(
                          record.generated_image_url
                        )
                      }
                    }}
                    className="p-2 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors"
                    title={
                      record.generated_image_url.includes('|')
                        ? 'Download All'
                        : 'Download'
                    }
                  >
                    <Download className="h-5 w-5 text-gray-700" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation() // 阻止事件冒泡
                      handleViewDetails(record.id)
                    }}
                    className="p-2 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors"
                    title={t('ImageHistory.viewDetails')}
                  >
                    <Info className="h-5 w-5 text-gray-700" />
                  </button>
                </div>
              </div>

              <div
                className="p-3 cursor-pointer"
                onClick={() => handleViewDetails(record.id)}
              >
                <p className="text-sm text-gray-500 truncate mb-1">
                  {new Date(record.created_at).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </p>
                <p className="text-xs text-gray-800 line-clamp-2 overflow-hidden">
                  {record.prompt}
                </p>
              </div>
            </div>
          ))}
        </Masonry>
      )}

      {/* 加载更多指示器 */}
      {loading && (
        <div className="flex justify-center my-8">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900"></div>
        </div>
      )}

      {/* Intersection Observer 探测点 */}
      {!loading && hasMore && <div ref={ref} className="h-10" />}
    </div>
  )
}
