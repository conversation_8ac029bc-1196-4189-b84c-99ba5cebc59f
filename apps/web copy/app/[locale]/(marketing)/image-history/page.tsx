import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import ImageHistoryClient from './components/ImageHistoryClient'

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string }
}): Promise<Metadata> {
  const t = await getTranslations()

  return {
    title: t('ImageHistory.title'),
    description: t('ImageHistory.description'),
    openGraph: {
      title: t('ImageHistory.openGraph.title'),
      description: t('ImageHistory.openGraph.description'),
      type: 'website',
    },
  }
}

export default async function ImageHistoryPage() {
  return (
    <main className="min-h-screen px-8 pt-20 pb-24 bg-gradient-to-br from-indigo-50 to-blue-100">
      <ImageHistoryClient />
    </main>
  )
}
