import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import ImageDetailClient from './components/ImageDetailClient'
import { supabase } from '@/lib/supabaseClient'

// 获取记录数据
async function getImageRecord(id: string) {
  try {
    // 计算14天前的日期
    const fourteenDaysAgo = new Date()
    fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14)
    const fourteenDaysAgoStr = fourteenDaysAgo.toISOString()

    // 直接使用 Supabase 客户端获取数据
    const { data, error } = await supabase
      .from('img4o_image_generation_history')
      .select('*')
      .eq('id', id)
      .eq('is_deleted', false)
      .gte('created_at', fourteenDaysAgoStr) // 只获取14天内的记录
      .single()

    if (error || !data) {
      console.error('Supabase查询错误:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('获取记录数据失败:', error)
    return null
  }
}

export async function generateMetadata({
  params: { id },
}: {
  params: { id: string }
}): Promise<Metadata> {
  const record = await getImageRecord(id)

  if (!record) {
    return {
      title: 'Record Not Found',
      description:
        'The image generation record you are looking for does not exist or has been deleted.',
    }
  }

  // 使用提示词的前30个字符作为标题
  const titlePreview =
    record.prompt.length > 30
      ? `${record.prompt.substring(0, 30)}...`
      : record.prompt

  return {
    title: `Image Detail: ${titlePreview}`,
    description: record.prompt,
  }
}

export default async function ImageDetailPage({
  params: { id },
}: {
  params: { id: string }
}) {
  const record = await getImageRecord(id)

  if (!record) {
    notFound()
  }

  return (
    <main className="min-h-screen px-8 pt-20 pb-24 bg-gradient-to-br from-indigo-50 to-blue-100">
      <ImageDetailClient record={record} />
    </main>
  )
}
