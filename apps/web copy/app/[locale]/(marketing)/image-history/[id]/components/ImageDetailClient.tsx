'use client'

import { useRout<PERSON>, <PERSON> } from '@i18n/routing'
import { ArrowLeft, Download, Trash2 } from 'lucide-react'
import { useState } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@ui/components/alert-dialog'
import { useTranslations, useLocale } from 'next-intl'
import toast from 'react-hot-toast'

// 定义历史记录类型
type ImageGenerationRecord = {
  id: string
  user_id: string
  original_image_url: string // 可能包含多个URL，用 | 分隔
  generated_image_url: string // 可能包含多个URL，用 | 分隔
  prompt: string
  ratio: string
  created_at: string
  updated_at: string
  is_deleted: boolean
  extra_data: string | null
}

export default function ImageDetailClient({
  record,
}: {
  record: ImageGenerationRecord
}) {
  const t = useTranslations('ImageDetail')
  const router = useRouter()
  const currentLocale = useLocale()
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  // 处理下载图片 - 直接下载，不使用 NextJS 路由 (避免触发 TopLoader)
  const handleDownload = (imageUrl: string, fileName?: string) => {
    // 显示下载开始提示
    const toastId = toast.loading('Downloading image...')

    // 创建一个新的 XMLHttpRequest
    const xhr = new XMLHttpRequest()
    xhr.open('GET', imageUrl, true)
    xhr.responseType = 'blob'

    // 添加进度事件
    xhr.onprogress = function (event) {
      if (event.lengthComputable) {
        const percentComplete = Math.round((event.loaded / event.total) * 100)
        // 更新下载进度提示
        toast.loading(`Downloading: ${percentComplete}%`, { id: toastId })
      }
    }

    xhr.onload = function () {
      if (this.status === 200) {
        // 创建一个 blob URL
        const blob = new Blob([this.response])
        const url = URL.createObjectURL(blob)

        // 创建下载链接
        const link = document.createElement('a')
        link.href = url
        link.download = fileName || `generated-image-${Date.now()}.png`
        document.body.appendChild(link)
        link.click()

        // 清理
        setTimeout(() => {
          document.body.removeChild(link)
          URL.revokeObjectURL(url)
        }, 100)

        // 显示下载成功提示
        toast.success('Download successful', { id: toastId })
      } else {
        // 显示下载失败提示
        toast.error('Download failed', { id: toastId })
      }
    }

    xhr.onerror = function () {
      // 显示下载失败提示
      toast.error('Download failed', { id: toastId })
    }

    xhr.send()
  }

  // 处理删除记录
  const handleDelete = async () => {
    try {
      setIsDeleting(true)
      const response = await fetch(
        `/api/images/delete-history?id=${record.id}`,
        {
          method: 'DELETE',
        }
      )

      if (response.ok) {
        router.push('/image-history')
        router.refresh()
      } else {
        const error = await response.json()
        alert(t('failedToDelete', { error: error.error || 'unknown error' }))
      }
    } catch (error) {
      console.error('删除记录失败:', error)
      alert(t('deleteError'))
    } finally {
      setIsDeleting(false)
    }
  }

  // 解析创建时间
  const createdDate = new Date(record.created_at).toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })

  return (
    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 py-8">
      {/* 返回按钮 */}
      <Link
        href={'/image-history'}
        className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        {t('backToHistory')}
      </Link>

      <div className="bg-white rounded-xl shadow-sm overflow-hidden p-6">
        {/* 标题和操作 */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-1">
              {t('title')}
            </h1>
            <p className="text-sm text-gray-500">{createdDate}</p>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={() => setShowDeleteDialog(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              {t('delete')}
            </button>
            <button
              onClick={() => {
                // Check if there are multiple images
                if (record.generated_image_url.includes('|')) {
                  // Download all images
                  const imageUrls = record.generated_image_url.split('|')
                  const totalImages = imageUrls.length

                  // Show download start notification
                  toast.success(`Downloading ${totalImages} images`)

                  // Download all images
                  imageUrls.forEach((url: string, index: number) => {
                    setTimeout(() => {
                      handleDownload(
                        url,
                        `generated-image-${index + 1}-${Date.now()}.png`
                      )
                    }, index * 800) // Add delay to prevent overlapping notifications
                  })
                } else {
                  // Download single image
                  handleDownload(record.generated_image_url)
                }
              }}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="mr-2 h-4 w-4" />
              {record.generated_image_url.includes('|')
                ? 'Download All'
                : t('download')}
            </button>
          </div>
        </div>

        {/* 图片展示区域 */}
        <div className="mb-8">
          <div
            className={`grid gap-4 ${
              record.original_image_url
                ? 'grid-cols-1 md:grid-cols-2'
                : 'grid-cols-1'
            }`}
          >
            {/* 原始图片区域 - 只在有原始图片时显示 */}
            {record.original_image_url && (
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium text-gray-500">
                    {t('originalImage')}
                  </h3>
                </div>

                {/* 检查是否有多张原始图片 */}
                {record.original_image_url.includes('|') ? (
                  // 多图模式 - 显示网格
                  <div className="grid gap-2">
                    {(() => {
                      // 解析所有图片URL
                      const imageUrls = record.original_image_url.split('|')
                      const imageCount = imageUrls.length

                      // 根据图片数量决定布局
                      if (imageCount === 2) {
                        // 1x2 网格
                        return (
                          <div className="grid grid-cols-2 gap-2">
                            {imageUrls.map((url: string, idx: number) => (
                              <div key={idx} className="relative">
                                <img
                                  src={url}
                                  alt={`${t('originalImage')} ${idx + 1}`}
                                  className="max-h-[200px] w-full object-contain rounded"
                                />
                              </div>
                            ))}
                          </div>
                        )
                      } else if (imageCount >= 3) {
                        // 多图网格
                        return (
                          <div className="grid grid-cols-2 gap-2">
                            {imageUrls
                              .slice(0, 4)
                              .map((url: string, idx: number) => (
                                <div key={idx} className="relative">
                                  <img
                                    src={url}
                                    alt={`${t('originalImage')} ${idx + 1}`}
                                    className="max-h-[150px] w-full object-contain rounded"
                                  />
                                </div>
                              ))}
                          </div>
                        )
                      } else {
                        // 默认显示第一张
                        return (
                          <div className="aspect-auto flex items-center justify-center">
                            <img
                              src={imageUrls[0]}
                              alt={t('originalImage')}
                              className="max-h-[400px] max-w-full object-contain rounded"
                            />
                          </div>
                        )
                      }
                    })()}
                  </div>
                ) : (
                  // 单图模式 - 显示单张图片
                  <div className="aspect-auto flex items-center justify-center">
                    <img
                      src={record.original_image_url}
                      alt={t('originalImage')}
                      className="max-h-[400px] max-w-full object-contain rounded"
                    />
                  </div>
                )}
              </div>
            )}

            {/* 生成图片区域 */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-gray-500">
                  {t('generatedImage')}
                </h3>
              </div>

              {/* Check if there are multiple images */}
              {record.generated_image_url.includes('|') ? (
                // Multiple images mode - display grid
                <div className="grid gap-2">
                  {(() => {
                    // 解析所有图片URL
                    const imageUrls = record.generated_image_url.split('|')
                    const imageCount = imageUrls.length

                    // 根据图片数量决定布局
                    if (imageCount === 2) {
                      // 1x2 网格
                      return (
                        <div className="grid grid-cols-2 gap-2">
                          {imageUrls.map((url: string, idx: number) => (
                            <div key={idx} className="relative group">
                              <img
                                src={url}
                                alt={`${record.prompt} ${idx + 1}`}
                                className="max-h-[400px] w-full object-contain rounded"
                              />
                              <button
                                onClick={() =>
                                  handleDownload(
                                    url,
                                    `generated-image-${
                                      idx + 1
                                    }-${Date.now()}.png`
                                  )
                                }
                                className="absolute bottom-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors opacity-0 group-hover:opacity-100"
                                title="Download"
                              >
                                <Download className="h-4 w-4 text-gray-700" />
                              </button>
                            </div>
                          ))}
                        </div>
                      )
                    } else if (imageCount >= 4) {
                      // 2x2 网格
                      return (
                        <div className="grid grid-cols-2 gap-2">
                          {imageUrls
                            .slice(0, 4)
                            .map((url: string, idx: number) => (
                              <div key={idx} className="relative group">
                                <img
                                  src={url}
                                  alt={`${record.prompt} ${idx + 1}`}
                                  className="max-h-[200px] w-full object-contain rounded"
                                />
                                <button
                                  onClick={() =>
                                    handleDownload(
                                      url,
                                      `generated-image-${
                                        idx + 1
                                      }-${Date.now()}.png`
                                    )
                                  }
                                  className="absolute bottom-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors opacity-0 group-hover:opacity-100"
                                  title="Download"
                                >
                                  <Download className="h-4 w-4 text-gray-700" />
                                </button>
                              </div>
                            ))}
                        </div>
                      )
                    } else {
                      // 默认显示第一张
                      return (
                        <div className="relative group">
                          <img
                            src={imageUrls[0]}
                            alt={record.prompt}
                            className="max-h-[400px] max-w-full object-contain rounded"
                          />
                          <button
                            onClick={() => handleDownload(imageUrls[0])}
                            className="absolute bottom-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors opacity-0 group-hover:opacity-100"
                            title="Download"
                          >
                            <Download className="h-4 w-4 text-gray-700" />
                          </button>
                        </div>
                      )
                    }
                  })()}
                </div>
              ) : (
                // 单图模式 - 显示单张图片
                <div className="aspect-auto flex items-center justify-center relative group">
                  <img
                    src={record.generated_image_url}
                    alt={t('generatedImage')}
                    className="max-h-[400px] max-w-full object-contain rounded"
                  />
                  <button
                    onClick={() => handleDownload(record.generated_image_url)}
                    className="absolute bottom-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100 transition-colors opacity-0 group-hover:opacity-100"
                    title="Download"
                  >
                    <Download className="h-4 w-4 text-gray-700" />
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 参数信息 */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t('generationParameters')}
          </h2>

          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">
                {t('prompt')}
              </h3>
              <p className="mt-1 text-sm text-gray-900">{record.prompt}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  {t('ratio')}
                </h3>
                <p className="mt-1 text-sm text-gray-900">{record.ratio}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  {t('createdDate')}
                </h3>
                <p className="mt-1 text-sm text-gray-900">{createdDate}</p>
              </div>
            </div>

            {/* TODO 额外字段展示 */}
            {/* {record.extra_data && (
              <div>
                <h3 className="text-sm font-medium text-gray-500">其他信息</h3>
                <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">
                  {record.extra_data}
                </p>
              </div>
            )} */}
          </div>
        </div>

        {/* 删除确认对话框 */}
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogContent className="bg-white">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-red-600">
                {t('delete')}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {t('deleteConfirm')}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>{t('deleteCancel')}</AlertDialogCancel>
              <AlertDialogAction
                className="bg-red-600 hover:bg-red-700 focus:ring-red-600 text-white"
                onClick={handleDelete}
                disabled={isDeleting}
              >
                {isDeleting ? t('deleteLoading') : t('deleteButton')}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  )
}
