import { PostListItem } from '@marketing/blog/components/PostListItem'
import { allLearnPromptsPosts } from 'content-collections'
import { getLocale } from 'next-intl/server'

export async function generateMetadata() {
  return {
    title: 'Learn AI Prompt Engineering',
    description:
      'Master AI prompt engineering with our comprehensive tutorials and guides.',
  }
}

export default async function LearnPromptsListPage() {
  const locale = await getLocale()

  return (
    <div className="container max-w-6xl pt-32 pb-16">
      <div className="mb-12 text-center">
        <h1 className="font-bold text-4xl md:text-5xl leading-tight tracking-tight mb-4">
          Learn AI Prompt Engineering
        </h1>
        <p className="text-xl text-muted-foreground">
          Master the art of crafting effective AI prompts
        </p>
      </div>

      <div className="grid gap-8 md:grid-cols-2">
        {allLearnPromptsPosts
          .filter((post) => post.published && locale === post.locale)
          .sort(
            (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
          )
          .map((post) => (
            <PostListItem
              post={post}
              key={post.path}
              basePath="/learn-prompts"
            />
          ))}
      </div>
    </div>
  )
}
