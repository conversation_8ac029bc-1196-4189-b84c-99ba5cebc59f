'use client'

import { Button } from '@ui/components/button'
import { useMenuModal } from '@shared/hooks/useMenuModal'

export default function TestMenuPage() {
  const { openMenuModal } = useMenuModal()

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8">
            菜单模态框测试页面
          </h1>

          <div className="grid gap-8">
            {/* 单个分类测试 */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">单个分类菜单</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button
                  onClick={() => openMenuModal('business')}
                  variant="outline"
                  className="h-12"
                >
                  business单个分类
                </Button>
                <Button
                  onClick={() => openMenuModal('creative')}
                  variant="outline"
                  className="h-12"
                >
                  creative单个分类
                </Button>
                <Button
                  onClick={() => openMenuModal('imageTools')}
                  variant="outline"
                  className="h-12"
                >
                  imageTools单个分类
                </Button>
              </div>
            </div>

            {/* 按项目ID筛选测试 */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">
                按项目ID筛选菜单 🎯
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={() => openMenuModal(['product-video'])}
                  className="h-12 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  仅显示product-video + 不传入自定义标题
                </Button>
                <Button
                  onClick={() => openMenuModal(['photo-to-anime'])}
                  className="h-12 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  仅显示photo-to-anime
                </Button>
                <Button
                  onClick={() => openMenuModal(['ghibli'])}
                  className="h-12 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  仅显示ghibli
                </Button>
                <Button
                  onClick={() =>
                    openMenuModal(
                      ['product-video', 'ai-face-swap', 'ai-hug'],
                      {
                        title: '自定义标题',
                      }
                    )
                  }
                  className="h-12 bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700"
                >
                  product-video + ai-face-swap + ai-hug 自定义标题
                </Button>
              </div>
            </div>

            {/* 新功能测试 */}
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">自定义链接</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={() =>
                    openMenuModal(['product-video'], {
                      title: '自定义标题优先级测试',
                      viewMoreHref: '/custom-page',
                    })
                  }
                  className="h-12 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                >
                  自定义标题 + 链接
                </Button>
                <Button
                  onClick={() =>
                    openMenuModal(['product-video', 'ai-face-swap'], {
                      viewMoreHref: '/special-tools',
                      title: 'What a custom title!',
                    })
                  }
                  className="h-12 bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700"
                >
                  多项目 + 自定义链接
                </Button>
                <Button
                  onClick={() =>
                    openMenuModal(['product-video', 'ai-face-swap'],{
                      title: "What's New!",
                    })
                  }
                  className="h-12 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700"
                >
                  多项目默认设置
                </Button>
              </div>
            </div>

            {/* 说明文档 */}
            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3 text-blue-900">
                功能说明
              </h3>
              <div className="text-blue-800 space-y-2">
                <p>
                  ✅ <strong>单分类菜单</strong>：显示单个分类下的所有工具
                </p>
                <p>
                  ✅ <strong>按ID筛选菜单</strong>
                  ：根据项目ID精确显示特定工具
                </p>
                <p>
                  ✅ <strong>标题优先级</strong>：自定义标题 &gt; 单分类标题
                  &gt; 默认标题
                </p>
                <p>
                  ✅ <strong>键盘导航</strong>
                  ：支持ESC关闭、上下箭头选择、Enter确认
                </p>
                <p>
                  ✅ <strong>错误处理</strong>：优雅处理无效ID的情况
                </p>
              </div>
            </div>

            {/* 技术细节 */}
            <div className="bg-gray-100 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-3">技术实现</h3>
              <div className="text-gray-700 text-sm space-y-2">
                <p>
                  <strong>Hook:</strong> <code>useMenuModal()</code>
                </p>
                <p>
                  <strong>单分类:</strong>{' '}
                  <code>openMenuModal(categoryId)</code>
                </p>
                <p>
                  <strong>按ID筛选:</strong>{' '}
                  <code>openMenuModal(itemIds[])</code>
                </p>
                <p>
                  <strong>状态管理:</strong> Jotai atoms
                </p>
                <p>
                  <strong>组件:</strong> MultiCategoryMenuModalComponent
                </p>
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
  )
}
