import React from 'react'
import { useTranslations } from 'next-intl'

const FaqSection = () => {
  const t = useTranslations()

  const faqs = [
    {
      id: '01',
      question: t('pricingFaq.payment.question'),
      answer: t('pricingFaq.payment.answer'),
    },
    {
      id: '02',
      question: t('pricingFaq.subscription.question'),
      answer: t('pricingFaq.subscription.answer'),
    },
    {
      id: '03',
      question: t('pricingFaq.license.question'),
      answer: t('pricingFaq.license.answer'),
    },
    {
      id: '04',
      question: t('pricingFaq.freeGen.question'),
      answer: t('pricingFaq.freeGen.answer'),
    },
    {
      id: '05',
      question: t('pricingFaq.storage.question'),
      answer: t('pricingFaq.storage.answer'),
    },
    {
      id: '06',
      question: t('pricingFaq.private.question'),
      answer: t('pricingFaq.private.answer'),
    },
  ]

  return (
    <section className="max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-16">
        <h2 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-[#4B6BFB] to-[#2F4BFB] bg-clip-text text-transparent !leading-tight">
          {t('pricingFaq.title')}
        </h2>
        <p className="text-gray-600 text-lg md:text-xl">
          {t('pricingFaq.subtitle')}
        </p>
      </div>

      <div className="grid gap-6 max-w-4xl mx-auto">
        {faqs.map((faq) => (
          <div
            key={faq.id}
            className="bg-white rounded-xl p-6 border border-slate-200 hover:border-[#4B6BFB]/30 transition-colors"
          >
            <h3 className="flex gap-3 text-xl font-semibold mb-4">
              <span className="text-[#4B6BFB]">{faq.id}.</span>
              <span className="text-slate-800">{faq.question}</span>
            </h3>
            <p className="text-slate-600 leading-relaxed pl-9">{faq.answer}</p>
          </div>
        ))}
      </div>

      <div className="text-center mt-12 text-gray-600">
        <p>
          {t('pricingFaq.support.text')}{' '}
          <a
            href="mailto:<EMAIL>"
            className="text-[#4B6BFB] hover:text-[#2F4BFB] transition-colors"
          >
            <EMAIL>
          </a>
        </p>
      </div>

      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-[#4B6BFB] opacity-5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-72 h-72 bg-[#2F4BFB] opacity-5 rounded-full blur-3xl"></div>

        {/* 网格装饰 */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div
            className="h-full w-full"
            style={{
              backgroundImage:
                'linear-gradient(#4B6BFB 1px, transparent 1px), linear-gradient(to right, #4B6BFB 1px, transparent 1px)',
              backgroundSize: '32px 32px',
            }}
          ></div>
        </div>
      </div>
    </section>
  )
}

export default FaqSection
