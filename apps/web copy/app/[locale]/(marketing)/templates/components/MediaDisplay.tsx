'use client'

import { useRef, useState, useEffect } from 'react'
import Image from 'next/image'
import { TemplateCardProps } from './TemplateCard'
import BeforeAfterSlider from '@/[locale]/components/BeforeAfterSlide'

interface MediaDisplayProps {
  mediaUrl: string
  title: string
  isHovered: boolean
  item: TemplateCardProps['item']
}

const MediaDisplay = ({ mediaUrl = '', title, isHovered, item }: MediaDisplayProps) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  const isVideo = mediaUrl.includes('.mp4') || mediaUrl.includes('.webm') || mediaUrl.includes('video')
  const isBackgroundRemove = item.bgRemove

  // 检测是否为移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 处理视频播放/暂停
  useEffect(() => {
    if (!isVideo || !videoRef.current) return

    const video = videoRef.current

    if (isHovered && isLoaded) {
      // 鼠标悬停时播放
      video.play().catch(() => {
        // 播放失败时静默处理
      })
    } else {
      // 鼠标离开时暂停并重置到开始
      video.pause()
      video.currentTime = 0
    }
  }, [isHovered, isLoaded, isVideo])

  // 视频加载完成
  const handleVideoLoad = () => {
    setIsLoaded(true)
  }

  if (isVideo) {
    return (
      <div className="relative aspect-video w-full h-36 sm:h-40 md:h-52 overflow-hidden rounded-lg bg-gray-800">
        <video
          ref={videoRef}
          className="w-full h-full object-cover"
          muted
          loop
          playsInline
          preload="metadata"
          onLoadedData={handleVideoLoad}
          poster={`${mediaUrl}#t=0.1`} // 显示视频第一帧作为封面
        >
          <source src={mediaUrl} type="video/mp4" />
        </video>

        {/* 播放指示器 - 仅在非悬停状态显示 */}
        {/* {!isHovered && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
              <svg 
                className="w-6 h-6 sm:w-8 sm:h-8 text-white ml-1" 
                fill="currentColor" 
                viewBox="0 0 24 24"
              >
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
          </div>
        )} */}
      </div>
    )
  }

  if (isBackgroundRemove) {
    return <BeforeAfterSlider className='sm:!h-40 select-none md:!h-52 overflow-hidden rounded-lg' beforeImage={item.beforeImage || ''} afterImage={item.afterImage || ''} />
  }

  return (
    <div className="relative w-full h-full sm:h-40 md:h-52 overflow-hidden rounded-lg">
      <Image
        src={mediaUrl}
        alt={title}
        fill
        className="object-cover absolute z-10 inset-0 w-full h-full transition-transform duration-300 group-hover:scale-105"
        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
      />
      <Image
        src={mediaUrl}
        alt={title}
        fill
        className="object-cover absolute z-0 blur-sm inset-0 w-full h-full transition-transform duration-300 group-hover:scale-105"
        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
      />
    </div>
  )
}

export default MediaDisplay
