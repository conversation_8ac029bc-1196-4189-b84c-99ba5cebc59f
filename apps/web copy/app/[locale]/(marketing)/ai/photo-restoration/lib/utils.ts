import { CreateHistoryRequest, HistoryItem } from '@/types/history'
import toast from 'react-hot-toast'

/**
 * 保存生成历史记录
 * @param historyData - 历史记录数据
 * @returns Promise<void>
 */
export const saveGenerationHistory = async (
  historyData: CreateHistoryRequest
): Promise<HistoryItem> => {
  try {
    const response = await fetch('/api/history/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(historyData),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    // 可选：处理响应数据
    const result = await response.json()
    console.log('Generation history saved successfully:', result)
    return result.data as HistoryItem // 返回创建的记录数据
  } catch (error) {
    console.error('Failed to save generation history:', error)
    throw error // 重新抛出错误，让调用方决定如何处理
  }
}

/**
 * 更新生成历史记录
 * @param id - 历史记录ID
 * @param userId - 用户ID
 * @param updateData - 更新数据
 * @returns Promise<void>
 */
export const updateGenerationHistory = async (
  id: number,
  userId: string,
  updateData: {
    status?: 'PENDING' | 'SUCCESS' | 'FAILED' | 'PROCESSING'
    inputParams?: Record<string, any>
    resultData?: Record<string, any>
    metadata?: Record<string, any>
    errorMessage?: string
    completedAt?: string
  }
): Promise<void> => {
  try {
    const response = await fetch('/api/history/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        id,
        userId,
        ...updateData,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log('Generation history updated successfully:', result)
  } catch (error) {
    console.error('Failed to update generation history:', error)
    throw error
  }
}

/**
 * 下载单个图片
 * @param imageUrl - 图片URL（可选，如果未提供则使用 fallbackUrl）
 * @param fallbackUrl - 备用图片URL
 * @param index - 图片索引（可选，用于生成文件名）
 * @returns Promise<void>
 */
export const downloadImage = async (
  imageUrl?: string,
  fallbackUrl?: string | null,
  index?: number
): Promise<void> => {
  const urlToDownload = imageUrl || fallbackUrl
  if (!urlToDownload) return

  // Generate filename based on whether index is provided
  const timestamp = Date.now()
  let filename
  if (index !== undefined) {
    // If index is provided, use it in the filename
    filename = `generated-image-${index + 1}-${timestamp}.png`
  } else {
    // Otherwise use default naming
    filename = `generated-image-${timestamp}.png`
  }

  try {
    // Use fetch API to download the image directly
    const response = await fetch(
      `/api/download?url=${encodeURIComponent(
        urlToDownload
      )}&filename=${encodeURIComponent(filename)}`,
      {
        method: 'GET',
      }
    )

    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`)
    }

    // Get the blob from the response
    const blob = await response.blob()

    // Create a download link for the blob
    const blobUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = filename

    // Trigger download
    document.body.appendChild(link)
    link.click()

    // Clean up
    document.body.removeChild(link)
    window.URL.revokeObjectURL(blobUrl)
  } catch (error) {
    console.error('Error downloading image:', error)
    toast.error('Failed to download image. Please try again.')
    throw error
  }
}

/**
 * 批量下载图片（带延迟以避免浏览器阻止）
 * @param imageUrls - 图片URL数组
 * @param delay - 每次下载之间的延迟时间（毫秒，默认800ms）
 * @returns Promise<void>
 */
export const downloadAllImages = async (
  imageUrls: string[],
  delay: number = 800
): Promise<void> => {
  if (!imageUrls.length) return

  // Display download start notification
  const toastId = toast.loading(`Downloading images (0/${imageUrls.length})...`)

  try {
    // Download each image with a delay between downloads
    for (let i = 0; i < imageUrls.length; i++) {
      const url = imageUrls[i]
      const timestamp = Date.now()
      const filename = `generated-image-${i + 1}-${timestamp}.png`

      // Update download progress notification
      toast.loading(`Downloading images (${i + 1}/${imageUrls.length})...`, {
        id: toastId,
      })

      try {
        // Use fetch API to download the image directly
        const response = await fetch(
          `/api/download?url=${encodeURIComponent(
            url
          )}&filename=${encodeURIComponent(filename)}`,
          {
            method: 'GET',
          }
        )

        if (!response.ok) {
          throw new Error(`Download failed: ${response.statusText}`)
        }

        // Get the blob from the response
        const blob = await response.blob()

        // Create a download link for the blob
        const blobUrl = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = blobUrl
        link.download = filename

        // Trigger download
        document.body.appendChild(link)
        link.click()

        // Clean up
        document.body.removeChild(link)
        window.URL.revokeObjectURL(blobUrl)
      } catch (error) {
        console.error(`Error downloading image ${i + 1}:`, error)
        throw error // Re-throw to be caught by the outer try-catch
      }

      // Add delay to avoid browser blocking
      if (i < imageUrls.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, delay))
      }
    }

    // Download complete notification
    toast.success(`Successfully downloaded ${imageUrls.length} images`, {
      id: toastId,
    })
  } catch (error) {
    // Download error notification
    toast.error('Error occurred during download. Please try again.', {
      id: toastId,
    })
    console.error('Error downloading images:', error)
    throw error
  }
}

/**
 * 客户端直接上传文件到 OSS
 * @param file - 要上传的文件
 * @returns Promise<string> - 返回 OSS URL
 */
export const clientDirectUpload = async (file: File): Promise<string> => {
  try {
    console.log('Using client direct upload method')
    // 1. 获取签名 URL
    const signatureResponse = await fetch('/api/upload/get-signature', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        filename: file.name,
        contentType: file.type,
      }),
    })

    if (!signatureResponse.ok) {
      console.error(
        'Failed to get upload signature, status:',
        signatureResponse.status
      )
      throw new Error('Failed to get upload signature')
    }

    const signatureData = await signatureResponse.json()

    if (signatureData.code !== 200) {
      console.error('Signature API returned error:', signatureData.msg)
      throw new Error(signatureData.msg || 'Failed to get upload signature')
    }

    const { url: signedUrl, ossUrl } = signatureData.data

    if (!signedUrl || !ossUrl) {
      console.error('Invalid signature data:', signatureData)
      throw new Error('Invalid signature data received')
    }

    // 2. 使用签名 URL 直接上传文件到 OSS
    const uploadResponse = await fetch(signedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    })

    if (!uploadResponse.ok) {
      console.error('Failed to upload to OSS, status:', uploadResponse.status)
      throw new Error('Upload failed')
    }

    // 3. 返回 OSS URL
    return ossUrl
  } catch (error) {
    console.error('Client direct upload failed:', error)
    throw new Error('Client direct upload failed')
  }
}

/**
 * 上传单个文件到 OSS
 * @param file - 要上传的文件
 * @returns Promise<string> - 返回 OSS URL
 */
export const uploadToOSS = async (file: File): Promise<string> => {
  try {
    // 使用客户端直接上传
    return await clientDirectUpload(file)
  } catch (error) {
    console.error('Failed to upload to OSS:', error)
    throw new Error('Failed to upload image')
  }
}

/**
 * 上传多个图片到 OSS
 * @param files - 要上传的文件数组
 * @returns Promise<string[]> - 返回 OSS URL 数组
 */
export const uploadMultipleToOSS = async (files: File[]): Promise<string[]> => {
  const uploadPromises = files.map((file) => uploadToOSS(file))
  return Promise.all(uploadPromises)
}
