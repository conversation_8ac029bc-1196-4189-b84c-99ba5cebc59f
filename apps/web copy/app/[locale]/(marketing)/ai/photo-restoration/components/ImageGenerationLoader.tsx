'use client'

import React, { useState, useEffect, useMemo } from 'react'
import styles from './index.module.css'
import { useTranslations } from 'next-intl'

const ImageGenerationLoader = ({ progress = 0 }) => {
  const [phaseIndex, setPhaseIndex] = useState(0)
  const t = useTranslations()
  // 使用 useMemo 缓存计算后的进度值
  const displayProgress = useMemo(() => Math.max(5, progress), [progress])

  const generationPhases = [
    { name: t('ImageGenerator.generationPhases.0'), emoji: '🔍' },
    { name: t('ImageGenerator.generationPhases.1'), emoji: '🎨' },
    { name: t('ImageGenerator.generationPhases.2'), emoji: '🏗️' },
    { name: t('ImageGenerator.generationPhases.3'), emoji: '✨' },
    { name: t('ImageGenerator.generationPhases.4'), emoji: '⚡' },
    { name: t('ImageGenerator.generationPhases.5'), emoji: '🌟' },
  ]

  // Phase cycling animation
  useEffect(() => {
    const phaseInterval = setInterval(() => {
      setPhaseIndex((prev) => (prev + 1) % generationPhases.length)
    }, 2800)

    return () => clearInterval(phaseInterval)
  }, [])

  return (
    <div className="flex flex-col items-center w-full max-w-md mx-auto p-3 sm:p-4 md:p-6">
      {/* Title with animation */}
      <h3 className="text-base sm:text-lg md:text-xl font-bold text-gray-800 mb-3 sm:mb-4 md:mb-6 flex items-center">
        {t('ImageGenerator.generationPhasesDesc')}
        <span className="ml-1 sm:ml-2 inline-block animate-pulse">
          {generationPhases[phaseIndex].emoji}
        </span>
      </h3>

      {/* Moving particles progress bar */}
      <div className="w-full h-6 sm:h-7 md:h-8 bg-blue-100 rounded-md sm:rounded-lg mb-3 sm:mb-4 md:mb-6 overflow-hidden relative">
        {/* Main progress bar */}
        <div
          className="h-full bg-gradient-to-r from-blue-400 to-blue-600 transition-all duration-300"
          style={{ width: `${displayProgress}%` }}
        />

        {/* Animated particles to create rushing effect */}
        <div className="absolute top-0 left-0 w-full h-full">
          {[...Array(10)].map((_, i) => (
            <div
              key={i}
              className={`absolute h-3 sm:h-4 w-1 bg-white opacity-70 rounded-full ${styles.movingParticle}`}
              style={{
                left: `${i * 10 + Math.random() * 5}%`,
                animationDelay: `${i * 0.2}s`,
                animationDuration: `${1 + Math.random()}s`,
              }}
            />
          ))}
        </div>

        {/* Progress percentage */}
        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
          <span className="text-xs sm:text-sm font-bold text-white drop-shadow-md">
            {Math.floor(displayProgress)}%
          </span>
        </div>
      </div>

      {/* Current processing phase with animation */}
      <div className="text-center w-full">
        <div className="text-xs sm:text-sm font-medium text-gray-700 animate-fade-in-out">
          {generationPhases[phaseIndex].name}...
        </div>
      </div>
    </div>
  )
}

export default ImageGenerationLoader
