'use client'

import { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { AlertCircle, Sparkles } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useAtom, useSet<PERSON>tom } from 'jotai'
import { Button } from '@ui/components/button'
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormLabel,
} from '@ui/components/form'
import { Switch } from '@ui/components/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
//
import {
  imagesAtom,
  formDataAtom,
  generatedTaskIdAtom,
  formSchema,
  type FormData,
  generationProgressAtom,
  isGeneratingAtom,
  generationErrorAtom,
  generatedImageUrl<PERSON>tom,
  generatedImageUrls<PERSON>tom,
  currentTaskIdAtom,
  promptAtom,
} from '../lib/state'
import { useModal } from '@shared/hooks/useModal'
import { getUserIdFromCookie } from '@/utils/lib'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useTranslations } from 'next-intl'
import { TASK_TYPES } from '@/../constants'
import { consumePointsAtom } from '@marketing/stores'
import { CreateHistoryRequest } from '@/types/history'
import {
  saveGenerationHistory,
  updateGenerationHistory,
  uploadToOSS,
  uploadMultipleToOSS,
} from '../lib/utils'
import { ImageUploadArea } from './ImageUploadArea'
import PointIcon from '../../components/ui/PointIcon'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'

// API related configuration
const API_URL_GENERATE = '/api/images/generate/photo-restoration'
const API_URL_STATUS = '/api/images/record-info'
const POLL_INTERVAL = 5000
const MAX_POLL_TIME = 600000 // 5 minutes
const DEFAULT_MAX_UPLOAD_IMAGES = 1

type ImageStyleConverterProps = {
  onGenerated?: (generatedUrl: string) => void
  onStartGenerating?: () => void
  onError?: () => void
  maxUploadImages?: number
}

export function ImageStyleConverter({
  onGenerated,
  onStartGenerating,
  onError,
  maxUploadImages = DEFAULT_MAX_UPLOAD_IMAGES,
}: ImageStyleConverterProps) {
  const t = useTranslations()
  const router = useRouter()
  // 使用整合的图片数据
  const [images, setImages] = useAtom(imagesAtom)
  const [prompt] = useAtom(promptAtom)
  const { showLoginModal, showInsufficientCreditsModal } = useModal()
  const [formData, setFormData] = useAtom(formDataAtom)
  const [generatedTaskId, setGeneratedTaskId] = useAtom(generatedTaskIdAtom)

  const user = getUserFromClientCookies()
  const generationMode = TASK_TYPES.PHOTO_RESTORATION

  // 默认表单数据
  const defaultFormValues: FormData = {
    prompt: 'Restore and enhance this old photo',
    ratio: '1:1',
    nVariants: '1',
    colorize: false,
    restoration: true,
    removeScratchesAndCracks: false,
  }

  // Form configuration - 只使用默认值初始化表单
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  })

  const { calculatePoints, updateUserInfo } = usePermissionCheck()

  // Consume points
  const consumePoints = useSetAtom(consumePointsAtom)

  // Use global state
  const [isGenerating, setIsGenerating] = useAtom(isGeneratingAtom)
  const [, setGenerationProgress] = useAtom(generationProgressAtom)
  const [generationError, setGenerationError] = useAtom(generationErrorAtom)
  const [, setGeneratedImageUrl] = useAtom(generatedImageUrlAtom)
  const [, setGeneratedImageUrls] = useAtom(generatedImageUrlsAtom)

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTaskId, setCurrentTaskId] = useAtom(currentTaskIdAtom)
  const [currentHistoryId, setCurrentHistoryId] = useState<number | null>(null)
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number | null>(null)

  // Create initial history data (PENDING status)
  const createInitialHistoryData = (
    taskId: string,
    originalImageUrls: string[]
  ): CreateHistoryRequest => {
    const userId = getUserIdFromCookie()
    return {
      userId,
      taskType: TASK_TYPES.PHOTO_RESTORATION,
      externalTaskId: taskId,
      status: 'PENDING',
      inputParams: {
        prompt: form.getValues('prompt'), // 使用原始prompt
        ratio: form.getValues('ratio'),
        nVariants: form.getValues('nVariants'),
        colorize: form.getValues('colorize'),
        restoration: form.getValues('restoration'),
        removeScratchesAndCracks: form.getValues('removeScratchesAndCracks'),
        originalImageUrls: originalImageUrls,
        generationMode: generationMode,
        selectedStyle: null,
      },
      metadata: {
        description: 'Image generation task started',
        originalImageCount: originalImageUrls.length,
      },
    }
  }

  const resetLoading = useCallback(() => {
    setIsGenerating(false)
    setGenerationProgress(0)
    setGeneratedTaskId(null)
    setIsSubmitting(false)
    setCurrentTaskId(null)
    setCurrentHistoryId(null)
    setGeneratedImageUrl(null)
    setGeneratedImageUrls([])
    onError?.()
  }, [
    setIsGenerating,
    setGenerationProgress,
    setGeneratedTaskId,
    setIsSubmitting,
    setCurrentTaskId,
    setGeneratedImageUrl,
    setGeneratedImageUrls,
    onError,
  ])

  // Start polling task status
  const startPolling = useCallback(
    (id: string, originalImageUrls: string[]) => {
      // If this task has already been successfully generated, return directly
      if (generatedTaskId === id) {
        setIsSubmitting(false)
        setCurrentTaskId(null)
        return
      }

      // 确保每次开始轮询时都重置开始时间
      startTimeRef.current = Date.now()
      console.log(
        '开始轮询任务:',
        id,
        '开始时间:',
        new Date(startTimeRef.current).toLocaleString()
      )

      const pollForResult = async () => {
        try {
          const response = await fetch(`${API_URL_STATUS}?taskId=${id}`, {
            method: 'GET',
            headers: {
              Accept: 'application/json',
            },
          })

          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`)
          }

          const data = await response.json()

          if (data.code === 200) {
            if (data.data.status === 'SUCCESS' && data.data.successFlag === 1) {
              // Generation successful
              const resultUrls = data.data.response.resultUrls || []

              // 保存所有生成的图片URL
              setGeneratedImageUrls(resultUrls)

              // 为了兼容性，仍然设置第一张图片为主图片
              const resultUrl = resultUrls.length > 0 ? resultUrls[0] : null
              setIsGenerating(false)
              setGenerationProgress(100)
              setGeneratedTaskId(id)

              if (resultUrls.length > 0) {
                // 设置第一张图片为主图片（兼容旧版本）
                if (resultUrl) {
                  setGeneratedImageUrl(resultUrl)
                }

                // Update history record with successful results
                if (currentHistoryId) {
                  try {
                    await updateGenerationHistory(
                      currentHistoryId,
                      getUserIdFromCookie(),
                      {
                        status: 'SUCCESS',
                        resultData: {
                          generatedImageUrls: resultUrls,
                          originalImageUrls: originalImageUrls,
                        },
                        metadata: {
                          description:
                            'Image generation task completed successfully',
                          originalImageCount: originalImageUrls.length,
                          generatedImageCount: resultUrls.length,
                        },
                        completedAt: new Date().toISOString(),
                      }
                    )
                  } catch (error) {
                    console.error('Failed to update history record:', error)
                  }
                }

                if (onGenerated && resultUrl) {
                  onGenerated(resultUrl)
                }
              }

              setIsSubmitting(false)
              setCurrentTaskId(null)

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else if (
              data.data.status === 'FAILED' ||
              (data.data.status === 'GENERATE_FAILED' &&
                data.data.successFlag === 3)
            ) {
              // Generation failed - update history record
              const errorMessage = `Generation failed: ${
                data.data.errorMessage || 'Unknown error'
              }`

              if (currentHistoryId) {
                try {
                  await updateGenerationHistory(
                    currentHistoryId,
                    getUserIdFromCookie(),
                    {
                      status: 'FAILED',
                      errorMessage: errorMessage,
                      metadata: {
                        description: 'Image generation task failed',
                        originalImageCount: originalImageUrls.length,
                        errorDetails: data.data.errorMessage || 'Unknown error',
                      },
                      completedAt: new Date().toISOString(),
                    }
                  )
                } catch (error) {
                  console.error(
                    'Failed to update failed history record:',
                    error
                  )
                }
              }

              setGenerationError(errorMessage)
              resetLoading()

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else {
              // Still processing, update progress
              const elapsed = Date.now() - (startTimeRef.current || Date.now())
              const progress = Number(data.data.progress) * 100

              // 确保进度只能向前增长，不会倒退
              setGenerationProgress((currentProgress) => {
                const newProgress = Math.max(currentProgress, progress)
                return newProgress
              })

              // Check if timeout
              if (elapsed > MAX_POLL_TIME) {
                console.log('Generation timed out, please try again')
                setGenerationError(t('ImageStyleConverter.generationTimeout'))
                resetLoading()

                if (pollIntervalRef.current) {
                  clearInterval(pollIntervalRef.current)
                  pollIntervalRef.current = null
                }
              }
            }
          } else {
            throw new Error(data.msg || 'Request failed')
          }
        } catch (err) {
          console.error('Error polling for result:', err)
          setGenerationError(t('ImageStyleConverter.generationError'))
          resetLoading()

          if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current)
            pollIntervalRef.current = null
          }
        }
      }

      // Execute immediately, then set timer
      pollForResult()
      pollIntervalRef.current = setInterval(pollForResult, POLL_INTERVAL)
    },
    [
      onGenerated,
      setGeneratedTaskId,
      generatedTaskId,
      resetLoading,
      setIsGenerating,
      setGenerationProgress,
      setGenerationError,
      setGeneratedImageUrl,
    ]
  )

  // Check if form is valid
  const isFormValid = useMemo(() => {
    return (
      images.length > 0 &&
      (form.watch('colorize') ||
        form.watch('restoration') ||
        form.watch('removeScratchesAndCracks'))
    )
  }, [images, form])

  // Form submission handling
  const onSubmit = useCallback(
    async (values: FormData) => {
      if (!values.prompt.trim()) {
        setGenerationError(t('ImageStyleConverter.promptRequired'))
        return
      }

      // Check points first
      if (!user) {
        showLoginModal({
          title: t('loginTipsTitle'),
          content: t('tipLogin'),
          props: {
            needBottomArea: true, // 显示会员权益
          },
        })
        return
      }

      // If already submitting or there's an ongoing task, don't submit again
      if (isSubmitting || currentTaskId) {
        return
      }

      setGenerationError(null)
      setIsSubmitting(true)
      setIsGenerating(true)

      // 只有在没有当前任务ID时才重置进度为5%（表示这是一个新任务）
      if (!currentTaskId) {
        setGenerationProgress(5)
      }

      onStartGenerating?.()

      try {
        let filesUrl: string[] = []

        // 收集所有已上传完成的图片的 OSS URL
        const ossUrls = images
          .filter((image) => image.ossUrl) // 只选择已有 OSS URL 的图片
          .map((image) => image.ossUrl as string)

        // 检查是否所有图片都已上传完成
        if (ossUrls.length === images.length) {
          // 所有图片都已上传完成，直接使用 OSS URL
          filesUrl = ossUrls
        } else {
          // 如果有图片还未上传完成，收集所有图片文件对象
          const files = images
            .filter((image) => image.file) // 只选择有文件对象的图片
            .map((image) => image.file as File)

          // 重新上传所有图片
          filesUrl = await uploadMultipleToOSS(files)
        }

        const response = await fetch(API_URL_GENERATE, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify({
            filesUrl,
            prompt: values.prompt, // 传递原始prompt，后端会根据功能选择生成最终prompt
            size: values.ratio,
            callBackUrl: 'https://your-callback-url.com/callback',
            mode: generationMode, // 添加生成模式参数
            nVariants: values.nVariants || '1', // 添加变体数量参数
            colorize: values.colorize,
            restoration: values.restoration,
            removeScratchesAndCracks: values.removeScratchesAndCracks,
          }),
        })

        if (!response.ok) {
          console.log(`HTTP error! Status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 401000) {
          showLoginModal({
            title: t('loginTipsTitle'),
            content: t('tipLogin'),
          })
          resetLoading()
          return
        }

        if (
          data.code === 400000 &&
          data.message?.en === 'Insufficient points.'
        ) {
          resetLoading()
          showInsufficientCreditsModal({
            content: t('ImageStyleConverter.insufficientPoints'),
          })
          return
        }

        if (data.code === 100000) {
          const newTaskId = data.data.taskId
          setCurrentTaskId(newTaskId)
          updateUserInfo()
          // Save initial history record with PENDING status
          // try {
          //   const initialHistoryData = createInitialHistoryData(
          //     newTaskId,
          //     filesUrl
          //   )
          //   const historyRecord = await saveGenerationHistory(
          //     initialHistoryData
          //   )
          //   setCurrentHistoryId(historyRecord.id)
          // } catch (error) {
          //   console.error('Failed to save initial history record:', error)
          // }

          startPolling(newTaskId, filesUrl)
        } else {
          throw new Error(data.msg || 'Request failed')
        }
      } catch (err) {
        console.error('Generation request failed:', err)
        setGenerationError(
          'Failed to initiate generation request, please try again'
        )
        resetLoading()
      }
    },
    [
      images,
      router,
      setFormData,
      onStartGenerating,
      startPolling,
      resetLoading,
      isSubmitting,
      showLoginModal,
      showInsufficientCreditsModal,
      user,
      t,
      consumePoints,
    ]
  )

  // 包装上传方法以处理错误状态
  const handleUploadToOSS = async (file: File): Promise<string> => {
    try {
      return await uploadToOSS(file)
    } catch (error) {
      console.error('Failed to upload to OSS:', error)
      setGenerationError('Failed to upload image, please try again')
      throw error
    }
  }

  // Listen for form changes
  useEffect(() => {
    const subscription = form.watch((values, { name }) => {
      console.log('Form value changed:', name, values)

      if (
        name === 'prompt' ||
        name === 'ratio' ||
        name === 'nVariants' ||
        name === 'colorize' ||
        name === 'restoration' ||
        name === 'removeScratchesAndCracks'
      ) {
        // 保存表单数据
        const formData = {
          prompt: values.prompt || 'Restore and enhance this old photo',
          ratio: values.ratio || '3:2',
          nVariants: values.nVariants || '1',
          colorize: values.colorize ?? true,
          restoration: values.restoration ?? true,
          removeScratchesAndCracks: values.removeScratchesAndCracks ?? false,
        }
        // 同时更新 formData atom
        setFormData(formData)
      }
    })
    return () => subscription.unsubscribe()
  }, [form, setFormData])

  // Listen for image changes and auto-adjust ratio
  useEffect(() => {
    console.log('images:', images)
    if (images.length > 0 && images[0].width && images[0].height) {
      const { width, height } = images[0]
      const ratio = width / height

      // Preset ratio actual values
      const ratioValues = {
        '3:2': 1.5,
        '2:3': 0.667,
        '1:1': 1,
      } as const

      // Find closest ratio
      let closestRatio: '3:2' | '2:3' | '1:1' = '1:1'
      let minDiff = Infinity

      Object.entries(ratioValues).forEach(([key, value]) => {
        const diff = Math.abs(ratio - value)
        if (diff < minDiff) {
          minDiff = diff
          closestRatio = key as '3:2' | '2:3' | '1:1'
        }
      })

      // Only update if the current ratio is different
      const currentRatio = form.getValues('ratio')
      if (currentRatio !== closestRatio) {
        console.log('自动设置比例:', closestRatio)
        form.setValue('ratio', closestRatio)
      }
    }
  }, [images, form])

  // Clean up polling
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current)
      }
    }
  }, [])

  return (
    <>
      <div className="w-full h-full image-style-converter">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col h-full"
          >
            <div className="flex-shrink-0 mb-6">
              <ImageUploadArea
                images={images}
                onImagesChange={setImages}
                maxUploadImages={maxUploadImages}
                isGenerating={isGenerating}
                allowDelete={false}
                onUploadStart={async (file: File, index: number) => {
                  try {
                    // 上传文件到 OSS
                    const ossUrl = await handleUploadToOSS(file)

                    // 更新图片数据
                    setImages((prevImages) => {
                      const updatedImages = [...prevImages]
                      if (index < updatedImages.length) {
                        updatedImages[index] = {
                          ...updatedImages[index],
                          ossUrl,
                          uploading: false,
                        }

                        // 如果是第一张图片且有尺寸信息，自动设置比例
                        if (
                          index === 0 &&
                          updatedImages[index].width &&
                          updatedImages[index].height
                        ) {
                          const { width, height } = updatedImages[index]
                          const ratio = width! / height!

                          // Preset ratio actual values
                          const ratioValues = {
                            '3:2': 1.5,
                            '2:3': 0.667,
                            '1:1': 1,
                          } as const

                          // Find closest ratio
                          let closestRatio: '3:2' | '2:3' | '1:1' = '1:1'
                          let minDiff = Infinity

                          Object.entries(ratioValues).forEach(
                            ([key, value]) => {
                              const diff = Math.abs(ratio - value)
                              if (diff < minDiff) {
                                minDiff = diff
                                closestRatio = key as '3:2' | '2:3' | '1:1'
                              }
                            }
                          )

                          // Set form ratio value
                          form.setValue('ratio', closestRatio)
                        }
                      }
                      return updatedImages
                    })
                  } catch (error) {
                    // 更新图片数据，标记错误
                    setImages((prevImages) => {
                      const updatedImages = [...prevImages]
                      if (index < updatedImages.length) {
                        updatedImages[index] = {
                          ...updatedImages[index],
                          uploading: false,
                          error: t('ImageStyleConverter.uploadFailed'),
                        }
                      }
                      return updatedImages
                    })
                    console.error(`Upload failed at index ${index}:`, error)
                  }
                }}
              />
            </div>

            <div className="overflow-y-auto">
              {/* Hidden ratio field - automatically set based on image dimensions */}
              <FormField
                control={form.control}
                name="ratio"
                render={({ field }) => (
                  <FormItem className="hidden">
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="3:2">3:2</SelectItem>
                          <SelectItem value="2:3">2:3</SelectItem>
                          <SelectItem value="1:1">1:1</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Image Settings Switches */}
              <div className="space-y-3 mb-6">
                {/* AI Photo Colorizer */}
                <p className="text-lg font-medium text-gray-900">
                  Image Settings
                </p>

                <FormField
                  control={form.control}
                  name="colorize"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base font-medium text-gray-900">
                          AI Photo Colorizer
                        </FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isGenerating}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* AI Old Photo Restoration */}
                <FormField
                  control={form.control}
                  name="restoration"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base font-medium text-gray-900">
                          AI Old Photo Restoration
                        </FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isGenerating}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* AI Remove Scratches/Creases */}
                <FormField
                  control={form.control}
                  name="removeScratchesAndCracks"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base font-medium text-gray-900">
                          AI Remove Scratches/Creases
                        </FormLabel>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isGenerating}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Generate button and progress */}
            <div className="space-y-4 flex-shrink-0">
              <Button
                type="submit"
                className="w-full bg-gradient-to-r gap-1 from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200"
                disabled={!isFormValid}
                loading={isGenerating}
                size="lg"
              >
                {isGenerating
                  ? t('ImageStyleConverter.generating')
                  : t('ImageStyleConverter.generateButton')}

                <PointIcon points={calculatePoints()} />
              </Button>

              {generationError && (
                <div
                  className={`bg-red-50 p-3 rounded-md flex items-start ${'bg-opacity-80'}`}
                >
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-600">{generationError}</p>
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
    </>
  )
}
