'use client'

import { ImageStyleConverter } from './ImageStyleConverter'
import ImageHistory from './ImageHistory'
import GeneratedImageGrid from './GeneratedImageGrid'
import MediaShowcase from './MediaShowcase'
import { History, Image as ImageIcon } from 'lucide-react'
import { useAtom } from 'jotai'
import {
  isGeneratingAtom,
  generationProgressAtom,
  generatedImageUrlAtom,
  generatedImageUrlsAtom,
} from '../lib/state'
import { downloadImage, downloadAllImages } from '../lib/utils'
import ImageGenerationLoader from './ImageGenerationLoader'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@ui/components/tabs'

export default function ImageGenerator() {
  // 使用全局状态
  const [isGenerating] = useAtom(isGeneratingAtom)
  const [generationProgress] = useAtom(generationProgressAtom)
  const [generatedImageUrl] = useAtom(generatedImageUrlAtom)
  const [generatedImageUrls] = useAtom(generatedImageUrlsAtom)

  // 下载单个图片的处理函数
  const handleDownload = async (imageUrl?: string, index?: number) => {
    try {
      await downloadImage(imageUrl, generatedImageUrl, index)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  // 下载所有图片的处理函数
  const handleDownloadAll = async () => {
    try {
      await downloadAllImages(generatedImageUrls)
    } catch (error) {
      console.error('Download all failed:', error)
    }
  }

  return (
    <div className="h-full flex bg-white border-t border-gray-200">
      {/* 左侧：图片生成器 */}
      <div className="w-[480px] border-r border-gray-200 bg-white">
        <div className="h-full overflow-y-auto p-4">
          <ImageStyleConverter />
        </div>
      </div>

      {/* 右侧：生成结果展示和历史记录 */}
      <div className="flex-1 flex flex-col bg-gray-50">
        <Tabs defaultValue="creations" className="h-full flex flex-col">
          {/* Tab 切换头部 */}
          <div className="bg-white flex justify-center">
            <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
              <TabsTrigger
                value="creations"
                className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
              >
                <ImageIcon className="h-4 w-4" />
                <span>Creations</span>
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
              >
                <History className="h-4 w-4" />
                <span>History</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab 内容 */}
          <TabsContent
            value="creations"
            className="flex-1 overflow-hidden mt-0"
          >
            {/* Generation content area */}
            {isGenerating ? (
              <div className="h-full flex items-center justify-center p-4">
                <ImageGenerationLoader progress={generationProgress} />
              </div>
            ) : generatedImageUrls.length > 0 ? (
              <div className="h-full overflow-y-auto p-4">
                <div className="min-h-full flex items-center justify-center">
                  <GeneratedImageGrid
                    imageUrls={generatedImageUrls}
                    onDownload={handleDownload}
                    onDownloadAll={handleDownloadAll}
                  />
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center p-4">
                <MediaShowcase
                  title="Restore old photos with AI"
                  description="Restore old photos and remove scratches and cracks naturally!"
                  videoSrc="/ai-files/image-upscaler/upscaler.mp4"
                  className="w-full"
                />
              </div>
            )}
          </TabsContent>

          <TabsContent
            value="history"
            className="flex-1 overflow-hidden p-4 mt-0"
          >
            <ImageHistory className="h-full" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
