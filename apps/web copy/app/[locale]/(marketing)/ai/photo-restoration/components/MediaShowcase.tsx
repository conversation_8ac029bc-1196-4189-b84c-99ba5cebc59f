'use client'

import React from 'react'
import Image from 'next/image'

interface MediaShowcaseProps {
  title: string
  description: string
  videoSrc?: string
  imageSrc?: string
  imageAlt?: string
  className?: string
}

export default function MediaShowcase({
  title,
  description,
  videoSrc,
  imageSrc,
  imageAlt = 'Showcase image',
  className = '',
}: MediaShowcaseProps) {
  return (
    <div
      className={`w-full max-w-4xl mx-auto h-full flex flex-col ${className}`}
    >
      {/* 标题和描述 */}
      <div className="text-center mb-4 sm:mb-6 lg:mb-8 flex-shrink-0">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-2 sm:mb-4 mt-4 sm:mt-6 lg:mt-10">
          {title}
        </h2>
        <p className="text-sm sm:text-base lg:text-lg text-gray-600 max-w-2xl mx-auto">
          {description}
        </p>
      </div>

      {/* 媒体内容 - 占用剩余空间 */}
      <div className="flex-1 flex items-center justify-center min-h-0">
        {videoSrc ? (
          <video
            className="max-w-full max-h-full rounded-2xl object-contain"
            autoPlay
            loop
            muted
            playsInline
            controls={false}
          >
            <source src={videoSrc} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        ) : imageSrc ? (
          <Image
            src={imageSrc}
            alt={imageAlt}
            width={800}
            height={534}
            className="max-w-full max-h-full rounded-2xl"
          />
        ) : null}
      </div>
    </div>
  )
}
