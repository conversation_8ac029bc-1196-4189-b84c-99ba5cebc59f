'use client'

import { useTranslations } from 'next-intl'
import { Wand2 } from 'lucide-react'
import { useState, useEffect, useRef } from 'react'

interface SampleImage {
  url: string
  prompt: string
}

interface SampleImageGridProps {
  sampleImages: SampleImage[]
  onPromptSelect: (prompt: string) => void
}

export default function SampleImageGrid({
  sampleImages,
  onPromptSelect,
}: SampleImageGridProps) {
  const t = useTranslations()
  const containerRef = useRef<HTMLDivElement>(null)
  const [layout, setLayout] = useState({
    imageWidth: 200,
    imageHeight: 356,
    gap: 16,
  })

  useEffect(() => {
    const calculateLayout = () => {
      if (!containerRef.current) return

      const containerWidth = containerRef.current.clientWidth
      const containerHeight = containerRef.current.clientHeight

      // 减去 padding
      const availableWidth = containerWidth - 48 // 24px padding each side
      const availableHeight = containerHeight - 48

      const gap = 16

      // 先按容器高度计算图片尺寸（9:16比例）
      const maxImageHeight = availableHeight
      const imageWidthFromHeight = (maxImageHeight * 9) / 16

      // 检查能否放下3张图片
      const totalWidthFor3Images = imageWidthFromHeight * 3 + gap * 2

      let finalImageWidth = imageWidthFromHeight
      let finalImageHeight = maxImageHeight

      if (totalWidthFor3Images > availableWidth) {
        // 放不下3张，按宽度重新计算
        finalImageWidth = (availableWidth - gap * 2) / 3
        finalImageHeight = (finalImageWidth * 16) / 9
      }

      setLayout({
        imageWidth: finalImageWidth,
        imageHeight: finalImageHeight,
        gap,
      })
    }

    calculateLayout()
    window.addEventListener('resize', calculateLayout)
    return () => window.removeEventListener('resize', calculateLayout)
  }, [])

  return (
    <div
      ref={containerRef}
      className="w-full h-full flex items-center justify-center p-6"
    >
      <div className="w-full">
        <div
          className="grid grid-cols-3 w-full place-items-center"
          style={{ gap: `${layout.gap}px` }}
        >
          {sampleImages.map((image, index: number) => (
            <div
              key={index}
              className="relative group bg-gray-50 rounded-lg overflow-hidden"
              style={{
                width: `${layout.imageWidth}px`,
                height: `${layout.imageHeight}px`,
              }}
            >
              <img
                src={image.url}
                alt={`Sample image ${index + 1}`}
                className="w-full h-full object-cover rounded-lg"
              />
              {/* 渐变背景遮罩 */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />

              {/* 内容容器 */}
              <div className="absolute inset-x-0 bottom-0 p-4 flex flex-col gap-3 translate-y-2 group-hover:translate-y-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
                {/* 提示词 */}
                <p className="text-sm text-white/90 line-clamp-3 font-medium">
                  {image.prompt}
                </p>

                {/* 按钮 */}
                <button
                  onClick={() => onPromptSelect(image.prompt)}
                  className="w-full px-4 py-2 rounded-lg bg-white/90 backdrop-blur-sm text-black hover:bg-white transition-colors flex items-center justify-center gap-2 group/btn"
                >
                  <Wand2 className="h-4 w-4 transition-transform group-hover/btn:scale-110" />
                  <span className="text-sm font-medium">
                    {t('ImageGenerator.usePrompt')}
                  </span>
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
