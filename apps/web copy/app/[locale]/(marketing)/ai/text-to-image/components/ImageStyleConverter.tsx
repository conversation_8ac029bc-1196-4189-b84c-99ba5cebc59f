'use client'

import { useState, useRef, useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { AlertCircle, Sparkles, Shuffle } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useAtom, useSetAtom } from 'jotai'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
import { Button } from '@ui/components/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@ui/components/tooltip'
import { Textarea } from '@ui/components/textarea'
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage,
} from '@ui/components/form'
//
import {
  imagesAtom,
  formDataAtom,
  generatedTaskIdAtom,
  formSchema,
  type FormData,
  generationProgress<PERSON>tom,
  isGenerating<PERSON>tom,
  generation<PERSON>rror<PERSON>tom,
  generatedImage<PERSON>rl<PERSON>tom,
  generatedImage<PERSON><PERSON><PERSON><PERSON>,
  currentTaskId<PERSON><PERSON>,
  prompt<PERSON>tom,
} from '../lib/state'
import { useModal } from '@shared/hooks/useModal'
import { getUserIdFromCookie } from '@/utils/lib'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useTranslations, useLocale } from 'next-intl'
import { TASK_TYPES } from '@/../constants'
import { consumePointsAtom } from '@marketing/stores'
import { CreateHistoryRequest } from '@/types/history'
import { saveGenerationHistory, updateGenerationHistory } from '../lib/utils'
import { RANDOM_PROMPTS_MAP } from '../lib/const'
import PointIcon from '../../components/ui/PointIcon'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'

// API related configuration
const API_URL_GENERATE = '/api/images/generate'
const API_URL_STATUS = '/api/images/record-info'
const POLL_INTERVAL = 5000
const MAX_POLL_TIME = 600000 // 5 minutes

type ImageStyleConverterProps = {
  onGenerated?: (generatedUrl: string) => void
  onStartGenerating?: () => void
  redirectOnGenerate?: boolean
  onError?: () => void
  onGetSetPrompt?: (setPromptFn: (prompt: string) => void) => void
}

export function ImageStyleConverter({
  onGenerated,
  onStartGenerating,
  redirectOnGenerate = false,
  onError,
  onGetSetPrompt,
}: ImageStyleConverterProps) {
  const t = useTranslations()
  const router = useRouter()
  // 使用整合的图片数据
  const [images, setImages] = useAtom(imagesAtom)
  const [prompt] = useAtom(promptAtom)
  const { showLoginModal, showInsufficientCreditsModal } = useModal()
  const [formData, setFormData] = useAtom(formDataAtom)
  const [generatedTaskId, setGeneratedTaskId] = useAtom(generatedTaskIdAtom)

  const user = getUserFromClientCookies()
  const generationMode = 'text-to-image'
  const { calculatePoints, updateUserInfo } = usePermissionCheck()
  // 默认表单数据
  const defaultFormValues: FormData = {
    prompt: '',
    ratio: '2:3',
    nVariants: '1',
  }

  // Form configuration - 只使用默认值初始化表单
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  })

  // Consume points
  const consumePoints = useSetAtom(consumePointsAtom)

  // Use global state
  const [isGenerating, setIsGenerating] = useAtom(isGeneratingAtom)
  const [, setGenerationProgress] = useAtom(generationProgressAtom)
  const [generationError, setGenerationError] = useAtom(generationErrorAtom)
  const [, setGeneratedImageUrl] = useAtom(generatedImageUrlAtom)
  const [, setGeneratedImageUrls] = useAtom(generatedImageUrlsAtom)

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTaskId, setCurrentTaskId] = useAtom(currentTaskIdAtom)
  const [currentHistoryId, setCurrentHistoryId] = useState<number | null>(null)
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number | null>(null)
  const locale = useLocale()

  // 随机选择 Prompt 的函数
  const getRandomPrompt = useCallback(() => {
    const prompts = RANDOM_PROMPTS_MAP[locale || 'en']

    const randomIndex = Math.floor(Math.random() * prompts.length)
    return prompts[randomIndex]
  }, [])

  // 处理随机 Prompt 按钮点击
  const handleRandomPrompt = useCallback(() => {
    if (isGenerating) return // 生成中时不允许操作

    const randomPrompt = getRandomPrompt()
    form.setValue('prompt', randomPrompt)
  }, [form, getRandomPrompt, isGenerating])

  // Create initial history data (PENDING status)
  const createInitialHistoryData = (
    taskId: string,
    originalImageUrls: string[]
  ): CreateHistoryRequest => {
    const userId = getUserIdFromCookie()
    return {
      userId,
      taskType: TASK_TYPES.TEXT_TO_IMAGE,
      externalTaskId: taskId,
      status: 'PENDING',
      inputParams: {
        prompt: form.getValues('prompt'),
        ratio: form.getValues('ratio'),
        nVariants: form.getValues('nVariants'),
        originalImageUrls: originalImageUrls,
        generationMode: generationMode,
        selectedStyle: null,
      },
      metadata: {
        description: 'Image generation task started',
        originalImageCount: originalImageUrls.length,
      },
    }
  }

  const resetLoading = useCallback(() => {
    setIsGenerating(false)
    setGenerationProgress(0)
    setGeneratedTaskId(null)
    setIsSubmitting(false)
    setCurrentTaskId(null)
    setCurrentHistoryId(null)
    setGeneratedImageUrl(null)
    setGeneratedImageUrls([])
    onError?.()
  }, [
    setIsGenerating,
    setGenerationProgress,
    setGeneratedTaskId,
    setIsSubmitting,
    setCurrentTaskId,
    setGeneratedImageUrl,
    setGeneratedImageUrls,
    onError,
  ])

  // Start polling task status
  const startPolling = useCallback(
    (id: string, originalImageUrls: string[]) => {
      // If this task has already been successfully generated, return directly
      if (generatedTaskId === id) {
        setIsSubmitting(false)
        setCurrentTaskId(null)
        return
      }

      // 确保每次开始轮询时都重置开始时间
      startTimeRef.current = Date.now()
      console.log(
        '开始轮询任务:',
        id,
        '开始时间:',
        new Date(startTimeRef.current).toLocaleString()
      )

      const pollForResult = async () => {
        try {
          const response = await fetch(`${API_URL_STATUS}?taskId=${id}`, {
            method: 'GET',
            headers: {
              Accept: 'application/json',
            },
          })

          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`)
          }

          const data = await response.json()

          if (data.code === 200) {
            if (data.data.status === 'SUCCESS' && data.data.successFlag === 1) {
              // Generation successful
              const resultUrls = data.data.response.resultUrls || []

              // 保存所有生成的图片URL
              setGeneratedImageUrls(resultUrls)

              // 为了兼容性，仍然设置第一张图片为主图片
              const resultUrl = resultUrls.length > 0 ? resultUrls[0] : null
              setIsGenerating(false)
              setGenerationProgress(100)
              setGeneratedTaskId(id)

              if (resultUrls.length > 0) {
                // 设置第一张图片为主图片（兼容旧版本）
                if (resultUrl) {
                  setGeneratedImageUrl(resultUrl)
                }

                // Update history record with successful results
                if (currentHistoryId) {
                  try {
                    await updateGenerationHistory(
                      currentHistoryId,
                      getUserIdFromCookie(),
                      {
                        status: 'SUCCESS',
                        resultData: {
                          generatedImageUrls: resultUrls,
                          originalImageUrls: originalImageUrls,
                        },
                        metadata: {
                          description:
                            'Image generation task completed successfully',
                          originalImageCount: originalImageUrls.length,
                          generatedImageCount: resultUrls.length,
                        },
                        completedAt: new Date().toISOString(),
                      }
                    )
                  } catch (error) {
                    console.error('Failed to update history record:', error)
                  }
                }

                if (onGenerated && resultUrl) {
                  onGenerated(resultUrl)
                }
              }

              setIsSubmitting(false)
              setCurrentTaskId(null)

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else if (
              data.data.status === 'FAILED' ||
              (data.data.status === 'GENERATE_FAILED' &&
                data.data.successFlag === 3)
            ) {
              // Generation failed - update history record
              const errorMessage = `Generation failed: ${
                data.data.errorMessage || 'Unknown error'
              }`

              if (currentHistoryId) {
                try {
                  await updateGenerationHistory(
                    currentHistoryId,
                    getUserIdFromCookie(),
                    {
                      status: 'FAILED',
                      errorMessage: errorMessage,
                      metadata: {
                        description: 'Image generation task failed',
                        originalImageCount: originalImageUrls.length,
                        errorDetails: data.data.errorMessage || 'Unknown error',
                      },
                      completedAt: new Date().toISOString(),
                    }
                  )
                } catch (error) {
                  console.error(
                    'Failed to update failed history record:',
                    error
                  )
                }
              }

              setGenerationError(errorMessage)
              resetLoading()

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else {
              // Still processing, update progress
              const elapsed = Date.now() - (startTimeRef.current || Date.now())
              const progress = Number(data.data.progress) * 100

              // 确保进度只能向前增长，不会倒退
              setGenerationProgress((currentProgress) => {
                const newProgress = Math.max(currentProgress, progress)
                return newProgress
              })

              // Check if timeout
              if (elapsed > MAX_POLL_TIME) {
                console.log('Generation timed out, please try again')
                setGenerationError(t('ImageStyleConverter.generationTimeout'))
                resetLoading()

                if (pollIntervalRef.current) {
                  clearInterval(pollIntervalRef.current)
                  pollIntervalRef.current = null
                }
              }
            }
          } else {
            throw new Error(data.msg || 'Request failed')
          }
        } catch (err) {
          console.error('Error polling for result:', err)
          setGenerationError(t('ImageStyleConverter.generationError'))
          resetLoading()

          if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current)
            pollIntervalRef.current = null
          }
        }
      }

      // Execute immediately, then set timer
      pollForResult()
      pollIntervalRef.current = setInterval(pollForResult, POLL_INTERVAL)
    },
    [
      onGenerated,
      setGeneratedTaskId,
      generatedTaskId,
      resetLoading,
      setIsGenerating,
      setGenerationProgress,
      setGenerationError,
      setGeneratedImageUrl,
    ]
  )

  // Check if form is valid
  const isFormValid = form.watch('prompt')?.trim()

  // Form submission handling
  const onSubmit = useCallback(
    async (values: FormData) => {
      if (!values.prompt.trim()) {
        setGenerationError(t('ImageStyleConverter.promptRequired'))
        return
      }

      // Check points first
      if (!user) {
        showLoginModal({
          title: t('loginTipsTitle'),
          content: t('tipLogin'),
          props: {
            needBottomArea: true, // 显示会员权益
          },
        })
        return
      }

      // If already submitting or there's an ongoing task, don't submit again
      if (isSubmitting || currentTaskId) {
        return
      }

      setGenerationError(null)
      setIsSubmitting(true)
      setIsGenerating(true)

      // 只有在没有当前任务ID时才重置进度为5%（表示这是一个新任务）
      if (!currentTaskId) {
        setGenerationProgress(5)
      }

      onStartGenerating?.()

      try {
        let filesUrl: string[] = []

        const response = await fetch(API_URL_GENERATE, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify({
            filesUrl,
            prompt: values.prompt,
            size: values.ratio,
            callBackUrl: 'https://your-callback-url.com/callback',
            mode: generationMode, // 添加生成模式参数
            nVariants: values.nVariants || '1', // 添加变体数量参数
          }),
        })

        if (!response.ok) {
          console.log(`HTTP error! Status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 401000) {
          showLoginModal({
            title: t('loginTipsTitle'),
            content: t('tipLogin'),
          })
          resetLoading()
          return
        }

        if (
          data.code === 400000 &&
          data.message?.en === 'Insufficient points.'
        ) {
          resetLoading()
          showInsufficientCreditsModal({
            content: t('ImageStyleConverter.insufficientPoints'),
          })
          return
        }

        if (data.code === 100000) {
          const newTaskId = data.data.taskId
          setCurrentTaskId(newTaskId)
          updateUserInfo()
          // Save initial history record with PENDING status
          // try {
          //   const initialHistoryData = createInitialHistoryData(
          //     newTaskId,
          //     filesUrl
          //   )
          //   const historyRecord = await saveGenerationHistory(
          //     initialHistoryData
          //   )
          //   setCurrentHistoryId(historyRecord.id)
          // } catch (error) {
          //   console.error('Failed to save initial history record:', error)
          // }

          startPolling(newTaskId, filesUrl)
        } else {
          throw new Error(data.msg || 'Request failed')
        }
      } catch (err) {
        console.error('Generation request failed:', err)
        setGenerationError(
          'Failed to initiate generation request, please try again'
        )
        resetLoading()
      }
    },
    [
      images,
      generationMode,
      router,
      setFormData,
      onStartGenerating,
      startPolling,
      resetLoading,
      isSubmitting,
      showLoginModal,
      showInsufficientCreditsModal,
      user,
      t,
      consumePoints,
    ]
  )

  // Listen for form changes
  useEffect(() => {
    const subscription = form.watch((values, { name }) => {
      console.log('Form value changed:', name, values)

      if (name === 'prompt' || name === 'ratio' || name === 'nVariants') {
        // 保存表单数据
        if (values.prompt || values.ratio || values.nVariants) {
          const formData = {
            prompt: values.prompt || '',
            ratio: values.ratio || '3:2',
            nVariants: values.nVariants || '1',
          }
          // 同时更新 formData atom
          setFormData(formData)
          // 如果是提示词变化，通知父组件
          if (name === 'prompt' && onGetSetPrompt) {
            onGetSetPrompt((prompt: string) => {
              form.setValue('prompt', prompt)
            })
          }
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, setFormData, onGetSetPrompt])

  // 监听 prompt atom 的变化
  useEffect(() => {
    if (prompt) {
      form.setValue('prompt', prompt)
    }
  }, [prompt, form])

  // Clean up polling
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current)
      }
    }
  }, [])

  // 提供setPrompt方法给父组件
  useEffect(() => {
    if (onGetSetPrompt) {
      const setPromptFn = (prompt: string) => {
        form.setValue('prompt', prompt, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        })
      }
      onGetSetPrompt(setPromptFn)
    }
  }, [onGetSetPrompt, form])

  return (
    <>
      <div className="w-full h-full image-style-converter">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col h-full"
          >
            <div className="overflow-y-auto">
              <div className="py-2">
                <div className="flex items-center justify-between">
                  <span className="block text-sm font-medium text-gray-700">
                    {t('ImageStyleConverter.conversionPrompt')}
                    <span className="text-red-500 ml-0.5">*</span>
                  </span>
                </div>

                <FormField
                  control={form.control}
                  name="prompt"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div
                          className={`mt-3 border rounded-md ${
                            isGenerating
                              ? 'border-gray-200 bg-gray-50'
                              : 'border-gray-300 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500'
                          } ${
                            redirectOnGenerate
                              ? 'bg-gray-50/30 backdrop-blur-sm'
                              : 'bg-white'
                          }`}
                        >
                          <Textarea
                            {...field}
                            placeholder={t(
                              'ImageStyleConverter.conversionPromptDesc'
                            )}
                            className={`h-28 resize-none border-0 focus:ring-0 focus:border-0 focus-visible:ring-0 focus-visible:ring-offset-0 rounded-none rounded-t-md ${
                              isGenerating
                                ? 'opacity-70 text-gray-400 cursor-not-allowed placeholder:text-gray-300 bg-transparent'
                                : 'bg-transparent'
                            }`}
                            disabled={isGenerating}
                          />
                          <div className="flex justify-end p-2 border-t border-gray-200">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <button
                                    type="button"
                                    onClick={handleRandomPrompt}
                                    disabled={isGenerating}
                                    className={`flex items-center justify-center gap-1 px-2 py-1 rounded-md text-xs font-medium transition-colors ${
                                      isGenerating
                                        ? 'opacity-50 cursor-not-allowed text-gray-400'
                                        : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                                    }`}
                                  >
                                    <Shuffle className="h-3 w-3" />
                                    <span>
                                      {t(
                                        'ImageStyleConverter.randomInspiration'
                                      )}
                                    </span>
                                  </button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    {t(
                                      'ImageStyleConverter.randomInspirationTooltip'
                                    )}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Ratio selection */}
              <div className="mb-4">
                <div className="mb-2">
                  <h3 className="text-sm font-medium text-gray-800">Ratio</h3>
                </div>
                <div className="w-full">
                  <Select
                    value={form.watch('ratio')}
                    onValueChange={(value) =>
                      form.setValue('ratio', value as '3:2' | '2:3' | '1:1')
                    }
                    disabled={isGenerating}
                  >
                    <SelectTrigger
                      className={`w-full bg-gray-50 border-gray-200 ${
                        isGenerating
                          ? 'opacity-70 text-gray-400 cursor-not-allowed'
                          : ''
                      }`}
                    >
                      <div className="flex items-center justify-between w-full">
                        <SelectValue placeholder="Select ratio" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3:2">
                        <div className="flex items-center gap-3">
                          <div className="w-8 flex items-center justify-center">
                            <div className="w-4 h-3 border border-gray-600 bg-gray-100"></div>
                          </div>
                          <span>3:2</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="1:1">
                        <div className="flex items-center gap-3">
                          <div className="w-8 flex items-center justify-center">
                            <div className="w-3 h-3 border border-gray-600 bg-gray-100"></div>
                          </div>
                          <span>1:1</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="2:3">
                        <div className="flex items-center gap-3">
                          <div className="w-8 flex items-center justify-center">
                            <div className="w-3 h-4 border border-gray-600 bg-gray-100"></div>
                          </div>
                          <span>2:3</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {form.formState.errors.ratio && (
                    <p className="text-sm text-red-600 mt-1">
                      {form.formState.errors.ratio.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Image Count selection */}
              <div className="mb-4">
                <div className="mb-2">
                  <h3 className="text-sm font-medium text-gray-800">
                    Image Count
                  </h3>
                </div>
                <div className="w-full">
                  <Select
                    value={form.watch('nVariants')}
                    onValueChange={(value) =>
                      form.setValue('nVariants', value as '1' | '2' | '4')
                    }
                    disabled={isGenerating}
                  >
                    <SelectTrigger
                      className={`w-full bg-gray-50 border-gray-200 ${
                        isGenerating
                          ? 'opacity-70 text-gray-400 cursor-not-allowed'
                          : ''
                      }`}
                    >
                      <div className="flex items-center justify-between w-full">
                        <SelectValue placeholder="Select quantity" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Generate button and progress */}
            <div className="space-y-4 flex-shrink-0">
              <Button
                type="submit"
                className="w-full bg-gradient-to-r gap-1 from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200"
                disabled={!isFormValid}
                loading={isGenerating}
                size="lg"
              >
                {isGenerating
                  ? t('ImageStyleConverter.generating')
                  : t('ImageStyleConverter.generateButton')}

                <PointIcon points={calculatePoints()} />
              </Button>

              {generationError && (
                <div
                  className={`bg-red-50 p-3 rounded-md flex items-start ${
                    redirectOnGenerate ? 'bg-opacity-80' : ''
                  }`}
                >
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-600">{generationError}</p>
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
    </>
  )
}
