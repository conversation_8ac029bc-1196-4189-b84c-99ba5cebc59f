'use client'

import { ImageStyleConverter } from './ImageStyleConverter'
import ImageHistory from './ImageHistory'
import SampleImageGrid from './SampleImageGrid'
import GeneratedImageGrid from './GeneratedImageGrid'
import { History, Image as ImageIcon } from 'lucide-react'
import { useAtom } from 'jotai'
import {
  isGeneratingAtom,
  generationProgressAtom,
  generatedImageUrlAtom,
  generatedImageUrlsAtom,
} from '../lib/state'
import { downloadImage, downloadAllImages } from '../lib/utils'
import ImageGenerationLoader from './ImageGenerationLoader'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@ui/components/tabs'
import { useMemo, useCallback, useRef } from 'react'
import { SAMPLE_IMAGES } from '../lib/const'

export default function ImageGenerator() {
  // 使用全局状态
  const [isGenerating] = useAtom(isGeneratingAtom)
  const [generationProgress] = useAtom(generationProgressAtom)
  const [generatedImageUrl] = useAtom(generatedImageUrlAtom)
  const [generatedImageUrls] = useAtom(generatedImageUrlsAtom)

  // 创建一个ref来存储ImageStyleConverter组件的setPrompt方法
  const setPromptRef = useRef<((prompt: string) => void) | null>(null)

  // 随机选择3张图片
  const randomSampleImages = useMemo(() => {
    const shuffled = [...SAMPLE_IMAGES].sort(() => Math.random() - 0.5)
    return shuffled.slice(0, 3)
  }, [])

  // 设置提示词的处理函数
  const handleSetPrompt = useCallback((prompt: string) => {
    if (setPromptRef.current) {
      setPromptRef.current(prompt)
    }
  }, [])

  // 获取子组件的setPrompt方法
  const handleGetSetPrompt = useCallback(
    (setPromptFn: (prompt: string) => void) => {
      setPromptRef.current = setPromptFn
    },
    []
  )

  // 下载单个图片的处理函数
  const handleDownload = async (imageUrl?: string, index?: number) => {
    try {
      await downloadImage(imageUrl, generatedImageUrl, index)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  // 下载所有图片的处理函数
  const handleDownloadAll = async () => {
    try {
      await downloadAllImages(generatedImageUrls)
    } catch (error) {
      console.error('Download all failed:', error)
    }
  }

  return (
    <div className="h-full flex bg-white border-t border-gray-200">
      {/* 左侧：图片生成器 */}
      <div className="w-[480px] border-r border-gray-200 bg-white">
        <div className="h-full overflow-y-auto p-4">
          <ImageStyleConverter
            redirectOnGenerate={false}
            onGetSetPrompt={handleGetSetPrompt}
          />
        </div>
      </div>

      {/* 右侧：生成结果展示和历史记录 */}
      <div className="flex-1 flex flex-col bg-gray-50">
        <Tabs defaultValue="creations" className="h-full flex flex-col">
          {/* Tab 切换头部 */}
          <div className="bg-white flex justify-center">
            <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
              <TabsTrigger
                value="creations"
                className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
              >
                <ImageIcon className="h-4 w-4" />
                <span>Creations</span>
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
              >
                <History className="h-4 w-4" />
                <span>History</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab 内容 */}
          <TabsContent
            value="creations"
            className="flex-1 overflow-hidden mt-0"
          >
            {/* Generation content area */}
            {isGenerating ? (
              <div className="h-full flex items-center justify-center p-4">
                <ImageGenerationLoader progress={generationProgress} />
              </div>
            ) : generatedImageUrls.length > 0 ? (
              <div className="h-full overflow-y-auto p-4">
                <div className="min-h-full flex items-center justify-center">
                  <GeneratedImageGrid
                    imageUrls={generatedImageUrls}
                    onDownload={handleDownload}
                    onDownloadAll={handleDownloadAll}
                  />
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center p-4">
                <SampleImageGrid
                  sampleImages={randomSampleImages}
                  onPromptSelect={handleSetPrompt}
                />
              </div>
            )}
          </TabsContent>

          <TabsContent
            value="history"
            className="flex-1 overflow-hidden p-4 mt-0"
          >
            <ImageHistory className="h-full" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
