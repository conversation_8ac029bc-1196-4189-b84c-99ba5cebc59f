'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from '@ui/components/tabs'
import { useAuth } from '../../../../../modules/ui/hooks/use-auth'
import { useUpload } from '../face-swap/hooks/useUpload'
import { HistoryIcon, RotateCcw, Sparkles } from 'lucide-react'
import {
  IMAGE_TO_VIDEO_CONFIG,
  type VideoSettings,
  type VideoEffect,
} from './config'

// 导入新的统一组件和hooks
import {
  useUnifiedGeneration,
  useUnifiedHistory,
  UnifiedHistoryTab,
  TaskType,
  GenerationProgress,
  useGenerationProgress,
} from '../components'

// 导入新的侧边栏组件
import {
  ImageToVideoSidebar,
  type GenerationParams,
} from './components/ImageToVideoSidebar'
import { downloadFile } from '../components/utils'

const TASK_TYPE: TaskType = 'imagetovideo'

export default function ImageToVideoPage() {
  const [selectedEffect, setSelectedEffect] = useState<VideoEffect | null>(null)
  const [customPrompt, setCustomPrompt] = useState<string>('')
  const [videoSettings, setVideoSettings] = useState<VideoSettings>({
    duration: IMAGE_TO_VIDEO_CONFIG.videoSettings.defaults.duration,
    quality: IMAGE_TO_VIDEO_CONFIG.videoSettings.defaults.quality,
    aspectRatio: IMAGE_TO_VIDEO_CONFIG.videoSettings.defaults.aspectRatio,
  })
  const [activeTab, setActiveTab] = useState('creations')

  // 使用进度管理hook
  const {
    progress: generationProgress,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress: setGenerationProgress,
  } = useGenerationProgress()

  const { isLoggedIn } = useAuth()

  const imageUpload = useUpload()

  // 使用统一的生成hook
  const {
    isGenerating,
    taskId,
    error: generateError,
    taskDetail,
    isPolling,
    pollingError,
    generate,
    reset: resetGeneration,
    stopPolling,
  } = useUnifiedGeneration(TASK_TYPE)

  // 使用统一的历史记录hook
  const {
    items: historyItems,
    isLoading: isLoadingHistory,
    error: historyError,
    refreshHistory,
  } = useUnifiedHistory(TASK_TYPE)

  // 新的生成函数，处理裁剪后的图片
  const handleGenerateFromSidebar = useCallback(
    async (params: GenerationParams) => {
      try {
        // 切换到 creations tab
        setActiveTab('creations')

        // 开始进度计时器
        startProgress()
        // 使用统一的生成接口，直接使用已上传的图片URL
        await generate({
          imageUrl: params.uploadedImageUrl,
          prompt: params.prompt,
          duration: params.duration,
          quality: params.quality,
          aspectRatio: params.aspectRatio,
        })
      } catch (error) {
        console.error('Failed to generate video:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [generate, startProgress, resetProgress, setActiveTab]
  )

  const handleReset = useCallback(() => {
    imageUpload.reset()
    setSelectedEffect(null)
    setCustomPrompt('')
    resetGeneration()
    stopPolling()

    // 重置进度状态
    resetProgress()
  }, [imageUpload, resetGeneration, stopPolling, resetProgress])

  const handleRegenerateFromHistory = useCallback(
    async (input: Record<string, any>) => {
      const imageUrl = input.imageUrl

      if (!imageUrl) return

      // 设置图片到上传组件
      imageUpload.setImageUrl(imageUrl)

      // 从历史记录恢复设置
      if (input.prompt) {
        setCustomPrompt(input.prompt)
      }
      if (input.duration || input.quality || input.aspectRatio) {
        setVideoSettings({
          duration: input.duration || videoSettings.duration,
          quality: input.quality || videoSettings.quality,
          aspectRatio: input.aspectRatio || videoSettings.aspectRatio,
        })
      }

      try {
        // 开始进度计时器
        startProgress()

        await generate({
          imageUrl,
          prompt: input.prompt || customPrompt || 'Transform image to video',
          duration: input.duration || videoSettings.duration,
          quality: input.quality || videoSettings.quality,
          aspectRatio: input.aspectRatio || videoSettings.aspectRatio,
        })
      } catch (error) {
        console.error('Failed to regenerate video:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [
      imageUpload,
      generate,
      customPrompt,
      videoSettings,
      startProgress,
      resetProgress,
    ]
  )

  React.useEffect(() => {
    if (isLoggedIn && historyItems.length === 0) {
      refreshHistory()
    }
  }, [isLoggedIn, historyItems.length, refreshHistory])

  React.useEffect(() => {
    if (
      taskDetail &&
      (taskDetail.status.status === 'success' ||
        taskDetail.status.status === 'failed')
    ) {
      setTimeout(() => {
        refreshHistory()
      }, 1000)
    }
  }, [taskDetail?.status.status, refreshHistory])

  // 监听任务完成状态，更新进度到100%
  React.useEffect(() => {
    if (taskDetail?.status.status === 'success') {
      // 任务成功完成，将进度设置为100%并停止计时器
      setGenerationProgress(100)
      stopProgress()
    } else if (taskDetail?.status.status === 'failed') {
      // 任务失败，重置进度
      resetProgress()
    }
  }, [
    taskDetail?.status.status,
    setGenerationProgress,
    stopProgress,
    resetProgress,
  ])

  // 适配现有的VideoResultDisplay组件 - 参考ai-smile逻辑
  const adaptedTaskDetail = taskDetail
    ? {
        taskId: taskDetail.taskId,
        state:
          taskDetail.status.status === 'success'
            ? 'success'
            : taskDetail.status.status === 'failed'
            ? 'fail'
            : taskDetail.status.status === 'processing'
            ? 'generating'
            : 'wait',
        inputInfo: taskDetail.input,
        videoInfo: taskDetail.output,
        error: taskDetail.error,
        expireFlag: false,
      }
    : null

  const handleDownload = useCallback(async (url: string) => {
    try {
      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = `image_to_video_${Date.now()}.mp4`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }, [])

  return (
    <main className="h-screen pt-[68px] bg-gray-50">
      <div className="h-full flex bg-white border-t border-gray-200">
        <div className="w-[480px] border-r border-gray-200 bg-white flex flex-col">
          {/* 新的图片转视频侧边栏 */}
          <ImageToVideoSidebar
            onGenerate={handleGenerateFromSidebar}
            isGenerating={isGenerating}
            onReset={handleReset}
            className="h-full"
          />
        </div>

        <div className="flex-1 flex flex-col bg-gray-50">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <div className="bg-white flex justify-center">
              <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
                <TabsTrigger
                  value="creations"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <Sparkles className="h-4 w-4" />
                  <span>Create Video</span>
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <HistoryIcon className="h-4 w-4" />
                  <span>History</span>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent
              value="creations"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="h-full overflow-y-auto p-4 relative">
                <div className="min-h-full flex items-start justify-center">
                  <div className="w-full">
                    <div className="text-center mb-12">
                      <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        Transform Images to Video
                      </h1>
                      <p className="text-lg text-gray-600">
                        Choose from various effects to bring your images to life
                        with AI-powered video generation.
                      </p>
                    </div>

                    <div className="flex justify-center">
                      <div className="w-full max-w-2xl">
                        {/* 使用统一的生成进度组件 */}
                        {isGenerating || isPolling ? (
                          <GenerationProgress
                            progress={generationProgress}
                            isGenerating={isGenerating || isPolling}
                            title="Generating Video"
                            description="AI is transforming your image into a video..."
                            size="md"
                          />
                        ) : generateError || pollingError ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                                <RotateCcw className="w-8 h-8 text-red-500" />
                              </div>
                              <div className="space-y-2">
                                <h3 className="text-lg font-semibold text-gray-900">
                                  Generation Failed
                                </h3>
                                <p className="text-sm text-red-600">
                                  {generateError || pollingError}
                                </p>
                              </div>
                              <button
                                onClick={() => {
                                  resetGeneration()
                                  stopPolling()
                                  resetProgress()
                                }}
                                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                              >
                                Try Again
                              </button>
                            </div>
                          </div>
                        ) : adaptedTaskDetail?.state === 'success' &&
                          adaptedTaskDetail?.videoInfo?.videoUrl ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="space-y-6">
                              <div className="relative max-w-md mx-auto">
                                <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                                  <video
                                    className="w-full h-full object-contain"
                                    src={adaptedTaskDetail.videoInfo.videoUrl}
                                    controls
                                    loop
                                    muted
                                    playsInline
                                  />
                                </div>
                              </div>
                              <div className="flex justify-center space-x-4">
                                <button
                                  onClick={() =>
                                    downloadFile(
                                      adaptedTaskDetail.videoInfo?.videoUrl ||
                                        ''
                                    )
                                  }
                                  className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg"
                                >
                                  <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                  </svg>
                                  <span>Download Video</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span className="text-2xl">🎬</span>
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900">
                                Ready to Transform Images
                              </h3>
                              <p className="text-sm text-gray-600">
                                Upload an image and select an effect to create
                                amazing videos with AI
                              </p>
                              {selectedEffect && (
                                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                                  <p className="text-sm text-blue-800">
                                    Selected effect:{' '}
                                    <strong>{selectedEffect.name}</strong>
                                  </p>
                                  <p className="text-xs text-blue-600 mt-1">
                                    {selectedEffect.description}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="history"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="w-full h-full">
                {/* 使用新的统一历史记录组件 */}
                <UnifiedHistoryTab
                  taskType={TASK_TYPE}
                  onRegenerate={handleRegenerateFromHistory}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </main>
  )
}
