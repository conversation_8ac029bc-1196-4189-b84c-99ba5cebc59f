'use client'

import { useState, useCallback } from 'react'
import { type VideoSettings, type VideoEffect } from '../config'

interface GenerateVideoParams {
  imageUrl: string
  effect?: VideoEffect
  customPrompt?: string
  settings: VideoSettings
}

interface VideoGenerationResponse {
  code: number
  msg: string
  data: {
    taskId: string
  } | null
  history_record_id?: string | null
}

interface GenerationError {
  code: number
  message: string
}

export function useImageToVideo() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [taskId, setTaskId] = useState<string | null>(null)
  const [error, setError] = useState<GenerationError | null>(null)

  const generateVideo = useCallback(async (params: GenerateVideoParams) => {
    const { imageUrl, effect, customPrompt, settings } = params

    try {
      setIsGenerating(true)
      setError(null)
      setTaskId(null)

      // Determine final prompt
      const finalPrompt = customPrompt || effect?.prompt
      if (!finalPrompt) {
        throw new Error('Prompt is required for video generation')
      }

      // Validate settings compatibility (8s + 1080p not allowed)
      if (settings.duration === 8 && settings.quality === '1080p') {
        throw new Error(
          'Cannot generate 8-second video with 1080p quality. Please use 720p for 8-second videos or 5-second duration for 1080p.'
        )
      }

      // Build request body
      const requestBody = {
        prompt: finalPrompt,
        imageUrl,
        duration: settings.duration,
        quality: settings.quality,
        aspectRatio: settings.aspectRatio,
        waterMark: '', // Default empty watermark
      }

      console.log('Generating video with params:', requestBody)

      // Call API
      const response = await fetch('/api/video/imagetovideo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      const data: VideoGenerationResponse = await response.json()

      if (!response.ok) {
        throw new Error(data.msg || `HTTP error! status: ${response.status}`)
      }

      if (data.code !== 200) {
        throw new Error(data.msg || 'Video generation failed')
      }

      if (!data.data?.taskId) {
        throw new Error('No task ID received from server')
      }

      const newTaskId = data.data.taskId
      setTaskId(newTaskId)

      console.log('Video generation started:', {
        taskId: newTaskId,
        historyRecordId: data.history_record_id,
      })

      return newTaskId
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred'
      const errorCode =
        err instanceof Error && 'code' in err ? (err as any).code : 500

      console.error('Video generation failed:', err)

      setError({
        code: errorCode,
        message: errorMessage,
      })

      throw err
    } finally {
      setIsGenerating(false)
    }
  }, [])

  const reset = useCallback(() => {
    setIsGenerating(false)
    setTaskId(null)
    setError(null)
  }, [])

  return {
    isGenerating,
    taskId,
    error,
    generateVideo,
    reset,
  }
}
