'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

interface TaskDetail {
  id: string
  state: 'pending' | 'processing' | 'completed' | 'failed'
  video_url?: string
  progress?: number
  error_message?: string
  estimated_time?: number
}

interface TaskResponse {
  code: number
  msg: string
  data: TaskDetail | null
}

interface TaskError {
  code: number
  message: string
}

export function useImageToVideoTask(taskId: string | null) {
  const [taskDetail, setTaskDetail] = useState<TaskDetail | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  const [error, setError] = useState<TaskError | null>(null)
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)

  // Stop polling
  const stopPolling = useCallback(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
    }
    setIsPolling(false)
  }, [])

  // Fetch task detail
  const fetchTaskDetail = useCallback(
    async (id: string) => {
      try {
        const response = await fetch(`/api/video/detail?taskId=${id}`)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data: TaskResponse = await response.json()

        if (data.code !== 200) {
          throw new Error(data.msg || 'Failed to fetch task detail')
        }

        if (!mountedRef.current) return

        const detail = data.data
        if (detail) {
          setTaskDetail(detail)
          setError(null)

          // Stop polling if task is completed or failed
          if (detail.state === 'completed' || detail.state === 'failed') {
            stopPolling()
          }
        }

        return detail
      } catch (err) {
        console.error('Failed to fetch task detail:', err)

        if (!mountedRef.current) return

        const errorMessage =
          err instanceof Error ? err.message : 'Unknown error occurred'
        setError({
          code: 500,
          message: errorMessage,
        })

        stopPolling()
        return null
      }
    },
    [stopPolling]
  )

  // Start polling
  const startPolling = useCallback(
    (id: string) => {
      if (!id) return

      setIsPolling(true)
      setError(null)

      // Immediate fetch
      fetchTaskDetail(id)

      // Start polling interval
      pollingIntervalRef.current = setInterval(() => {
        fetchTaskDetail(id)
      }, 3000) // Poll every 3 seconds
    },
    [fetchTaskDetail]
  )

  // Auto-start polling when taskId changes
  useEffect(() => {
    if (taskId) {
      startPolling(taskId)
    } else {
      stopPolling()
      setTaskDetail(null)
    }

    return () => {
      stopPolling()
    }
  }, [taskId, startPolling, stopPolling])

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true

    return () => {
      mountedRef.current = false
      stopPolling()
    }
  }, [stopPolling])

  // Manual refresh
  const refreshTask = useCallback(() => {
    if (taskId) {
      fetchTaskDetail(taskId)
    }
  }, [taskId, fetchTaskDetail])

  // Reset state
  const reset = useCallback(() => {
    stopPolling()
    setTaskDetail(null)
    setError(null)
  }, [stopPolling])

  return {
    taskDetail,
    isPolling,
    error,
    refreshTask,
    stopPolling,
    reset,
  }
}
