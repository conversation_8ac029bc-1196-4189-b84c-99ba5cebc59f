'use client'

import { <PERSON>, Zap, <PERSON>io, Sparkles } from 'lucide-react'
import { type VideoSettings, type VideoEffect } from '../config'

type SettingsSummaryProps = {
  settings: VideoSettings
  selectedEffect: VideoEffect | null
}

export function SettingsSummary({
  settings,
  selectedEffect,
}: SettingsSummaryProps) {
  return (
    <div className="flex items-center gap-3 text-xs">
      {/* Duration */}
      <div className="flex items-center gap-1 text-gray-600">
        <Clock className="h-3 w-3" />
        <span className="font-medium">{settings.duration}s</span>
      </div>

      {/* Quality */}
      <div className="flex items-center gap-1 text-gray-600">
        <Zap className="h-3 w-3" />
        <span className="font-medium">{settings.quality}</span>
      </div>

      {/* Aspect Ratio */}
      <div className="flex items-center gap-1 text-gray-600">
        <Ratio className="h-3 w-3" />
        <span className="font-medium">{settings.aspectRatio}</span>
      </div>

      {/* Selected Effect */}
      {selectedEffect && (
        <div className="flex items-center gap-1 text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
          <Sparkles className="h-3 w-3" />
          <span className="font-medium">{selectedEffect.name}</span>
        </div>
      )}
    </div>
  )
}
