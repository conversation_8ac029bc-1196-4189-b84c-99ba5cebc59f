'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Play,
  Pause,
  Download,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Clock,
  Volume2,
  VolumeX,
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { Button } from '@ui/components/button'
import { type VideoSettings, type VideoEffect } from '../config'

type TaskDetail = {
  id: string
  state: 'pending' | 'processing' | 'completed' | 'failed'
  video_url?: string
  progress?: number
  error_message?: string
  estimated_time?: number
}

type VideoResultDisplayProps = {
  taskDetail: TaskDetail | null
  isLoading: boolean
  error: string | null
  onDownload: (url: string) => void
  onRetry: () => void
  className?: string
  videoSettings: VideoSettings
  selectedEffect: VideoEffect | null
}

export function VideoResultDisplay({
  taskDetail,
  isLoading,
  error,
  onDownload,
  onRetry,
  className = '',
  videoSettings,
  selectedEffect,
}: VideoResultDisplayProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)

  // Handle video events
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)
    const handleEnded = () => {
      setIsPlaying(false)
      setCurrentTime(0)
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)
    video.addEventListener('ended', handleEnded)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
      video.removeEventListener('ended', handleEnded)
    }
  }, [taskDetail?.video_url])

  const handlePlayPause = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }

  const handleMuteToggle = () => {
    const video = videoRef.current
    if (!video) return

    video.muted = !isMuted
    setIsMuted(!isMuted)
  }

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    const video = videoRef.current
    if (!video || !duration) return

    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const percentage = clickX / rect.width
    const newTime = percentage * duration

    video.currentTime = newTime
    setCurrentTime(newTime)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  const getProgressPercentage = () => {
    if (!duration) return 0
    return (currentTime / duration) * 100
  }

  // Render different states
  const renderContent = () => {
    // Loading state
    if (isLoading || (taskDetail && taskDetail.state === 'processing')) {
      return (
        <div className="w-full aspect-video bg-gray-100 rounded-lg flex flex-col items-center justify-center">
          <div className="text-center">
            <div className="relative mb-4">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent mx-auto"></div>
              {taskDetail?.progress && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs font-medium text-blue-600">
                    {Math.round(taskDetail.progress)}%
                  </span>
                </div>
              )}
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Generating Your Video
            </h3>
            <p className="text-gray-600 mb-4">
              {taskDetail?.state === 'processing'
                ? 'AI is processing your video...'
                : 'Preparing video generation...'}
            </p>
            {taskDetail?.estimated_time && (
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Clock className="h-4 w-4" />
                <span>Estimated time: {taskDetail.estimated_time} minutes</span>
              </div>
            )}
          </div>
        </div>
      )
    }

    // Error state
    if (error || (taskDetail && taskDetail.state === 'failed')) {
      return (
        <div className="w-full aspect-video bg-red-50 border-2 border-red-200 rounded-lg flex flex-col items-center justify-center">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-900 mb-2">
              Generation Failed
            </h3>
            <p className="text-red-700 mb-4">
              {taskDetail?.error_message ||
                error ||
                'Something went wrong while generating your video.'}
            </p>
            <Button
              onClick={onRetry}
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-50"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </div>
      )
    }

    // Success state with video
    if (
      taskDetail &&
      taskDetail.state === 'completed' &&
      taskDetail.video_url
    ) {
      return (
        <div className="w-full">
          <div className="relative group bg-gray-900 rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              className="w-full aspect-video object-cover"
              src={taskDetail.video_url}
              muted={isMuted}
              playsInline
              preload="metadata"
            >
              Your browser does not support the video tag.
            </video>

            {/* Video Controls Overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors">
              {/* Play/Pause Button */}
              <div className="absolute inset-0 flex items-center justify-center">
                <button
                  onClick={handlePlayPause}
                  className="bg-white/90 hover:bg-white rounded-full p-4 shadow-lg transition-all transform hover:scale-105"
                >
                  {isPlaying ? (
                    <Pause className="h-6 w-6 text-gray-800" />
                  ) : (
                    <Play className="h-6 w-6 text-gray-800 ml-0.5" />
                  )}
                </button>
              </div>

              {/* Bottom Controls */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                <div className="flex items-center gap-3">
                  {/* Progress Bar */}
                  <div className="flex-1">
                    <div
                      className="h-1 bg-white/30 rounded-full cursor-pointer"
                      onClick={handleSeek}
                    >
                      <div
                        className="h-full bg-white rounded-full transition-all"
                        style={{ width: `${getProgressPercentage()}%` }}
                      />
                    </div>
                  </div>

                  {/* Time */}
                  <div className="text-white text-sm font-medium">
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </div>

                  {/* Mute Button */}
                  <button
                    onClick={handleMuteToggle}
                    className="text-white hover:text-gray-300 transition-colors"
                  >
                    {isMuted ? (
                      <VolumeX className="h-4 w-4" />
                    ) : (
                      <Volume2 className="h-4 w-4" />
                    )}
                  </button>

                  {/* Download Button */}
                  <button
                    onClick={() => onDownload(taskDetail.video_url!)}
                    className="text-white hover:text-gray-300 transition-colors"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Video Info */}
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <h3 className="font-semibold text-green-900">
                Video Generated Successfully!
              </h3>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-green-700">Effect:</span>
                <span className="text-green-900 font-medium ml-2">
                  {selectedEffect?.name || 'Custom'}
                </span>
              </div>
              <div>
                <span className="text-green-700">Duration:</span>
                <span className="text-green-900 font-medium ml-2">
                  {videoSettings.duration}s
                </span>
              </div>
              <div>
                <span className="text-green-700">Quality:</span>
                <span className="text-green-900 font-medium ml-2">
                  {videoSettings.quality}
                </span>
              </div>
              <div>
                <span className="text-green-700">Aspect Ratio:</span>
                <span className="text-green-900 font-medium ml-2">
                  {videoSettings.aspectRatio}
                </span>
              </div>
            </div>
          </div>
        </div>
      )
    }

    // Default empty state
    return (
      <div className="w-full aspect-video bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
            <Play className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Ready to Generate
          </h3>
          <p className="text-gray-600">
            Upload an image, select an effect, and click generate to create your
            video.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={`w-full ${className}`}>
      <AnimatePresence mode="wait">
        <motion.div
          key={taskDetail?.state || 'empty'}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderContent()}
        </motion.div>
      </AnimatePresence>
    </div>
  )
}
