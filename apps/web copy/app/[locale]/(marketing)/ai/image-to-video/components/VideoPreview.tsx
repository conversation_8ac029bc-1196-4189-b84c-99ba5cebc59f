'use client'

import { useEffect, useRef, useState } from 'react'
import { createPortal } from 'react-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { Play, Volume2, VolumeX } from 'lucide-react'
import { type VideoEffect } from '../config'

type VideoPreviewProps = {
  effect: VideoEffect | null
  isVisible: boolean
}

export function VideoPreview({ effect, isVisible }: VideoPreviewProps) {
  const [isMounted, setIsMounted] = useState(false)
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  // Ensure component is mounted (for SSR compatibility)
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Handle video loading
  useEffect(() => {
    if (effect && videoRef.current) {
      const video = videoRef.current

      const handleLoadedData = () => {
        setIsVideoLoaded(true)
        // Auto-play when loaded if visible
        if (isVisible) {
          video.play().catch(() => {
            // Ignore autoplay failures
          })
          setIsPlaying(true)
        }
      }

      const handleEnded = () => {
        setIsPlaying(false)
        // Loop the video
        video.currentTime = 0
        if (isVisible) {
          video.play().catch(() => {
            // Ignore autoplay failures
          })
          setIsPlaying(true)
        }
      }

      const handlePlay = () => setIsPlaying(true)
      const handlePause = () => setIsPlaying(false)

      video.addEventListener('loadeddata', handleLoadedData)
      video.addEventListener('ended', handleEnded)
      video.addEventListener('play', handlePlay)
      video.addEventListener('pause', handlePause)

      // Reset loading state when effect changes
      setIsVideoLoaded(false)
      setIsPlaying(false)

      return () => {
        video.removeEventListener('loadeddata', handleLoadedData)
        video.removeEventListener('ended', handleEnded)
        video.removeEventListener('play', handlePlay)
        video.removeEventListener('pause', handlePause)
      }
    }
  }, [effect, isVisible])

  // Handle play/pause when visibility changes
  useEffect(() => {
    if (videoRef.current && isVideoLoaded) {
      const video = videoRef.current

      if (isVisible) {
        video.play().catch(() => {
          // Ignore autoplay failures
        })
      } else {
        video.pause()
        video.currentTime = 0
      }
    }
  }, [isVisible, isVideoLoaded])

  // Calculate position for preview (right side of left sidebar)
  const getPreviewPosition = () => {
    // Position at the right edge of the 480px sidebar
    return {
      left: '480px', // Right edge of sidebar
      top: '10%',
      transform: 'translateY(-50%)',
    }
  }

  if (!isMounted || !effect || !isVisible) {
    return null
  }

  const previewContent = (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9, x: -20 }}
          animate={{ opacity: 1, scale: 1, x: 0 }}
          exit={{ opacity: 0, scale: 0.9, x: -20 }}
          transition={{
            duration: 0.2,
            ease: [0.25, 0.25, 0, 1],
          }}
          className="fixed z-50 pointer-events-auto"
          style={getPreviewPosition()}
        >
          <div className="bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden max-w-xs">
            {/* Preview header */}
            <div className="p-3 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 truncate">
                    {effect.name}
                  </h3>
                  <p className="text-xs text-gray-500 truncate">
                    {effect.description}
                  </p>
                </div>
              </div>
            </div>

            {/* Video container */}
            <div className="relative bg-gray-900">
              <video
                ref={videoRef}
                className="w-full h-full object-contain"
                loop
                playsInline
                preload="metadata"
                src={effect.videoUrl}
              >
                Your browser does not support the video tag.
              </video>

              {/* Loading overlay */}
              {!isVideoLoaded && (
                <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent mx-auto mb-2"></div>
                    <p className="text-xs text-gray-500">Loading preview...</p>
                  </div>
                </div>
              )}

              {/* Play/pause overlay */}
              {isVideoLoaded && !isPlaying && (
                <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                  <div className="bg-white/90 rounded-full p-3 shadow-lg">
                    <Play className="h-6 w-6 text-gray-800 ml-0.5" />
                  </div>
                </div>
              )}

              {/* Video controls overlay */}
              <div className="absolute bottom-2 left-2 right-2">
                <div className="bg-black/50 rounded-md px-2 py-1 backdrop-blur-sm">
                  <div className="flex items-center justify-between text-white text-xs">
                    <span className="truncate">Preview</span>
                    <div className="flex items-center gap-1">
                      {effect.tags.slice(0, 2).map((tag) => (
                        <span
                          key={tag}
                          className="bg-white/20 px-1.5 py-0.5 rounded text-[10px] capitalize"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer with effect info */}
            <div className="p-3 bg-gray-50">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span className="font-medium capitalize">Video Effect</span>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Ready</span>
                </div>
              </div>
            </div>
          </div>

          {/* Arrow pointing to the effect */}
          <div className="absolute right-full top-1/2 transform -translate-y-1/2 mr-1">
            <div className="w-0 h-0 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-white drop-shadow-sm"></div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )

  // Render in portal to ensure high z-index
  return createPortal(previewContent, document.body)
}
