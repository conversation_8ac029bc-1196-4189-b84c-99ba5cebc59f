// Image to Video Configuration
export const IMAGE_TO_VIDEO_CONFIG = {
  // Upload Configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  },

  // Video Effects Configuration
  effects: {
    // Action Effects
    action: [
      {
        id: 'fighting',
        name: 'Fighting Action',
        description: 'Transform image into dynamic fighting scene',
        imageUrl: '/images/image-to-video/fighting.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/fighting.mp4',
        prompt:
          'Based on the provided image, generate a dynamic video showing two characters fiercely fighting in the original scene of the image. The movements should be natural and smooth, including boxing, dodging, and wrestling, etc. Keep the background of the image unchanged and do not add any additional streets, buildings, or vehicles. The actions should be realistic and have a sense of conflict, with a tense atmosphere.',
        tags: ['action', 'fighting', 'dynamic', 'martial arts'],
      },
      {
        id: 'gun-shooting',
        name: 'Gun Shooting',
        description: 'Action-packed shooting scene effect',
        imageUrl: '/images/image-to-video/gun-shooting.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/gun-shooting.mp4',
        prompt:
          "Based on the person in the uploaded image, generate a dynamic video showcasing the action of the person holding a gun and shooting. The movements are natural and smooth, with clear details of the firearm, and the shooting posture is realistic and powerful. Maintain the original character features and background, avoiding any changes to the person's face and clothing. The overall image is realistic, with a tense atmosphere and a sense of action. There is no blurring, no lag, no unnatural movements, no character distortion, no background changes, no additional characters, no low resolution, no excessive lighting and shadows, and no firearm distortion.",
        tags: ['action', 'shooting', 'tactical', 'cinematic'],
      },
      {
        id: 'muscle',
        name: 'Muscle Power',
        description: 'Show muscular strength and power',
        imageUrl: '/images/image-to-video/muscle.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/muscle.mp4',
        prompt:
          "Based on the person in the uploaded image, generate a dynamic video that naturally removes clothing, showcasing the gradual revelation of the person's muscular contours. The movements are natural and smooth, highlighting the strength and muscles of the person. Pay attention to maintaining the same skin tone, keeping the person's facial and overall features unchanged, and keeping the background the same as the original scene. The image is realistic and expressive. There is no blurring, no lag, no deformation of the person, no unnatural movements, no excessive exposure, no changes in the background, no low resolution, no animation distortion, no extra characters, no exaggerated muscles, and no uncoordinated lighting and shadows.",
        tags: ['strength', 'muscle', 'fitness', 'power'],
      },
      {
        id: 'kiss',
        name: 'Romantic Kiss',
        description: 'Romantic kissing scene effect',
        imageUrl: '/images/image-to-video/kiss.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/kiss.mp4',
        prompt:
          'Create a romantic kissing scene, gentle movements, soft lighting, emotional intimacy, tender moment, cinematic romance',
        tags: ['romance', 'kiss', 'emotional', 'tender'],
      },
      {
        id: 'crush',
        name: 'Crushing Power',
        description: 'Powerful crushing action effect',
        imageUrl: '/images/image-to-video/crush.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/crush.mp4',
        prompt:
          'According to the uploaded image, generate a dynamic video where a metal column descends from above, crushing the main body to demonstrate the strong crushing force and deformation and fracture of objects under immense pressure. Metals, glass, and other objects are forcefully flattened, twisted, and burst, with fragments flying around, rich and realistic in deformation details. The scene is accompanied by vibration effects, flying debris and dust, enhancing the visual impact and sense of power. The overall dynamic flow is smooth, with clear details, and the background remains stable.',
        tags: ['power', 'crush', 'force', 'dramatic'],
      },
    ],

    // Transformation Effects
    transformation: [
      {
        id: 'squish',
        name: 'Squish Effect',
        description: 'Playful squishing transformation',
        imageUrl: '/images/image-to-video/squish.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/squish.mp4',
        prompt:
          'According to the uploaded image, generate a dynamic video where the main subject is transformed into a soft clay material with a strong squish effect. A pair of complete human arms and hands occupy most of the screen, squeezing and cracking the subject like clay, but without an explosion effect. It produces a huge deformation that is natural, smooth, and elastic. The overall movement shows the soft and plastic nature of clay, with a smooth and elastic rebound during the deformation process. The video style is cartoonish and fun, with the background remaining unchanged from the original image. The overall image is clear and smooth, with no blurring, no stutters, no character deformation distortion, no unnatural movements, no background changes, no low resolution, and no animation distortion.',
        tags: ['squish', 'playful', 'elastic', 'cartoon'],
      },

      {
        id: 'dizzydizzy',
        name: 'Dizzy Spin',
        description: 'Spinning dizzy effect',
        imageUrl: '/images/image-to-video/dizzydizzy.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/dizzydizzy.mp4',
        prompt:
          'The main body remains unchanged, rotating around the main body, rotating very fast, very fast, a dizzy sensation appears above the main body.',
        tags: ['dizzy', 'spinning', 'rotating', 'comedic'],
      },
      {
        id: 'growhair',
        name: 'Hair Growth',
        description: 'Dynamic hair growing effect',
        imageUrl: '/images/image-to-video/growhair.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/growhair.mp4',
        prompt:
          'Show hair growing rapidly, flowing hair movement, length transformation, natural hair dynamics, smooth growth animation',
        tags: ['hair', 'growth', 'flowing', 'natural'],
      },
    ],

    // Artistic Style
    artistic: [],

    // Themed Effects
    themed: [
      {
        id: 'puppy',
        name: 'Puppy Style',
        description: 'Cute puppy-like appearance',
        imageUrl: '/images/image-to-video/puppy.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/puppy.mp4',
        prompt:
          'Several fluffy little dogs appeared around the girl, and she happily petted the puppies.',
        tags: ['puppy', 'cute', 'animal', 'playful'],
      },
      {
        id: 'product',
        name: 'Product Style',
        description: 'Professional product photography style',
        imageUrl: '/images/image-to-video/product.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/product.mp4',
        prompt:
          'Create product photography style, professional presentation, clean background, commercial lighting, marketing aesthetic',
        tags: ['product', 'professional', 'commercial', 'clean'],
      },
      {
        id: 'running',
        name: 'Running',
        description: 'Happy running',
        imageUrl: '/images/image-to-video/running.png',
        videoUrl:
          'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/running.mp4',
        prompt:
          "The girl's running in the extended illustration is natural and generous, with a radiant smile.",
        tags: ['running'],
      },
    ],
  },

  // UI Configuration
  ui: {
    // Pagination
    historyPageSize: 10,

    // Video player settings
    videoPlayer: {
      autoplay: true,
      loop: true,
      muted: true,
      controls: true,
      preload: 'metadata',
    },

    // Animation and transition settings
    transitions: {
      duration: 200,
      easing: 'ease-in-out',
    },
  },

  // Video Generation Settings
  videoSettings: {
    // Duration options (in seconds)
    durations: [
      { value: 5, label: '5s', description: 'Standard duration' },
      { value: 8, label: '8s', description: 'Extended duration' },
    ],

    // Quality options
    qualities: [
      { value: '720p', label: '720p', description: 'High Definition' },
      { value: '1080p', label: '1080p', description: 'Full HD' },
    ],

    // Aspect ratio options
    aspectRatios: [
      { value: '16:9', label: '16:9', description: 'Widescreen', icon: '📺' },
      { value: '9:16', label: '9:16', description: 'Vertical', icon: '📲' },
      { value: '1:1', label: '1:1', description: 'Square', icon: '⬜' },
      { value: '4:3', label: '4:3', description: 'Standard', icon: '🖼️' },
      { value: '3:4', label: '3:4', description: 'Portrait', icon: '🖼️' },
    ],

    // Default settings
    defaults: {
      duration: 5,
      quality: '720p',
      aspectRatio: '16:9',
    },
  },

  // Feature Flags
  features: {
    enableHistory: true,
    enableEffectPreview: true,
    enableDownload: true,
    enableWebhook: true,
    enableAdvancedSettings: true,
    enablePromptEditing: true,

    // Development features
    enableDevTools: process.env.NODE_ENV === 'development',
    enableDebugLogs: process.env.NODE_ENV === 'development',
  },

  // Error Messages
  messages: {
    errors: {
      uploadFailed: 'Image upload failed, please try again.',
      generateFailed: 'Video generation failed, please try again.',
      loginRequired: 'Please login to use this feature.',
      invalidFileType: 'Please select a valid image file (JPG, PNG, WebP).',
      fileTooLarge: 'File size cannot exceed 10MB.',
      networkError: 'Network error, please check your connection.',
      noEffectSelected: 'Please select an effect before generating.',
      videoLoadFailed: 'Video preview loading failed.',
    },
    success: {
      uploadComplete: 'Image uploaded successfully!',
      generateComplete: 'Video generated successfully!',
      effectSelected: 'Effect applied successfully!',
      downloadStarted: 'Download started.',
    },
    info: {
      uploadInProgress: 'Uploading image...',
      generateInProgress: 'Generating video...',
      processingTask: 'Processing your request, this may take a few minutes...',
      selectEffect: 'Choose an effect to transform your image',
      hoverForPreview: 'Hover over effects to see video previews',
    },
  },
} as const

// Type definitions
export type VideoEffect = {
  readonly id: string
  readonly name: string
  readonly description: string
  readonly imageUrl: string
  readonly videoUrl: string
  readonly prompt: string
  readonly tags: readonly string[]
}

export type VideoSettings = {
  duration: number
  quality: string
  aspectRatio: string
}

// Helper functions
export const getAllEffects = (): readonly VideoEffect[] => {
  return [
    ...IMAGE_TO_VIDEO_CONFIG.effects.action,
    ...IMAGE_TO_VIDEO_CONFIG.effects.transformation,
    ...IMAGE_TO_VIDEO_CONFIG.effects.artistic,
    ...IMAGE_TO_VIDEO_CONFIG.effects.themed,
  ]
}

export const getEffectById = (id: string): VideoEffect | undefined => {
  return getAllEffects().find((effect) => effect.id === id)
}

export const isValidImageFile = (file: File): boolean => {
  const allowedTypes = IMAGE_TO_VIDEO_CONFIG.upload
    .allowedTypes as readonly string[]
  return (
    allowedTypes.includes(file.type) &&
    file.size <= IMAGE_TO_VIDEO_CONFIG.upload.maxFileSize
  )
}

// Video settings validation
export const validateVideoSettings = (
  duration: VideoSettings['duration'],
  quality: VideoSettings['quality'],
  aspectRatio: VideoSettings['aspectRatio']
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  // Check duration-quality compatibility
  if (duration === 8 && quality === '1080p') {
    errors.push('8-second videos cannot use 1080p quality. Please select 720p.')
  }

  if (quality === '1080p' && duration === 8) {
    errors.push(
      '1080p quality cannot be used with 8-second videos. Please select 5 seconds.'
    )
  }

  // Validate duration
  const validDurations = IMAGE_TO_VIDEO_CONFIG.videoSettings.durations.map(
    (d) => d.value
  )
  if (!validDurations.includes(duration as any)) {
    errors.push(
      `Invalid duration: ${duration}. Must be one of: ${validDurations.join(
        ', '
      )}`
    )
  }

  // Validate quality
  const validQualities = IMAGE_TO_VIDEO_CONFIG.videoSettings.qualities.map(
    (q) => q.value
  )
  if (!validQualities.includes(quality as any)) {
    errors.push(
      `Invalid quality: ${quality}. Must be one of: ${validQualities.join(
        ', '
      )}`
    )
  }

  // Validate aspect ratio
  const validAspectRatios =
    IMAGE_TO_VIDEO_CONFIG.videoSettings.aspectRatios.map((ar) => ar.value)
  if (!validAspectRatios.includes(aspectRatio as any)) {
    errors.push(
      `Invalid aspect ratio: ${aspectRatio}. Must be one of: ${validAspectRatios.join(
        ', '
      )}`
    )
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

// Get compatible options based on current selection
export const getCompatibleOptions = (
  currentDuration?: number,
  currentQuality?: string
) => {
  const { durations, qualities } = IMAGE_TO_VIDEO_CONFIG.videoSettings

  // Filter qualities based on duration
  const compatibleQualities =
    currentDuration === 8
      ? qualities.filter((q) => q.value === '720p')
      : qualities

  // Filter durations based on quality
  const compatibleDurations =
    currentQuality === '1080p'
      ? durations.filter((d) => d.value === 5)
      : durations

  return {
    compatibleQualities,
    compatibleDurations,
  }
}
