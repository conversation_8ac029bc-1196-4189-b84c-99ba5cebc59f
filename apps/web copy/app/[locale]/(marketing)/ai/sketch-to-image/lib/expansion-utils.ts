/**
 * AI Image Expansion Utilities
 * 图片扩图相关工具函数
 */

import { buildExpansionPrompt } from '../config'

/**
 * 图片扩图生成参数接口
 */
export interface ExpansionGenerationData {
  filesUrl: string[]
  prompt: string
  aspectRatio: string
  nVariants: string
  mode: string
  originalDimensions?: {
    width: number
    height: number
  }
}

/**
 * 获取图片的原始尺寸
 */
export const getImageDimensions = (
  imageUrl: string
): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      })
    }
    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }
    img.src = imageUrl
  })
}

/**
 * 将File对象转换为Blob URL
 */
export const fileToObjectURL = (file: File): string => {
  return URL.createObjectURL(file)
}

/**
 * 清理Blob URL
 */
export const revokeObjectURL = (url: string): void => {
  URL.revokeObjectURL(url)
}

/**
 * 检查图片文件类型是否有效
 */
export const isValidImageType = (file: File): boolean => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  return allowedTypes.includes(file.type)
}

/**
 * 格式化文件大小显示
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 构建API请求数据
 */
export const buildApiRequestData = (
  imageUrl: string,
  aspectRatio: string,
  imageCount: number,
  originalDimensions?: { width: number; height: number }
): ExpansionGenerationData => {
  // 生成扩图提示词
  const prompt = buildExpansionPrompt(aspectRatio)

  return {
    filesUrl: [imageUrl],
    prompt,
    aspectRatio,
    nVariants: imageCount.toString(),
    mode: 'image-to-image',
    originalDimensions,
  }
}

/**
 * 计算扩图比例信息
 */
export const calculateExpansionRatio = (
  originalWidth: number,
  originalHeight: number,
  targetWidth: number,
  targetHeight: number
): {
  widthRatio: number
  heightRatio: number
  overallRatio: number
} => {
  const widthRatio = targetWidth / originalWidth
  const heightRatio = targetHeight / originalHeight
  const overallRatio =
    (targetWidth * targetHeight) / (originalWidth * originalHeight)

  return {
    widthRatio,
    heightRatio,
    overallRatio,
  }
}

/**
 * 估算生成时间（基于图片尺寸和数量）
 */
export const estimateGenerationTime = (
  targetWidth: number,
  targetHeight: number,
  imageCount: number
): number => {
  // 基础时间：30秒
  const baseTime = 30

  // 基于像素数的时间增量（每100万像素增加10秒）
  const pixels = targetWidth * targetHeight
  const pixelTime = Math.floor(pixels / 1000000) * 10

  // 基于图片数量的时间增量
  const countTime = (imageCount - 1) * 15

  return baseTime + pixelTime + countTime
}

/**
 * 生成唯一的任务ID
 */
export const generateTaskId = (): string => {
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 8)
  return `expansion-${timestamp}-${randomStr}`
}

/**
 * 验证扩图参数
 */
export const validateExpansionParams = (params: {
  imageUrl: string | null
  aspectRatio: string
  imageCount: number
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!params.imageUrl) {
    errors.push('Image is required')
  }

  if (!params.aspectRatio) {
    errors.push('Aspect ratio is required')
  }

  if (params.imageCount < 1 || params.imageCount > 4) {
    errors.push('Image count must be between 1 and 4')
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * 下载文件功能
 */
export const downloadFile = async (
  url: string,
  filename: string
): Promise<void> => {
  try {
    const response = await fetch(url)
    const blob = await response.blob()

    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href)
  } catch (error) {
    console.error('Download failed:', error)
    throw error
  }
}

/**
 * 批量下载多个文件
 */
export const downloadMultipleFiles = async (
  urls: string[],
  filenamePrefix: string = 'expanded-image'
): Promise<void> => {
  for (let i = 0; i < urls.length; i++) {
    const filename = `${filenamePrefix}-${i + 1}.jpg`
    try {
      await downloadFile(urls[i], filename)
      // 添加延迟以避免浏览器阻止多个下载
      if (i < urls.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 500))
      }
    } catch (error) {
      console.error(`Failed to download file ${i + 1}:`, error)
    }
  }
}

/**
 * 复制文本到剪贴板
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text)
      return true
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      const successful = document.execCommand('copy')
      document.body.removeChild(textArea)
      return successful
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return false
  }
}

/**
 * 格式化生成时间显示
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds}s`
  }
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return remainingSeconds > 0
    ? `${minutes}m ${remainingSeconds}s`
    : `${minutes}m`
}

/**
 * 检查图片URL是否有效
 */
export const isValidImageUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}
