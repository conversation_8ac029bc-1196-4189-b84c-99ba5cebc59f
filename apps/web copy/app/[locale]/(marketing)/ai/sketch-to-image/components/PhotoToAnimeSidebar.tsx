'use client'

import React, { useState, useRef, useCallback, useMemo } from 'react'
import { Button } from '@ui/components/button'
import { RotateCcw } from 'lucide-react'
import { useToast } from '@ui/hooks/use-toast'
import { ImageUploadWithCrop } from '../../components/ui/ImageUploadWithCrop'
import { GenerateButton } from '../../face-swap/components/GenerateButton'
import { SKETCH_TO_IMAGE_CONFIG } from '../config'

// 新的生成参数接口 - 对齐 ImageToVideoSidebar 架构
export interface PhotoToAnimeGenerationParams {
  originalFile: File
  croppedBlob: Blob
  aspectRatio: string
  styleId: string
  imageCount: number
  customPrompt: string
  selectedGender: 'male' | 'female'
  uploadedImageUrl: string
}

// 新的组件 props 接口 - 简化状态管理
interface PhotoToAnimeSidebarProps {
  onGenerate: (params: PhotoToAnimeGenerationParams) => void
  isGenerating?: boolean
  onReset?: () => void
  className?: string
}

export const PhotoToAnimeSidebar = React.forwardRef<
  { getCroppedBlob: () => Promise<Blob | null> },
  PhotoToAnimeSidebarProps
>(({ onGenerate, isGenerating = false, onReset, className = '' }, ref) => {
  const { toast } = useToast()
  const imageUploadRef = useRef<{ getCroppedBlob: () => Promise<Blob | null> }>(
    null
  )

  // 内部状态管理 - 不再通过 props 传递
  const [originalFile, setOriginalFile] = useState<File | null>(null)
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadError, setUploadError] = useState<string | null>(null)

  // Photo-to-anime 特有状态
  const [selectedStyle, setSelectedStyle] = useState<string>('ghibli')
  const [aspectRatio, setAspectRatio] = useState<string>(
    SKETCH_TO_IMAGE_CONFIG.generation.defaults.aspectRatio
  )
  const [imageCount, setImageCount] = useState<number>(1)
  const [customPrompt, setCustomPrompt] = useState<string>('')
  const [selectedGender, setSelectedGender] = useState<'male' | 'female'>(
    'female'
  )

  // 生成唯一文件名的函数 - 复用 ImageToVideoSidebar 逻辑
  const generateUniqueFileName = useCallback((blob: Blob): string => {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 8)

    let extension = 'jpg'
    if (blob.type === 'image/png') {
      extension = 'png'
    } else if (blob.type === 'image/webp') {
      extension = 'webp'
    } else if (blob.type === 'image/jpeg') {
      extension = 'jpg'
    }

    return `photo-to-anime-${timestamp}-${randomStr}.${extension}`
  }, [])

  // 上传图片到OSS的函数 - 复用 ImageToVideoSidebar 逻辑
  const uploadImageToOSS = useCallback(
    async (blob: Blob): Promise<string> => {
      try {
        setIsUploading(true)
        setUploadError(null)

        const fileName = generateUniqueFileName(blob)

        const formData = new FormData()
        formData.append('file', blob, fileName)

        const response = await fetch('/api/upload', {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error(`上传失败: ${response.status}`)
        }

        const result = await response.json()

        if (result.code !== 200) {
          throw new Error(result.msg || '上传失败')
        }

        return result.data.url
      } catch (error) {
        console.error('图片上传失败:', error)
        const errorMessage = error instanceof Error ? error.message : '上传失败'
        setUploadError(errorMessage)
        toast({
          title: '上传失败',
          description: errorMessage,
          variant: 'error',
        })
        throw error
      } finally {
        setIsUploading(false)
        setUploadProgress(0)
      }
    },
    [toast, generateUniqueFileName]
  )

  // 处理图片选择 - 对齐 ImageToVideoSidebar 逻辑
  const handleImageChange = useCallback(
    async (file: File, blob: Blob | null) => {
      setOriginalFile(file)

      if (blob) {
        const url = URL.createObjectURL(blob)
        setImageUrl(url)
        setUploadError(null)
      }
    },
    []
  )

  // 处理图片移除
  const handleImageRemove = useCallback(() => {
    setOriginalFile(null)
    setUploadError(null)
    if (imageUrl) {
      URL.revokeObjectURL(imageUrl)
      setImageUrl(null)
    }
  }, [imageUrl])

  // 暴露给外部的 getCroppedBlob 方法
  React.useImperativeHandle(
    ref,
    () => ({
      getCroppedBlob: async () => {
        if (!imageUploadRef.current) return null
        return await imageUploadRef.current.getCroppedBlob()
      },
    }),
    []
  )

  // 检查是否可以生成
  const canGenerate =
    originalFile && !isGenerating && !isUploading && !uploadError

  // 处理生成按钮点击 - 完整的生成流程
  const handleGenerate = useCallback(async () => {
    if (!canGenerate) {
      toast({
        title: 'Cannot generate',
        description: 'Please ensure image is uploaded',
        variant: 'error',
      })
      return
    }

    if (!imageUploadRef.current) {
      toast({
        title: 'Cannot generate',
        description: 'Image upload component not initialized',
        variant: 'error',
      })
      return
    }

    try {
      // 获取裁剪后的图片
      const croppedBlob = await imageUploadRef.current.getCroppedBlob()
      if (!croppedBlob) {
        toast({
          title: 'Cannot generate',
          description: 'Please crop the image first',
          variant: 'error',
        })
        return
      }

      // 上传到OSS
      const ossUrl = await uploadImageToOSS(croppedBlob)

      // 构建生成参数
      const params = getGenerationParams(croppedBlob, ossUrl)

      if (params) {
        onGenerate(params)
      }
    } catch (error) {
      console.error('生成失败:', error)
    }
  }, [canGenerate, uploadImageToOSS, onGenerate, toast])

  // 构建生成参数
  function getGenerationParams(
    croppedBlob: Blob,
    ossUrl: string
  ): PhotoToAnimeGenerationParams | null {
    if (!originalFile || !ossUrl) return null
    return {
      originalFile,
      croppedBlob,
      aspectRatio,
      styleId: selectedStyle,
      imageCount,
      customPrompt: customPrompt.trim() || '',
      selectedGender,
      uploadedImageUrl: ossUrl,
    }
  }

  // 重置所有设置
  const handleReset = useCallback(() => {
    setOriginalFile(null)
    setSelectedStyle('ghibli')
    setAspectRatio(SKETCH_TO_IMAGE_CONFIG.generation.defaults.aspectRatio)
    setImageCount(1)
    setCustomPrompt('')
    setSelectedGender('female')
    setUploadError(null)
    if (imageUrl) {
      URL.revokeObjectURL(imageUrl)
      setImageUrl(null)
    }
    onReset?.()
  }, [imageUrl, onReset])

  return (
    <>
      {/* 标题栏 - 固定高度 */}
      <div className="p-4 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center justify-between">
          <h2 className="text-md font-semibold text-gray-900">
            Transform to Anime Style
          </h2>
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            disabled={isGenerating}
            className="flex items-center justify-center gap-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
          >
            <RotateCcw className="w-4 h-4" />
            <span>Reset All</span>
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto min-h-0">
        {/* 内容区域 */}
        <div className="p-4 space-y-4">
          {/* 图片上传区域 */}
          <div>
            <ImageUploadWithCrop
              ref={imageUploadRef}
              imageUrl={imageUrl}
              isUploading={isUploading}
              progress={uploadProgress}
              onImageChange={handleImageChange}
              onRemove={handleImageRemove}
              aspectRatio={aspectRatio}
              disabled={isGenerating}
              maxFileSize={SKETCH_TO_IMAGE_CONFIG.upload.maxFileSize}
              allowedTypes={[...SKETCH_TO_IMAGE_CONFIG.upload.allowedTypes]}
            />
          </div>

          {/* 分割线 */}
          <hr className="border-gray-200 my-2" />

          {/* 宽高比选择 */}
          <div className="space-y-3">
            <label className="text-md font-semibold text-gray-900">
              Aspect Ratio
            </label>
            <div className="grid grid-cols-3 gap-2">
              {SKETCH_TO_IMAGE_CONFIG.generation.aspectRatios
                .slice(0, 3)
                .map((ratio) => (
                  <button
                    key={ratio.value}
                    onClick={() => setAspectRatio(ratio.value)}
                    className={`p-1 text-sm font-medium border rounded-lg transition-colors ${
                      aspectRatio === ratio.value
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-300 text-gray-700 hover:border-gray-400'
                    }`}
                  >
                    {ratio.label}
                  </button>
                ))}
            </div>
          </div>

          {/* 分割线 */}
          <hr className="border-gray-200 my-2" />
        </div>
      </div>

      {/* 固定生成按钮 - 固定底部 */}
      <div className="flex-shrink-0 border-t border-gray-200">
        <GenerateButton
          swapImageUrl={imageUrl}
          requireTwoImages={false}
          isGenerating={isGenerating || isUploading}
          onGenerate={handleGenerate}
          generateError={uploadError}
        />
      </div>
    </>
  )
})

PhotoToAnimeSidebar.displayName = 'PhotoToAnimeSidebar'

export default PhotoToAnimeSidebar
