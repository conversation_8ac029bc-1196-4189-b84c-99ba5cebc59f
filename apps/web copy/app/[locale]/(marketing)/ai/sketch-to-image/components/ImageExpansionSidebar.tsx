'use client'

import React, { useState, useRef, useCallback, useMemo } from 'react'
import { Button } from '@ui/components/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
import { RotateCcw } from 'lucide-react'
import { useToast } from '@ui/hooks/use-toast'
import { ImageUploadWithCrop } from '../../components/ui/ImageUploadWithCrop'
import { GenerateButton } from '../../face-swap/components/GenerateButton'
import { IMAGE_EXPANSION_CONFIG, buildExpansionPrompt } from '../config'

// 图片扩图生成参数接口
export interface ImageExpansionGenerationParams {
  originalFile: File
  croppedBlob: Blob
  aspectRatio: string
  imageCount: number
  uploadedImageUrl: string
}

// 组件 props 接口
interface ImageExpansionSidebarProps {
  onGenerate: (params: ImageExpansionGenerationParams) => void
  onPreviewUpdate?: (imageUrl: string | null, aspectRatio: string) => void
  isGenerating?: boolean
  onReset?: () => void
  className?: string
}

// 比例视觉组件
const RatioVisual: React.FC<{ ratio: string; isSelected: boolean }> = ({
  ratio,
  isSelected,
}) => {
  // 计算长方形尺寸
  const getRectangleDimensions = (ratio: string) => {
    if (ratio === '1.2x') {
      // 1.2x用稍大的正方形，添加小点表示放大
      return { width: 20, height: 20, isScale: true }
    }

    const [w, h] = ratio.split(':').map(Number)
    const maxSize = 28 // 增大最大尺寸以便更好显示比例
    const minSize = 6 // 减小最小尺寸以便显示极端比例
    const aspectRatio = w / h

    let width, height
    if (aspectRatio > 1) {
      // 横向比例
      width = maxSize
      height = Math.round(maxSize / aspectRatio)
      height = Math.max(height, minSize)
    } else {
      // 纵向比例
      height = maxSize
      width = Math.round(maxSize * aspectRatio)
      width = Math.max(width, minSize)
    }

    return { width, height, isScale: false }
  }

  const { width, height, isScale } = getRectangleDimensions(ratio)

  return (
    <div className="relative">
      <div
        className={`border-2 rounded-[1px] ${
          isSelected
            ? 'border-blue-500 bg-blue-100'
            : 'border-gray-400 bg-gray-100'
        }`}
        style={{ width, height }}
      />
      {isScale && (
        <div
          className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-1 rounded-full ${
            isSelected ? 'bg-blue-600' : 'bg-gray-600'
          }`}
        />
      )}
    </div>
  )
}

export const ImageExpansionSidebar = React.forwardRef<
  { getCroppedBlob: () => Promise<Blob | null> },
  ImageExpansionSidebarProps
>(
  (
    {
      onGenerate,
      onPreviewUpdate,
      isGenerating = false,
      onReset,
      className = '',
    },
    ref
  ) => {
    const { toast } = useToast()
    const imageUploadRef = useRef<{
      getCroppedBlob: () => Promise<Blob | null>
    }>(null)

    // 内部状态管理
    const [originalFile, setOriginalFile] = useState<File | null>(null)
    const [imageUrl, setImageUrl] = useState<string | null>(null)
    const [isUploading, setIsUploading] = useState(false)
    const [uploadProgress, setUploadProgress] = useState(0)
    const [uploadError, setUploadError] = useState<string | null>(null)

    // 图片扩图特有状态
    const [aspectRatio, setAspectRatio] = useState<string>(
      IMAGE_EXPANSION_CONFIG.generation.defaults.aspectRatio
    )
    const [imageCount, setImageCount] = useState<number>(1)

    // 生成唯一文件名的函数
    const generateUniqueFileName = useCallback((blob: Blob): string => {
      const timestamp = Date.now()
      const randomStr = Math.random().toString(36).substring(2, 8)

      let extension = 'jpg'
      if (blob.type === 'image/png') {
        extension = 'png'
      } else if (blob.type === 'image/webp') {
        extension = 'webp'
      } else if (blob.type === 'image/jpeg') {
        extension = 'jpg'
      }

      return `image-expansion-${timestamp}-${randomStr}.${extension}`
    }, [])

    // 上传图片到OSS的函数
    const uploadImageToOSS = useCallback(
      async (blob: Blob): Promise<string> => {
        try {
          setIsUploading(true)
          setUploadError(null)

          const fileName = generateUniqueFileName(blob)

          const formData = new FormData()
          formData.append('file', blob, fileName)

          const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData,
          })

          if (!response.ok) {
            throw new Error(`Upload failed: ${response.status}`)
          }

          const result = await response.json()

          if (result.code !== 200) {
            throw new Error(result.msg || 'Upload failed')
          }

          return result.data.url
        } catch (error) {
          console.error('Image upload failed:', error)
          const errorMessage =
            error instanceof Error ? error.message : 'Upload failed'
          setUploadError(errorMessage)
          toast({
            title: 'Upload Failed',
            description: errorMessage,
            variant: 'error',
          })
          throw error
        } finally {
          setIsUploading(false)
          setUploadProgress(0)
        }
      },
      [toast, generateUniqueFileName]
    )

    // 处理图片选择
    const handleImageChange = useCallback(
      async (file: File, blob: Blob | null) => {
        setOriginalFile(file)

        if (blob) {
          const url = URL.createObjectURL(blob)
          setImageUrl(url)
          setUploadError(null)
        }
      },
      []
    )

    // 处理图片移除
    const handleImageRemove = useCallback(() => {
      setOriginalFile(null)
      setUploadError(null)
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl)
        setImageUrl(null)
      }
    }, [imageUrl])

    // 暴露给外部的 getCroppedBlob 方法
    React.useImperativeHandle(
      ref,
      () => ({
        getCroppedBlob: async () => {
          if (!imageUploadRef.current) return null
          return await imageUploadRef.current.getCroppedBlob()
        },
      }),
      []
    )

    // 检查是否可以生成
    const canGenerate =
      originalFile && !isGenerating && !isUploading && !uploadError

    // 处理生成按钮点击
    const handleGenerate = useCallback(async () => {
      if (!canGenerate) {
        toast({
          title: 'Cannot generate',
          description: 'Please ensure image is uploaded',
          variant: 'error',
        })
        return
      }

      if (!imageUploadRef.current) {
        toast({
          title: 'Cannot generate',
          description: 'Image upload component not initialized',
          variant: 'error',
        })
        return
      }

      try {
        // 获取裁剪后的图片
        const croppedBlob = await imageUploadRef.current.getCroppedBlob()
        if (!croppedBlob) {
          toast({
            title: 'Cannot generate',
            description: 'Please crop the image first',
            variant: 'error',
          })
          return
        }

        // 上传到OSS
        const ossUrl = await uploadImageToOSS(croppedBlob)

        // 构建生成参数
        const params = getGenerationParams(croppedBlob, ossUrl)

        if (params) {
          onGenerate(params)
        }
      } catch (error) {
        console.error('Generation failed:', error)
      }
    }, [canGenerate, uploadImageToOSS, onGenerate, toast])

    // 构建生成参数
    function getGenerationParams(
      croppedBlob: Blob,
      ossUrl: string
    ): ImageExpansionGenerationParams | null {
      if (!originalFile || !ossUrl) return null
      return {
        originalFile,
        croppedBlob,
        aspectRatio,
        imageCount,
        uploadedImageUrl: ossUrl,
      }
    }

    // 重置所有设置
    const handleReset = useCallback(() => {
      setOriginalFile(null)
      setAspectRatio(IMAGE_EXPANSION_CONFIG.generation.defaults.aspectRatio)
      setImageCount(1)
      setUploadError(null)
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl)
        setImageUrl(null)
      }
      onReset?.()
    }, [imageUrl, onReset])

    // 预览参数变化时通知父组件
    React.useEffect(() => {
      if (onPreviewUpdate) {
        onPreviewUpdate(imageUrl, aspectRatio)
      }
    }, [imageUrl, aspectRatio, onPreviewUpdate])

    return (
      <>
        {/* 标题栏 */}
        <div className="p-4 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center justify-between">
            <h2 className="text-md font-semibold text-gray-900">
              AI Image Expansion
            </h2>
            <Button
              variant="outline"
              size="sm"
              onClick={handleReset}
              disabled={isGenerating}
              className="flex items-center justify-center gap-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset All</span>
            </Button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto min-h-0">
          {/* 内容区域 */}
          <div className="p-4 space-y-4">
            {/* 图片上传区域 */}
            <div>
              <label className="text-sm font-medium text-gray-900 mb-2 block">
                Upload Image
              </label>
              <ImageUploadWithCrop
                ref={imageUploadRef}
                imageUrl={imageUrl}
                isUploading={isUploading}
                progress={uploadProgress}
                onImageChange={handleImageChange}
                onRemove={handleImageRemove}
                aspectRatio={aspectRatio}
                disabled={isGenerating}
                maxFileSize={IMAGE_EXPANSION_CONFIG.upload.maxFileSize}
                allowedTypes={[...IMAGE_EXPANSION_CONFIG.upload.allowedTypes]}
              />
            </div>

            {/* 分割线 */}
            <hr className="border-gray-200 my-4" />

            {/* Ratio区域 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-900">
                Target Ratio
              </label>
              <div className="grid grid-cols-3 gap-3">
                {IMAGE_EXPANSION_CONFIG.generation.aspectRatios.map((ratio) => (
                  <button
                    key={ratio.value}
                    onClick={() => setAspectRatio(ratio.value)}
                    className={`h-16 p-3 text-xs font-medium border rounded-lg transition-colors flex flex-col items-center justify-center gap-2 ${
                      aspectRatio === ratio.value
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-300 text-gray-700 hover:border-gray-400'
                    }`}
                    title={ratio.description}
                  >
                    <RatioVisual
                      ratio={ratio.value}
                      isSelected={aspectRatio === ratio.value}
                    />
                    <span className="text-xs whitespace-nowrap">
                      {ratio.label}
                    </span>
                  </button>
                ))}
              </div>
            </div>

            {/* 分割线 */}
            <hr className="border-gray-200 my-4" />

            {/* 图片数量区域 */}
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-900">
                Number of Images
              </label>
              <Select
                value={imageCount.toString()}
                onValueChange={(value) => setImageCount(parseInt(value))}
                disabled={isGenerating}
              >
                <SelectTrigger className="w-full h-10">
                  <SelectValue placeholder="Select number of images" />
                </SelectTrigger>
                <SelectContent>
                  {IMAGE_EXPANSION_CONFIG.generation.variants.map((variant) => (
                    <SelectItem key={variant.value} value={variant.value}>
                      {variant.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 固定生成按钮 */}
        <div className="flex-shrink-0 border-t border-gray-200">
          <GenerateButton
            swapImageUrl={imageUrl}
            requireTwoImages={false}
            isGenerating={isGenerating || isUploading}
            onGenerate={handleGenerate}
            generateError={uploadError}
          />
        </div>
      </>
    )
  }
)

ImageExpansionSidebar.displayName = 'ImageExpansionSidebar'

export default ImageExpansionSidebar
