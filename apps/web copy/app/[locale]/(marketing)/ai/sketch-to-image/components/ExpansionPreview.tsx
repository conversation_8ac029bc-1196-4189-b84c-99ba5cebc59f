'use client'

import React, { useMemo, useEffect, useState } from 'react'
import { calculateTargetDimensions, parseAspectRatio } from '../config'

interface ExpansionPreviewProps {
  imageUrl: string | null
  aspectRatio: string
  className?: string
}

export const ExpansionPreview: React.FC<ExpansionPreviewProps> = ({
  imageUrl,
  aspectRatio,
  className = '',
}) => {
  const [originalDimensions, setOriginalDimensions] = useState<{
    width: number
    height: number
  } | null>(null)

  // 获取原图的实际尺寸
  useEffect(() => {
    if (!imageUrl) {
      setOriginalDimensions(null)
      return
    }

    const img = new Image()
    img.onload = () => {
      setOriginalDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
      })
    }
    img.src = imageUrl
  }, [imageUrl])

  // 计算预览尺寸和位置
  const previewData = useMemo(() => {
    if (!originalDimensions || !imageUrl) {
      return null
    }

    // 计算目标尺寸
    const targetDimensions = calculateTargetDimensions(
      originalDimensions.width,
      originalDimensions.height,
      aspectRatio
    )

    // 预览容器的最大尺寸
    const maxPreviewWidth = 300
    const maxPreviewHeight = 300

    // 计算缩放比例以适应预览容器
    const scaleX = maxPreviewWidth / targetDimensions.width
    const scaleY = maxPreviewHeight / targetDimensions.height
    const scale = Math.min(scaleX, scaleY, 1) // 不放大，只缩小

    // 预览画布尺寸
    const previewCanvasWidth = targetDimensions.width * scale
    const previewCanvasHeight = targetDimensions.height * scale

    // 原图在预览中的尺寸
    const originalPreviewWidth = originalDimensions.width * scale
    const originalPreviewHeight = originalDimensions.height * scale

    // 原图在画布中的居中位置
    const originalX = (previewCanvasWidth - originalPreviewWidth) / 2
    const originalY = (previewCanvasHeight - originalPreviewHeight) / 2

    return {
      canvasWidth: previewCanvasWidth,
      canvasHeight: previewCanvasHeight,
      originalWidth: originalPreviewWidth,
      originalHeight: originalPreviewHeight,
      originalX,
      originalY,
      targetDimensions,
      scale,
    }
  }, [originalDimensions, aspectRatio, imageUrl])

  if (!imageUrl || !previewData) {
    return (
      <div
        className={`w-full h-full flex items-center justify-center ${className}`}
      >
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
            <span className="text-2xl">🖼️</span>
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-900">
              Preview Expansion
            </h3>
            <p className="text-sm text-gray-600">
              Upload an image to see the expansion preview
            </p>
          </div>
        </div>
      </div>
    )
  }

  // 创建棋盘格背景样式
  const checkerboardStyle: React.CSSProperties = {
    backgroundImage: `
      linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)
    `,
    backgroundSize: '20px 20px',
    backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
    backgroundColor: '#fafafa',
  }

  return (
    <div
      className={`w-full h-full flex flex-col items-center justify-center p-4 ${className}`}
    >
      {/* 预览信息 */}
      <div className="mb-4 text-center">
        <h3 className="text-sm font-medium text-gray-900 mb-1">
          Expansion Preview
        </h3>
        <div className="text-xs text-gray-600 space-y-1">
          <p>
            Original: {originalDimensions?.width} × {originalDimensions?.height}
          </p>
          <p>
            Target: {previewData.targetDimensions.width} ×{' '}
            {previewData.targetDimensions.height}
          </p>
          <p className="text-blue-600">
            {aspectRatio === '1.2x' ? '1.2x Scale' : `Ratio ${aspectRatio}`}
          </p>
        </div>
      </div>

      {/* 预览画布 */}
      <div className="relative border border-gray-300 rounded-lg overflow-hidden shadow-sm">
        {/* 背景画布（扩图区域） */}
        <div
          style={{
            width: previewData.canvasWidth,
            height: previewData.canvasHeight,
            ...checkerboardStyle,
          }}
          className="relative"
        >
          {/* 原图 */}
          <div
            className="absolute border-2 border-blue-500 rounded shadow-md overflow-hidden"
            style={{
              left: previewData.originalX,
              top: previewData.originalY,
              width: previewData.originalWidth,
              height: previewData.originalHeight,
            }}
          >
            <img
              src={imageUrl}
              alt="Original image"
              className="w-full h-full object-cover"
              style={{
                width: '100%',
                height: '100%',
              }}
            />
          </div>

          {/* 扩图区域指示器 */}
          <div className="absolute inset-0 pointer-events-none">
            {/* 顶部扩图区域 */}
            {previewData.originalY > 0 && (
              <div
                className="absolute top-0 left-0 right-0 bg-black bg-opacity-5 border border-dashed border-gray-400"
                style={{ height: previewData.originalY }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded">
                    Expand
                  </span>
                </div>
              </div>
            )}

            {/* 底部扩图区域 */}
            {previewData.originalY + previewData.originalHeight <
              previewData.canvasHeight && (
              <div
                className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-5 border border-dashed border-gray-400"
                style={{
                  height:
                    previewData.canvasHeight -
                    (previewData.originalY + previewData.originalHeight),
                }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded">
                    Expand
                  </span>
                </div>
              </div>
            )}

            {/* 左侧扩图区域 */}
            {previewData.originalX > 0 && (
              <div
                className="absolute top-0 bottom-0 left-0 bg-black bg-opacity-5 border border-dashed border-gray-400"
                style={{ width: previewData.originalX }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded rotate-90">
                    Expand
                  </span>
                </div>
              </div>
            )}

            {/* 右侧扩图区域 */}
            {previewData.originalX + previewData.originalWidth <
              previewData.canvasWidth && (
              <div
                className="absolute top-0 bottom-0 right-0 bg-black bg-opacity-5 border border-dashed border-gray-400"
                style={{
                  width:
                    previewData.canvasWidth -
                    (previewData.originalX + previewData.originalWidth),
                }}
              >
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs text-gray-500 bg-white bg-opacity-75 px-2 py-1 rounded -rotate-90">
                    Expand
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* 原图标签 */}
          <div
            className="absolute bg-blue-500 text-white text-xs px-2 py-1 rounded-br"
            style={{
              left: previewData.originalX,
              top: previewData.originalY,
            }}
          >
            Original
          </div>
        </div>
      </div>

      {/* 说明文字 */}
      <div className="mt-3 text-center">
        <p className="text-xs text-gray-500 max-w-xs">
          Gray areas show where the AI will expand your image. The original
          image (blue border) will remain unchanged.
        </p>
      </div>
    </div>
  )
}

export default ExpansionPreview
