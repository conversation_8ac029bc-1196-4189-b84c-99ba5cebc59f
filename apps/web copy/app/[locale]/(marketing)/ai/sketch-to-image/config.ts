// AI Image Expansion Configuration
//
export const IMAGE_EXPANSION_CONFIG = {
  // 上传配置
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
  },

  // 生成参数配置
  generation: {
    // 输出比例选项 - 8种预设比例 + 1.2x扩展
    aspectRatios: [
      { value: '1:1', label: '1:1', description: 'Square', icon: '⬜' },
      { value: '3:2', label: '3:2', description: 'Landscape', icon: '🖼️' },
      { value: '2:3', label: '2:3', description: 'Portrait', icon: '🖼️' },
      { value: '4:5', label: '4:5', description: 'Portrait', icon: '📱' },
      { value: '5:4', label: '5:4', description: 'Landscape', icon: '🖥️' },
      { value: '16:9', label: '16:9', description: 'Widescreen', icon: '📺' },
      { value: '9:16', label: '9:16', description: 'Vertical', icon: '📱' },
      { value: '21:9', label: '21:9', description: 'Ultrawide', icon: '🎬' },
      {
        value: '1.2x',
        label: '1.2x',
        description: '1.2x Original',
        icon: '📐',
      },
    ],

    // 图片数量选项
    variants: [
      { value: '1', label: '1 image' },
      { value: '2', label: '2 images' },
      { value: '3', label: '3 images' },
      { value: '4', label: '4 images' },
    ],

    // 默认设置
    defaults: {
      aspectRatio: '1.2x',
      variants: '1',
    },

    // 扩图提示词模板
    promptTemplates: {
      ratioExpansion:
        'Expand this image to {ratio}, keeping all original content visible. Ensure the style of the filled areas matches the original image.',
      scaleExpansion:
        'Upscale to the target resolution without cropping or covering any part. The newly filled regions should seamlessly blend with the original style, color, and lighting.',
      general:
        "When extending the canvas, make sure the completion is consistent with the original image's style and details.",
    },
  },

  // UI配置
  ui: {
    // 历史记录分页
    historyPageSize: 10,

    // 图片预览设置
    imagePreview: {
      maxWidth: 400,
      maxHeight: 400,
      quality: 0.8,
    },

    // 预览画布设置
    expansionPreview: {
      maxPreviewWidth: 300,
      maxPreviewHeight: 300,
      gridSize: 10, // 背景网格大小
      gridColor: '#f0f0f0',
      backgroundColor: '#fafafa',
    },

    // 动画和过渡设置
    transitions: {
      duration: 200,
      easing: 'ease-in-out',
    },
  },

  // API配置
  api: {
    generateEndpoint: '/api/images/generate',
    statusEndpoint: '/api/images/record-info',
    uploadEndpoint: '/api/upload',
    pollInterval: 5000,
    maxPollTime: 600000, // 10分钟
  },
}

// 类型定义
export type AspectRatio = {
  readonly value: string
  readonly label: string
  readonly description: string
  readonly icon: string
}

export type VariantOption = {
  readonly value: string
  readonly label: string
}

// 工具函数

/**
 * 获取所有可用的宽高比选项
 */
export const getAllAspectRatios = (): readonly AspectRatio[] => {
  return IMAGE_EXPANSION_CONFIG.generation.aspectRatios
}

/**
 * 根据比例值获取比例配置
 */
export const getAspectRatioById = (value: string): AspectRatio | undefined => {
  return IMAGE_EXPANSION_CONFIG.generation.aspectRatios.find(
    (ratio) => ratio.value === value
  )
}

/**
 * 检查文件是否有效
 */
export const isValidImageFile = (file: File): boolean => {
  return (
    IMAGE_EXPANSION_CONFIG.upload.allowedTypes.includes(file.type) &&
    file.size <= IMAGE_EXPANSION_CONFIG.upload.maxFileSize
  )
}

/**
 * 构建扩图提示词
 */
export const buildExpansionPrompt = (aspectRatio: string): string => {
  const templates = IMAGE_EXPANSION_CONFIG.generation.promptTemplates

  let basePrompt: string

  if (aspectRatio === '1.2x') {
    basePrompt = templates.scaleExpansion
  } else {
    basePrompt = templates.ratioExpansion.replace('{ratio}', aspectRatio)
  }

  // 添加通用指导
  basePrompt += ` ${templates.general}`

  return basePrompt
}

/**
 * 解析宽高比字符串为数值
 */
export const parseAspectRatio = (
  ratio: string
): { width: number; height: number } => {
  if (ratio === '1.2x') {
    // 1.2x模式返回特殊标识
    return { width: 1.2, height: 1 }
  }
  const [width, height] = ratio.split(':').map(Number)
  return { width, height }
}

/**
 * 计算目标尺寸基于原图尺寸和目标比例
 */
export const calculateTargetDimensions = (
  originalWidth: number,
  originalHeight: number,
  targetRatio: string
): { width: number; height: number } => {
  if (targetRatio === '1.2x') {
    return {
      width: Math.round(originalWidth * 1.2),
      height: Math.round(originalHeight * 1.2),
    }
  }

  const { width: ratioWidth, height: ratioHeight } =
    parseAspectRatio(targetRatio)
  const targetAspectRatio = ratioWidth / ratioHeight
  const originalAspectRatio = originalWidth / originalHeight

  let targetWidth: number
  let targetHeight: number

  if (originalAspectRatio > targetAspectRatio) {
    // 原图更宽，基于宽度计算
    targetWidth = originalWidth
    targetHeight = Math.round(originalWidth / targetAspectRatio)
  } else {
    // 原图更高，基于高度计算
    targetHeight = originalHeight
    targetWidth = Math.round(originalHeight * targetAspectRatio)
  }

  return { width: targetWidth, height: targetHeight }
}
