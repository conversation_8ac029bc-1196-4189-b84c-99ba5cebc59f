'use client'

import React from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@ui/components/dropdown-menu'
import { Info } from 'lucide-react'
import { MEMORY_VIDEO_CONFIG } from '../config'

interface MemoryVideoTipsPopoverProps {
  children: React.ReactNode
}

export function MemoryVideoTipsPopover({
  children,
}: MemoryVideoTipsPopoverProps) {
  const { uploadGuide } = MEMORY_VIDEO_CONFIG

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        side="right"
        align="start"
        className="w-[400px] bg-white p-0"
        sideOffset={8}
      >
        <div className="p-4">
          <div className="space-y-4">
            {/* Upload Guidelines Section */}
            <div className="border rounded-lg overflow-hidden">
              {/* Header */}
              <div className="flex items-center gap-3 bg-blue-50 p-4 border-b">
                <div className="flex items-center justify-center w-6 h-6 bg-blue-500 rounded-full flex-shrink-0">
                  <Info className="w-4 h-4 text-white" />
                </div>
                <div className="text-center">
                  <h3 className="text-base font-semibold text-blue-700">
                    {uploadGuide.title}
                  </h3>
                  <p className="text-sm text-blue-600 mt-1">
                    {uploadGuide.subtitle}
                  </p>
                </div>
              </div>

              <div className="p-4">
                {/* Requirements Grid */}
                <div className="grid grid-cols-1 gap-3">
                  {uploadGuide.requirements.map((requirement, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-3 bg-blue-50/50 rounded-lg p-3"
                    >
                      <span className="text-lg flex-shrink-0 mt-0.5">
                        {requirement.icon}
                      </span>
                      <div className="min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 mb-1">
                          {requirement.title}
                        </h4>
                        <p className="text-sm text-gray-600 leading-tight">
                          {requirement.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Example Images */}
                {MEMORY_VIDEO_CONFIG.ui.uploadGuideImages.length > 0 && (
                  <div className="mt-4 space-y-3">
                    <p className="text-sm font-medium text-blue-700 text-center">
                      Good Examples:
                    </p>
                    <div className="flex justify-center space-x-3">
                      {MEMORY_VIDEO_CONFIG.ui.uploadGuideImages
                        .slice(0, 2)
                        .map((image) => (
                          <div key={image.id} className="relative">
                            <img
                              src={image.url}
                              alt={image.alt}
                              className="w-16 h-16 object-cover rounded-lg border-2 border-blue-200"
                              onError={(e) => {
                                // 如果图片加载失败，显示占位符
                                const target = e.target as HTMLImageElement
                                target.src = `https://via.placeholder.com/64x64/3b82f6/ffffff?text=👤`
                              }}
                            />
                            <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                              <span className="text-white text-xs">✓</span>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
