'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { UploadImage } from '../face-swap/components/UploadImage'
import { GenerateButton } from '../face-swap/components/GenerateButton'
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@ui/components/tabs'
import { useAuth } from '../../../../../modules/ui/hooks/use-auth'
import { useUpload } from '../face-swap/hooks/useUpload'
import {
  HistoryIcon,
  ImageIcon,
  RotateCcw,
  Info,
  PlayCircle,
} from 'lucide-react'
import { MEMORY_VIDEO_CONFIG, SampleContent } from './config'
import { SampleVideos } from './components/SampleVideos'
import { MemoryVideoTipsPopover } from './components/MemoryVideoTipsPopover'
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '@ui/components/dialog'

// 导入新的统一组件和hooks
import {
  useUnifiedGeneration,
  useUnifiedHistory,
  UnifiedHistoryTab,
  TaskType,
  GenerationProgress,
  useGenerationProgress,
} from '../components'
import { downloadFile } from '../components/utils'

const TASK_TYPE: TaskType = 'memory_video'

export default function MemoryVideoPage() {
  const searchParams = useSearchParams()
  const {
    isLoggedIn,
    isLoginModalOpen,
    login,
    openLoginModal,
    closeLoginModal,
  } = useAuth()
  const [showExampleVideo, setShowExampleVideo] = useState(false)
  const [selectedSample, setSelectedSample] = useState<SampleContent | null>(
    null
  )
  const [activeTab, setActiveTab] = useState('creations')

  // 进度管理hook
  const {
    progress: generationProgress,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress: setGenerationProgress,
  } = useGenerationProgress()

  // 图片上传hook
  const imageUpload = useUpload()

  // 使用统一的生成hook
  const {
    isGenerating,
    taskId,
    error: generateError,
    taskDetail,
    isPolling,
    pollingError,
    generate,
    reset: resetGeneration,
    stopPolling,
  } = useUnifiedGeneration(TASK_TYPE)

  // 使用统一的历史记录hook
  const {
    items: historyItems,
    isLoading: isLoadingHistory,
    error: historyError,
    refreshHistory,
  } = useUnifiedHistory(TASK_TYPE)

  // 生成记忆视频
  const handleGenerate = useCallback(async () => {
    if (!imageUpload.imageUrl) {
      return
    }

    try {
      // 切换到 creations tab
      setActiveTab('creations')

      // 开始进度计时器
      startProgress()

      // 使用统一的生成接口
      await generate({
        image_url: imageUpload.imageUrl,
      })
    } catch (error) {
      console.error('Failed to generate memory video:', error)
      // 生成失败时重置进度
      resetProgress()
    }
  }, [
    imageUpload.imageUrl,
    generate,
    startProgress,
    resetProgress,
    setActiveTab,
  ])

  // 重置所有上传的图片
  const handleReset = useCallback(() => {
    imageUpload.reset()
    resetGeneration()
    stopPolling()
    setSelectedSample(null)

    // 重置进度状态
    resetProgress()
  }, [imageUpload, resetGeneration, stopPolling, resetProgress])

  // 样例图片选择
  const handleSampleSelect = useCallback(
    (sample: SampleContent) => {
      imageUpload.setImageUrl(sample.imageUrl)
      setSelectedSample(sample)
    },
    [imageUpload]
  )

  // 从历史记录重新生成
  const handleRegenerateFromHistory = useCallback(
    async (input: Record<string, any>) => {
      const imageUrl = input.image_url

      if (!imageUrl) return

      imageUpload.setImageUrl(imageUrl)
      setSelectedSample(null)

      try {
        // 开始进度计时器
        startProgress()

        await generate({
          image_url: imageUrl,
        })
      } catch (error) {
        console.error('Failed to regenerate memory video:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [imageUpload, generate, startProgress, resetProgress]
  )

  // 处理URL参数中的sample参数
  useEffect(() => {
    const sampleId = searchParams.get('sample')
    if (sampleId) {
      const sample = MEMORY_VIDEO_CONFIG.ui.sampleContent.find(
        (s) => s.id === parseInt(sampleId)
      )
      if (sample) {
        setSelectedSample(sample)
        imageUpload.setImageUrl(sample.imageUrl)
      }
    }
  }, [searchParams, imageUpload])

  // 初始化历史记录
  React.useEffect(() => {
    if (isLoggedIn && historyItems.length === 0) {
      refreshHistory()
    }
  }, [isLoggedIn, historyItems.length, refreshHistory])

  // 监听任务完成，自动刷新历史记录
  React.useEffect(() => {
    if (
      taskDetail &&
      (taskDetail.status.status === 'success' ||
        taskDetail.status.status === 'failed')
    ) {
      // 任务完成后延迟刷新历史记录，确保webhook已更新数据库
      setTimeout(() => {
        refreshHistory()
      }, 1000)
    }
  }, [taskDetail?.status.status, refreshHistory])

  // 监听任务完成状态，更新进度到100%
  React.useEffect(() => {
    if (taskDetail?.status.status === 'success') {
      // 任务成功完成，将进度设置为100%并停止计时器
      setGenerationProgress(100)
      stopProgress()
    } else if (taskDetail?.status.status === 'failed') {
      // 任务失败，重置进度
      resetProgress()
    }
  }, [
    taskDetail?.status.status,
    setGenerationProgress,
    stopProgress,
    resetProgress,
  ])

  // 适配现有的VideoResultDisplay组件
  const adaptedTaskDetail = taskDetail
    ? {
        taskId: taskDetail.taskId,
        status:
          taskDetail.status.status === 'success'
            ? 'completed'
            : taskDetail.status.status === 'failed'
            ? 'failed'
            : taskDetail.status.status === 'processing'
            ? 'processing'
            : 'pending',
        inputParams: taskDetail.input,
        resultData: taskDetail.output,
        error: taskDetail.error?.message,
      }
    : null

  return (
    <main className="h-screen pt-[68px] bg-gray-50">
      {/* Main Layout Container */}
      <div className="h-full flex bg-white border-t border-gray-200">
        {/* Left Sidebar - Operations Area */}
        <div className="w-[480px] border-r border-gray-200 bg-white flex flex-col">
          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              <div className="w-full">
                {/* Upload Image Section */}
                <div className="space-y-4">
                  <div className="w-full flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Upload Memorial Photo
                    </h3>
                    <div className="flex items-center gap-2">
                      {/* Reset Button */}
                      <button
                        onClick={handleReset}
                        className="flex items-center justify-center gap-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
                      >
                        <RotateCcw className="h-4 w-4" />
                        <span>reset all</span>
                      </button>
                      <MemoryVideoTipsPopover>
                        <button className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors">
                          <Info className="w-4 h-4" />
                        </button>
                      </MemoryVideoTipsPopover>
                    </div>
                  </div>

                  <div className="h-28">
                    <UploadImage
                      title="Upload Memorial Photo"
                      imageUrl={imageUpload.imageUrl}
                      isUploading={imageUpload.isUploading}
                      progress={imageUpload.progress}
                      onUpload={imageUpload.uploadImage}
                      featureType="memory-video"
                    />
                  </div>

                  {/* Sample Videos Section */}
                  <div className="space-y-4">
                    <SampleVideos
                      samples={MEMORY_VIDEO_CONFIG.ui.sampleContent}
                      onSelectSample={handleSampleSelect}
                      selectedSample={selectedSample}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Fixed Generate Button */}
          <GenerateButton
            swapImageUrl={imageUpload.imageUrl}
            targetImageUrl=""
            isGenerating={isGenerating}
            onGenerate={handleGenerate}
            generateError={generateError}
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-4 rounded-lg hover:shadow-md transition-all duration-200"
          />
        </div>

        {/* Right Main Content Area */}
        <div className="flex-1 flex flex-col bg-gray-50">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            {/* Tab 切换头部 */}
            <div className="bg-white flex justify-center">
              <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
                <TabsTrigger
                  value="creations"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <ImageIcon className="h-4 w-4" />
                  <span>Creations</span>
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <HistoryIcon className="h-4 w-4" />
                  <span>History</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Tab 内容 */}
            <TabsContent
              value="creations"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="h-full overflow-y-auto p-4 relative">
                <div className="min-h-full flex items-start justify-center">
                  <div className="w-full">
                    {/* Header Section */}
                    <div className="text-center mb-12">
                      <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        Animate Old Photos for a Lasting Memorial
                      </h1>
                      <p className="text-lg text-gray-600">
                        Our free memorial video maker helps you create a
                        touching tribute. Discover how to make a slide show for
                        a funeral that truly honors their memory and legacy.
                      </p>
                    </div>

                    {/* Result Display Area */}
                    <div className="flex justify-center">
                      <div className="w-full max-w-2xl">
                        {/* Show unified result display */}
                        {isGenerating || isPolling ? (
                          <GenerationProgress
                            progress={generationProgress}
                            isGenerating={isGenerating || isPolling}
                            title="Generating Memory Video"
                            description="AI is bringing your photo to life..."
                            size="md"
                          />
                        ) : generateError || pollingError ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                                <RotateCcw className="w-8 h-8 text-red-500" />
                              </div>
                              <div className="space-y-2">
                                <h3 className="text-lg font-semibold text-gray-900">
                                  Generation Failed
                                </h3>
                                <p className="text-sm text-red-600">
                                  {generateError || pollingError}
                                </p>
                              </div>
                              <button
                                onClick={() => {
                                  resetGeneration()
                                  stopPolling()
                                  resetProgress()
                                }}
                                className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                              >
                                Try Again
                              </button>
                            </div>
                          </div>
                        ) : adaptedTaskDetail?.status === 'completed' &&
                          adaptedTaskDetail?.resultData?.video_url ? (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="space-y-6">
                              <div className="relative max-w-md mx-auto">
                                <video
                                  src={adaptedTaskDetail.resultData.video_url}
                                  className="w-full h-[500px] rounded-lg shadow-lg object-contain"
                                  controls
                                  autoPlay
                                  loop
                                  muted
                                />
                              </div>
                              <div className="flex justify-center space-x-4">
                                <button
                                  onClick={() => {
                                    // Download functionality
                                    const a = document.createElement('a')
                                    a.href =
                                      adaptedTaskDetail.resultData?.video_url ||
                                      ''
                                    a.download = `memory_video_${Date.now()}.mp4`
                                    a.click()
                                  }}
                                  className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg"
                                >
                                  <svg
                                    className="w-5 h-5"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                    />
                                  </svg>
                                  <span>Download Video</span>
                                </button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                            <div className="text-center space-y-4">
                              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                                <span className="text-2xl">🎬</span>
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900">
                                Ready to Create Memories
                              </h3>
                              <p className="text-sm text-gray-600">
                                Upload a photo and click generate to create a
                                beautiful animated memory video
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="history"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="w-full h-full">
                {/* 使用新的统一历史记录组件 */}
                <UnifiedHistoryTab
                  taskType={TASK_TYPE}
                  onRegenerate={handleRegenerateFromHistory}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </main>
  )
}
