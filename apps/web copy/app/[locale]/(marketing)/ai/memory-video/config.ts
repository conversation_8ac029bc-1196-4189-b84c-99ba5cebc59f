// Memory Video Configuration
//
export const MEMORY_VIDEO_CONFIG = {
  // Upload Configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  },

  // UI Configuration
  ui: {
    // Pagination
    historyPageSize: 10,

    // Sample images and videos for Memory Video
    sampleContent: [
      {
        id: 1,
        imageUrl: '/samples/memorial-video-maker/A-1-before.jpeg',
        videoUrl: '/samples/memorial-video-maker/A-1-after.mp4',
        alt: 'Family memorial animation',
        category: 'family' as const,
        badge: 'Popular',
      },
      {
        id: 2,
        imageUrl: '/samples/memorial-video-maker/A-2-before.jpeg',
        videoUrl: '/samples/memorial-video-maker/A-2-after.mp4',
        alt: 'Individual memorial animation',
        category: 'individual' as const,
        badge: 'Trending',
      },
      {
        id: 3,
        imageUrl: '/samples/memorial-video-maker/A-3-before.jpeg',
        videoUrl: '/samples/memorial-video-maker/A-3-after.mp4',
        alt: 'Couple memorial animation',
        category: 'couple' as const,
      },
      {
        id: 4,
        imageUrl: '/samples/memorial-video-maker/A-4-before.jpeg',
        videoUrl: '/samples/memorial-video-maker/A-4-after.mp4',
        alt: 'Group memorial animation',
        category: 'group' as const,
      },
    ] as SampleContent[],

    // Upload guidance examples
    uploadGuideImages: [
      {
        id: 1,
        url: '/samples/memorial-video-maker/memorial-video-maker-before.png',
        alt: 'Good example: Clear memorial photo',
        type: 'good',
        description: 'Clear facial features and proper lighting for memorial',
      },
      {
        id: 2,
        url: '/samples/memorial-video-maker/A-1-before.jpeg',
        alt: 'Good example: High quality memorial portrait',
        type: 'good',
        description: 'High-resolution memorial portrait with good composition',
      },
    ],

    // Animation and transition settings
    transitions: {
      duration: 200,
      easing: 'ease-in-out',
    },

    // Video player settings
    videoPlayer: {
      autoplay: true,
      loop: true,
      muted: true,
      controls: true,
      preload: 'metadata',
    },
  },

  // Feature Flags
  features: {
    enableHistory: true,
    enableSampleVideos: true,
    enableDownload: true,
    enableWebhook: true,
    enableUploadGuide: true,

    // Development features
    enableDevTools: process.env.NODE_ENV === 'development',
    enableDebugLogs: process.env.NODE_ENV === 'development',
  },

  // Error Messages
  messages: {
    errors: {
      uploadFailed: 'Photo upload failed, please try again.',
      generateFailed: 'Memorial video generation failed, please try again.',
      loginRequired: 'Please login to use this feature.',
      invalidFileType: 'Please select a valid image file (JPG, PNG, WebP).',
      fileTooLarge: 'File size cannot exceed 10MB.',
      networkError: 'Network error, please check your connection.',
      noPersonDetected: 'Please upload a photo with a person visible.',
      videoLoadFailed: 'Video loading failed, please try again.',
    },
    success: {
      uploadComplete: 'Photo uploaded successfully!',
      generateComplete: 'Memorial video generated successfully!',
      downloadStarted: 'Download started.',
      videoReady: 'Your memorial video is ready!',
    },
    info: {
      uploadInProgress: 'Uploading photo...',
      generateInProgress: 'Generating memorial video...',
      processingTask: 'Processing your request, this may take a few minutes...',
      queueWaiting:
        'Your request is in queue, estimated wait time: {time} minutes',
      aiProcessing: 'AI is creating your memorial video...',
    },
  },

  // Upload guidance content
  uploadGuide: {
    title: 'Upload Guidelines',
    subtitle:
      'For best memorial video results, please follow these guidelines:',
    requirements: [
      {
        icon: '👤',
        title: 'Clear Person',
        description: 'Photo should contain a clear person or people',
      },
      {
        icon: '📸',
        title: 'High Quality',
        description: 'Use high-resolution photos for better memorial results',
      },
      {
        icon: '✨',
        title: 'Good Lighting',
        description: 'Well-lit photos work best for memorial animation',
      },
      {
        icon: '🎯',
        title: 'Focused Face',
        description: 'Face should be clearly visible and in focus for memorial',
      },
    ],
  },
} as const

// Type helpers
export type SampleContent = {
  id: number
  imageUrl: string
  videoUrl: string
  alt: string
  category: 'family' | 'couple' | 'individual' | 'group'
  badge?: string
}

export type UploadGuideImage = {
  readonly id: number
  readonly url: string
  readonly alt: string
  readonly type: 'good' | 'bad'
  readonly description: string
}

export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed'

export const isValidImageFile = (file: File): boolean => {
  const allowedTypes = MEMORY_VIDEO_CONFIG.upload
    .allowedTypes as readonly string[]
  return (
    allowedTypes.includes(file.type) &&
    file.size <= MEMORY_VIDEO_CONFIG.upload.maxFileSize
  )
}

export const getErrorMessage = (
  errorKey: keyof typeof MEMORY_VIDEO_CONFIG.messages.errors
): string => {
  return MEMORY_VIDEO_CONFIG.messages.errors[errorKey]
}

export const getSuccessMessage = (
  successKey: keyof typeof MEMORY_VIDEO_CONFIG.messages.success
): string => {
  return MEMORY_VIDEO_CONFIG.messages.success[successKey]
}

export const getInfoMessage = (
  infoKey: keyof typeof MEMORY_VIDEO_CONFIG.messages.info,
  params?: Record<string, string>
): string => {
  let message: string = MEMORY_VIDEO_CONFIG.messages.info[infoKey]

  // 替换参数占位符
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      message = message.replace(`{${key}}`, value)
    })
  }

  return message
}
