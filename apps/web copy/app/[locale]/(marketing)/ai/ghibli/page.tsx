// apps/web/app/[locale]/(marketing)/ai/image-to-image/page.tsx
import { getTranslations } from 'next-intl/server'
import ClientContainer from './components/ClientContainer'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('seo-generate-image-t'),
    description: t('seo-generate-image-d'),
    keywords: t('seo-generate-image-k'),
  }
}
export default function CreatePage() {
  return (
    <main className="min-h-screen px-3 sm:px-5 md:px-8 pt-32 sm:pt-36 md:pt-40 pb-16 sm:pb-20 md:pb-24 bg-gradient-to-br from-indigo-50 to-blue-100">
      <ClientContainer />
    </main>
  )
}
