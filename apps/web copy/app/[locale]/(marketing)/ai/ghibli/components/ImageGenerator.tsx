'use client'

import { useState, useEffect, useRef } from 'react'
import { useTranslations } from 'next-intl'
import { ImageStyleConverter } from './ImageStyleConverter'
import {
  Upload,
  ChevronLeft,
  ChevronRight,
  Download,
  History,
} from 'lucide-react'
import { useAtom } from 'jotai'
import {
  isGenerating<PERSON>tom,
  generationProgressAtom,
  generatedImageUrlAtom,
  generatedImageUrlsAtom,
} from '@marketing/home/<USER>'
import ReactCompareImage from 'react-compare-image'
import { Link } from '@i18n/routing'
import ImageGenerationLoader from './ImageGenerationLoader'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'
import { WaitingTips } from './modules'

// 示例图片数组
const EXAMPLE_IMAGES = [
  {
    originalImage: '/gulika/ghibli-original.jpeg',
    generatedImage: '/gulika/ghibli-generated.jpeg',
  },
  {
    originalImage: '/gulika/lego-original.jpeg',
    generatedImage: '/gulika/lego-generated.jpeg',
  },
  {
    originalImage: '/gulika/simpson-original.jpeg',
    generatedImage: '/gulika/simpson-generated.jpeg',
  },
  {
    originalImage: '/gulika/irasyo-original.jpeg',
    generatedImage: '/gulika/irasyo-generated.jpeg',
  },
  {
    originalImage: '/gulika/labi-original.jpeg',
    generatedImage: '/gulika/labi-generated.jpeg',
  },
  {
    originalImage: '/gulika/youhua-original.jpeg',
    generatedImage: '/gulika/youhua-generated.jpeg',
  },
]

// 添加动画变体
const slideVariants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
  }),
  center: {
    zIndex: 1,
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    zIndex: 0,
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
  }),
}

export default function ImageGenerator() {
  const t = useTranslations()

  const [[page, direction], setPage] = useState([0, 0])

  // 使用全局状态
  const [isGenerating] = useAtom(isGeneratingAtom)
  const [generationProgress] = useAtom(generationProgressAtom)
  const [generatedImageUrl] = useAtom(generatedImageUrlAtom)
  const [generatedImageUrls] = useAtom(generatedImageUrlsAtom)

  const containerRef = useRef<HTMLDivElement>(null)

  // 更新轮播控制
  const paginate = (newDirection: number) => {
    const newPage = page + newDirection
    if (newPage < 0) {
      setPage([EXAMPLE_IMAGES.length - 1, newDirection])
    } else if (newPage >= EXAMPLE_IMAGES.length) {
      setPage([0, newDirection])
    } else {
      setPage([newPage, newDirection])
    }
  }

  // 自动轮播
  useEffect(() => {
    if (!isGenerating) {
      const timer = setInterval(() => {
        paginate(1)
      }, 5000)
      return () => clearInterval(timer)
    }
  }, [isGenerating, page])

  // 滚动到优秀案例区域
  const scrollToExamples = () => {
    const examplesSection = document.getElementById('showcase-examples')
    if (examplesSection) {
      examplesSection.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // 检查是否需要自动触发生成请求
  useEffect(() => {
    const checkAutoGenerate = () => {
      const shouldAutoGenerate = sessionStorage.getItem(
        'auto_generate_on_redirect'
      )

      console.log(
        'Checking auto generate flag in ImageGenerator:',
        shouldAutoGenerate
      )

      if (shouldAutoGenerate === 'true') {
        console.log(
          'Auto generate flag is true, attempting to trigger generation'
        )

        // 清除标志，避免重复触发
        sessionStorage.removeItem('auto_generate_on_redirect')
        console.log('Auto generate flag cleared from sessionStorage')

        // 获取ImageStyleConverter组件的引用并触发提交
        const submitButton = document.querySelector(
          '.image-style-converter form button[type="submit"]'
        ) as HTMLButtonElement

        console.log('Submit button found:', !!submitButton)

        if (submitButton) {
          // 直接点击提交按钮
          console.log('Clicking submit button directly')

          // 在点击前，确保表单数据已正确加载
          // 获取 ImageStyleConverter 组件的引用
          const imageStyleConverterForm = document.querySelector(
            '.image-style-converter form'
          ) as HTMLFormElement

          if (imageStyleConverterForm) {
            // 尝试获取表单中的输入字段
            const promptInput = imageStyleConverterForm.querySelector(
              'textarea[name="prompt"], input[name="prompt"]'
            ) as HTMLInputElement | HTMLTextAreaElement

            if (promptInput) {
              console.log('Current prompt value:', promptInput.value)

              // 如果表单值为空，尝试从 sessionStorage 获取
              if (!promptInput.value.trim()) {
                console.log(
                  'Prompt is empty, trying to load from sessionStorage'
                )

                // 使用 window.sessionStorage 直接访问
                // 注意：这里的键需要与 ImageStyleConverter 组件中的 STORAGE_KEY_FORM 常量一致
                const savedFormDataStr =
                  window.sessionStorage.getItem('form_data_v2')
                if (savedFormDataStr) {
                  try {
                    const savedFormData = JSON.parse(savedFormDataStr)
                    console.log(
                      'Loaded form data from sessionStorage:',
                      savedFormData
                    )

                    // 如果有 prompt，尝试设置到输入字段
                    if (savedFormData.prompt) {
                      promptInput.value = savedFormData.prompt
                      console.log('Set prompt value to:', savedFormData.prompt)

                      // 触发 input 事件，确保 React 表单状态更新
                      const inputEvent = new Event('input', { bubbles: true })
                      promptInput.dispatchEvent(inputEvent)

                      // 给表单一点时间更新
                      setTimeout(() => {
                        console.log(
                          'Clicking submit button after setting prompt'
                        )
                        submitButton.click()
                      }, 300)
                      return
                    }
                  } catch (error) {
                    console.error('Error parsing saved form data:', error)
                  }
                }
              }
            }
          }

          // 如果上面的尝试都失败了，直接点击按钮
          submitButton.click()
          console.log('Submit button clicked')
        } else {
          console.log('Submit button not found, trying alternative method')

          // 备用方法：尝试获取表单并提交
          const imageStyleConverterForm = document.querySelector(
            '.image-style-converter form'
          ) as HTMLFormElement

          if (imageStyleConverterForm) {
            try {
              // 尝试从 sessionStorage 获取表单数据
              // 注意：这里的键需要与 ImageStyleConverter 组件中的 STORAGE_KEY_FORM 常量一致
              const savedFormDataStr =
                window.sessionStorage.getItem('form_data_v2')
              let savedFormData = null

              if (savedFormDataStr) {
                try {
                  savedFormData = JSON.parse(savedFormDataStr)
                  console.log(
                    'Loaded form data from sessionStorage:',
                    savedFormData
                  )
                } catch (error) {
                  console.error('Error parsing saved form data:', error)
                }
              }

              // 尝试查找表单中的提交按钮
              const submitButtons =
                imageStyleConverterForm.querySelectorAll('button')
              let found = false

              // 遍历所有按钮，查找提交按钮
              for (let i = 0; i < submitButtons.length; i++) {
                const btn = submitButtons[i]
                if (
                  btn.type === 'submit' ||
                  btn.textContent?.includes('Generate')
                ) {
                  // 在点击前，尝试设置表单值
                  if (savedFormData && savedFormData.prompt) {
                    const promptInput = imageStyleConverterForm.querySelector(
                      'textarea[name="prompt"], input[name="prompt"]'
                    ) as HTMLInputElement | HTMLTextAreaElement

                    if (promptInput) {
                      promptInput.value = savedFormData.prompt
                      // 触发 input 事件
                      const inputEvent = new Event('input', { bubbles: true })
                      promptInput.dispatchEvent(inputEvent)

                      // 给表单一点时间更新
                      setTimeout(() => {
                        btn.click()
                        console.log(
                          'Found and clicked submit button inside form after setting prompt'
                        )
                      }, 300)
                      found = true
                      break
                    }
                  }

                  // 如果没有设置表单值，直接点击
                  btn.click()
                  console.log('Found and clicked submit button inside form')
                  found = true
                  break
                }
              }

              if (!found) {
                console.log(
                  'No submit button found in form, trying to dispatch submit event'
                )
                // 尝试触发提交事件
                const submitEvent = new Event('submit', {
                  bubbles: true,
                  cancelable: true,
                })
                imageStyleConverterForm.dispatchEvent(submitEvent)
              }
            } catch (error) {
              console.error('Error trying to submit form:', error)
            }
          } else {
            console.log('Form not found, auto-generation failed')
          }
        }
      }
    }

    // 延迟一点时间执行，确保组件已完全加载
    const timer = setTimeout(checkAutoGenerate, 800)
    return () => clearTimeout(timer)
  }, [])

  // 触发上传图片事件
  const handleUploadClick = () => {
    const uploadInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement
    if (uploadInput) {
      uploadInput.click()
    }
  }

  // Download generated image using fetch API to avoid triggering top loader
  const handleDownload = async (imageUrl?: string, index?: number) => {
    const urlToDownload = imageUrl || generatedImageUrl
    if (!urlToDownload) return

    // Generate filename based on whether index is provided
    const timestamp = Date.now()
    let filename
    if (index !== undefined) {
      // If index is provided, use it in the filename
      filename = `generated-image-${index + 1}-${timestamp}.png`
    } else {
      // Otherwise use default naming
      filename = `generated-image-${timestamp}.png`
    }

    try {
      // Use fetch API to download the image directly
      const response = await fetch(
        `/api/download?url=${encodeURIComponent(
          urlToDownload
        )}&filename=${encodeURIComponent(filename)}`,
        {
          method: 'GET',
        }
      )

      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`)
      }

      // Get the blob from the response
      const blob = await response.blob()

      // Create a download link for the blob
      const blobUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename

      // Trigger download
      document.body.appendChild(link)
      link.click()

      // Clean up
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
    } catch (error) {
      console.error('Error downloading image:', error)
      toast.error('Failed to download image. Please try again.')
    }
  }

  // Download all images with delay to avoid browser blocking
  const handleDownloadAll = async () => {
    if (!generatedImageUrls.length) return

    // Display download start notification
    const toastId = toast.loading(
      `Downloading images (0/${generatedImageUrls.length})...`
    )

    try {
      // Download each image with a delay between downloads
      for (let i = 0; i < generatedImageUrls.length; i++) {
        const url = generatedImageUrls[i]
        const timestamp = Date.now()
        const filename = `generated-image-${i + 1}-${timestamp}.png`

        // Update download progress notification
        toast.loading(
          `Downloading images (${i + 1}/${generatedImageUrls.length})...`,
          {
            id: toastId,
          }
        )

        try {
          // Use fetch API to download the image directly
          const response = await fetch(
            `/api/download?url=${encodeURIComponent(
              url
            )}&filename=${encodeURIComponent(filename)}`,
            {
              method: 'GET',
            }
          )

          if (!response.ok) {
            throw new Error(`Download failed: ${response.statusText}`)
          }

          // Get the blob from the response
          const blob = await response.blob()

          // Create a download link for the blob
          const blobUrl = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = blobUrl
          link.download = filename

          // Trigger download
          document.body.appendChild(link)
          link.click()

          // Clean up
          document.body.removeChild(link)
          window.URL.revokeObjectURL(blobUrl)
        } catch (error) {
          console.error(`Error downloading image ${i + 1}:`, error)
          throw error // Re-throw to be caught by the outer try-catch
        }

        // Add delay to avoid browser blocking
        if (i < generatedImageUrls.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 800))
        }
      }

      // Download complete notification
      toast.success(
        `Successfully downloaded ${generatedImageUrls.length} images`,
        {
          id: toastId,
        }
      )
    } catch (error) {
      // Download error notification
      toast.error('Error occurred during download. Please try again.', {
        id: toastId,
      })
      console.error('Error downloading images:', error)
    }
  }

  return (
    <>
      <div
        ref={containerRef}
        className="w-full max-w-7xl mx-auto p-4 sm:p-6 md:p-8 bg-white/90 backdrop-blur-sm rounded-2xl shadow-[0_8px_30px_rgb(0,0,0,0.08)] border border-gray-100/50"
      >
        {/* 历史记录按钮 */}
        <div className="flex flex-row flex-wrap justify-end items-center gap-3 mb-4 sm:mb-6">
          <Link
            href="/image-history"
            className="inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-blue-600 border border-blue-500 rounded-md hover:bg-blue-50 transition-colors focus:outline-none focus:ring-1 focus:ring-blue-500 shrink-0"
            title={t('ImageGenerator.viewHistory')}
          >
            <History className="mr-0 sm:mr-2 h-4 w-4" />
            <span className="hidden sm:inline">
              {t('ImageGenerator.viewHistory')}
            </span>
          </Link>
        </div>

        <div className="grid grid-cols-1 gap-4 md:gap-6 lg:grid-cols-2 lg:gap-8">
          {/* 左侧：图片上传和转换组件 */}
          <div className="w-full">
            <ImageStyleConverter
              redirectOnGenerate={false}
              maxUploadImages={5}
            />
          </div>

          {/* 右侧：生成结果展示 */}
          <div className="w-full">
            <div className="h-full flex flex-col">
              <div className="flex-1 flex flex-col items-center justify-center">
                {isGenerating ? (
                  <div className="w-full flex flex-col items-center justify-center">
                    <ImageGenerationLoader progress={generationProgress} />

                    {/* 生成时间超过10秒的提示 - 使用抽离的组件 */}
                    <WaitingTips
                      isGenerating={isGenerating}
                      tipsTitle="Image generation in progress, this may take some time"
                      tipsDescription="While waiting, you can check out some great examples and tips to improve your prompt writing skills"
                      buttonText="View Examples"
                      onExamplesClick={scrollToExamples}
                    />
                  </div>
                ) : generatedImageUrls.length > 0 ? (
                  <div className="w-full h-full flex flex-col items-center justify-center py-4">
                    {/* 网格布局展示生成的图片 */}

                    <div
                      className={`w-full grid ${
                        generatedImageUrls.length === 1
                          ? 'grid-cols-1 max-w-md mx-auto'
                          : generatedImageUrls.length === 2
                          ? 'grid-cols-2 gap-2 sm:gap-3'
                          : generatedImageUrls.length === 4
                          ? 'grid-cols-2 gap-2 sm:gap-3'
                          : 'grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4'
                      }`}
                    >
                      {generatedImageUrls.map((imageUrl, index) => (
                        <div
                          key={index}
                          className="relative group aspect-square flex items-center justify-center bg-gray-50 rounded-xl overflow-hidden"
                        >
                          <img
                            src={imageUrl}
                            alt={`${t('ImageGenerator.generatedImage')} ${
                              index + 1
                            }`}
                            className="max-w-full max-h-full object-contain rounded-xl"
                          />

                          {/* 下载按钮 - 放在图片正中央，悬停时显示 */}
                          <button
                            onClick={() => handleDownload(imageUrl, index)}
                            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 p-2 sm:p-3 rounded-full bg-black/40 text-white hover:bg-black/60 transition-colors sm:opacity-0 sm:group-hover:opacity-100 opacity-100 touch-manipulation"
                            title={t('ImageGenerator.download')}
                          >
                            <Download className="h-5 w-5 sm:h-6 sm:w-6" />
                          </button>
                        </div>
                      ))}
                    </div>

                    {/* 下载所有图片按钮 - 放在图片展示的正下方 */}
                    {generatedImageUrls.length > 1 && (
                      <div className="w-full flex justify-center mt-4">
                        <button
                          onClick={handleDownloadAll}
                          className="inline-flex items-center px-3 sm:px-4 py-2 rounded-md text-xs sm:text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors border border-gray-300 touch-manipulation"
                        >
                          <Download className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                          Download All Images
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="w-full flex flex-col items-center">
                    {/* 轮播图 */}
                    <div className="relative w-full aspect-[16/9] mb-6 overflow-hidden rounded-xl">
                      <AnimatePresence initial={false} custom={direction}>
                        <motion.div
                          key={page}
                          custom={direction}
                          variants={slideVariants}
                          initial="enter"
                          animate="center"
                          exit="exit"
                          transition={{
                            x: { type: 'spring', stiffness: 300, damping: 30 },
                            opacity: { duration: 0.2 },
                          }}
                          className="absolute w-full h-full rounded-xl overflow-hidden"
                        >
                          <ReactCompareImage
                            leftImage={EXAMPLE_IMAGES[page].originalImage}
                            rightImage={EXAMPLE_IMAGES[page].generatedImage}
                            sliderLineWidth={2}
                            sliderLineColor="#3B82F6"
                            leftImageCss={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              borderRadius: '0.75rem',
                            }}
                            rightImageCss={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                              borderRadius: '0.75rem',
                            }}
                            handle={
                              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center shadow-md rotate-90">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                  className="w-5 h-5 text-white"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                                  />
                                </svg>
                              </div>
                            }
                            hover={false}
                            skeleton={
                              <div className="w-full h-full bg-gray-100 animate-pulse" />
                            }
                          />
                        </motion.div>
                      </AnimatePresence>

                      {/* 轮播控制按钮 */}
                      <button
                        onClick={() => paginate(-1)}
                        className="absolute left-1 sm:left-2 top-1/2 -translate-y-1/2 p-1 sm:p-2 rounded-full bg-black/30 text-white hover:bg-black/50 transition-colors z-10 touch-manipulation"
                      >
                        <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6" />
                      </button>
                      <button
                        onClick={() => paginate(1)}
                        className="absolute right-1 sm:right-2 top-1/2 -translate-y-1/2 p-1 sm:p-2 rounded-full bg-black/30 text-white hover:bg-black/50 transition-colors z-10 touch-manipulation"
                      >
                        <ChevronRight className="h-4 w-4 sm:h-6 sm:w-6" />
                      </button>

                      {/* 轮播指示器 */}
                      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 z-10">
                        {EXAMPLE_IMAGES.map((_, index) => (
                          <button
                            key={index}
                            onClick={() =>
                              setPage([index, index > page ? 1 : -1])
                            }
                            className={`w-2 h-2 rounded-full transition-colors ${
                              page === index ? 'bg-blue-500' : 'bg-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>

                    <div className="text-center mb-6">
                      <h4 className="text-sm font-medium text-gray-700 mb-1">
                        {t('ImageGenerator.imagineUnlimited')}
                      </h4>
                      <p className="text-sm text-gray-600 leading-relaxed line-clamp-2">
                        {t('ImageGenerator.uploadPhoto')}
                      </p>
                    </div>

                    {/* 上传按钮 */}
                    <button
                      onClick={handleUploadClick}
                      className="w-full max-w-md px-4 py-2 border-2 border-blue-500 text-blue-500 rounded-xl hover:bg-blue-50 transition-all duration-200 flex items-center justify-center gap-2 sm:gap-3 group touch-manipulation"
                    >
                      <Upload className="h-4 w-4 sm:h-5 sm:w-5 group-hover:scale-110 transition-transform" />
                      <span className="text-base sm:text-lg font-medium">
                        {t('ImageGenerator.uploadButtonText')}
                      </span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
