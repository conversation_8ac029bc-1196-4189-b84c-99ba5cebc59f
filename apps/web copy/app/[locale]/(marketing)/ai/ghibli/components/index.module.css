.resizableContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

.resizableContainer [data-testid='container'] {
  height: 100% !important;
  border-radius: 0.5rem;
  overflow: hidden;
}

@keyframes moving-particle {
  0% {
    transform: translateX(0);
    opacity: 0;
  }
  20% {
    opacity: 0.7;
  }
  100% {
    transform: translateX(300px);
    opacity: 0;
  }
}

.movingParticle {
  animation: moving-particle 2s linear infinite;
}

@keyframes rushing {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.rushing {
  animation: rushing 15s linear infinite;
}

@keyframes fade-in-out {
  0%,
  100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

.fadeInOut {
  animation: fade-in-out 2s ease-in-out infinite;
}
