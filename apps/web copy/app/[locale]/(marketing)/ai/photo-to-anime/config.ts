// Photo to Anime Configuration
//
export const PHOTO_TO_ANIME_CONFIG = {
  // 上传配置
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
  },

  // 动漫风格配置
  styles: {
    // 男性专用风格
    male: [
      {
        id: 'ghibli',
        name: 'Studio Ghibli Style',
        description: 'Whimsical hand-drawn anime style with vibrant colors',
        promptTemplate:
          'Convert to high-quality anime style, Studio Ghibli rendering, clean linework, anime eyes, detailed skin texture, professional anime art style, studio quality animation effect',
        example: '/ai-art-generator/A-2.jpeg',
        tags: ['anime', 'ghibli', 'vibrant'],
      },
      {
        id: 'cinematic',
        name: 'Cinematic Visual Effects',
        description: 'Hollywood movie quality with epic visual effects',
        promptTemplate:
          'Transform into Cinematic visual effects, Hollywood color grading, movie film texture, IMAX quality, professional movie color correction, epic visual effects, movie scene atmosphere',
        example: '/ai-art-generator/C-2.jpeg',
        tags: ['cinematic', 'hollywood', 'epic'],
      },
      {
        id: 'fashion-magazine',
        name: 'High-End Fashion Magazine',
        description: 'VOGUE magazine level professional retouching',
        promptTemplate:
          'Convert to high-end fashion magazine style, VOGUE magazine level retouching, perfect skin texture, premium quality, fashion editorial, professional lighting, premium gray tone, 4K ultra details',
        example: '/ai-art-generator/F-2.jpeg',
        tags: ['fashion', 'vogue', 'premium'],
      },
      {
        id: 'impressionist-art',
        name: 'Impressionist Art Effect',
        description: 'Monet-style impressionist painting effect',
        promptTemplate:
          'Transform into Impressionist art effect, Monet-style brushwork, impressionist light treatment, colorful effects, impressionist painting style, artistic light effects',
        example: '/ai-art-generator/E-2.jpeg',
        tags: ['impressionist', 'monet', 'artistic'],
      },
    ],

    // 女性专用风格
    female: [
      {
        id: 'ghibli',
        name: 'Studio Ghibli Style',
        description: 'Whimsical hand-drawn anime style with vibrant colors',
        promptTemplate:
          'Convert to high-quality anime style, Studio Ghibli rendering, clean linework, anime eyes, detailed skin texture, professional anime art style, studio quality animation effect',
        example: '/ai-art-generator/B-2.jpeg',
        tags: ['anime', 'ghibli', 'vibrant'],
      },
      {
        id: 'cyberpunk-2077',
        name: 'Cyberpunk 2077 Style',
        description: 'Futuristic cyberpunk aesthetic with neon effects',
        promptTemplate:
          'Transform into Cyberpunk 2077 style, neon light effects, futuristic tech feeling, metallic textures, high-tech hologram effects, dark street style, cyberpunk color scheme',
        example: '/ai-art-generator/D-2.jpeg',
        tags: ['cyberpunk', 'futuristic', 'neon'],
      },

      {
        id: 'chinese-ink-painting',
        name: 'Chinese Ink Painting',
        description: 'Traditional Chinese ink wash painting style',
        promptTemplate:
          'Transform into Chinese ink painting style, traditional ink wash effect, ink diffusion, Oriental artistic beauty, negative space composition',
        example: '/ai-art-generator/G-2.jpeg',
        tags: ['chinese', 'ink', 'traditional'],
      },
      {
        id: 'japanese-manga',
        name: 'Japanese Manga Style',
        description: 'Detailed manga illustration with sharp lines',
        promptTemplate:
          'as a detailed Japanese manga illustration, sharp lines, dynamic poses, expressive eyes, black and white ink style with screen tones',
        example: '/images/photo-to-anime/after.jpg',
        tags: ['manga', 'detailed', 'expressive'],
      },
    ],
  },

  // 生成参数配置
  generation: {
    // 输出比例选项
    aspectRatios: [
      { value: '1:1', label: '1:1', description: 'Square', icon: '⬜' },
      { value: '3:2', label: '3:2', description: 'Landscape', icon: '🖼️' },
      { value: '2:3', label: '2:3', description: 'Portrait', icon: '🖼️' },
    ],

    // 图片数量选项
    variants: [{ value: '1', label: '1 image' }],

    // 默认设置
    defaults: {
      aspectRatio: '1:1',
      variants: '1',
    },
  },

  // UI配置
  ui: {
    // 历史记录分页
    historyPageSize: 10,

    // 图片预览设置
    imagePreview: {
      maxWidth: 400,
      maxHeight: 400,
      quality: 0.8,
    },

    // 动画和过渡设置
    transitions: {
      duration: 200,
      easing: 'ease-in-out',
    },
  },

  // API配置
  api: {
    generateEndpoint: '/api/images/generate/photo-to-anime',
    statusEndpoint: '/api/images/record-info',
    uploadEndpoint: '/api/upload',
    pollInterval: 5000,
    maxPollTime: 600000, // 10分钟
  },
}

// 类型定义
export type AnimeStyle = {
  readonly id: string
  readonly name: string
  readonly description: string
  readonly promptTemplate: string
  readonly example: string
  readonly tags: readonly string[]
}

export type AspectRatio = {
  readonly value: string
  readonly label: string
  readonly description: string
  readonly icon: string
}

export type VariantOption = {
  readonly value: string
  readonly label: string
}

// 工具函数
export const getAllStyles = (): readonly AnimeStyle[] => {
  return [
    ...PHOTO_TO_ANIME_CONFIG.styles.male,
    ...PHOTO_TO_ANIME_CONFIG.styles.female,
  ]
}

export const getStyleById = (id: string): AnimeStyle | undefined => {
  return getAllStyles().find((style) => style.id === id)
}

export const getStylesByGender = (
  gender: 'male' | 'female'
): readonly AnimeStyle[] => {
  switch (gender) {
    case 'male':
      return PHOTO_TO_ANIME_CONFIG.styles.male
    case 'female':
      return PHOTO_TO_ANIME_CONFIG.styles.female
    default:
      return getAllStyles()
  }
}

export const isValidImageFile = (file: File): boolean => {
  return (
    PHOTO_TO_ANIME_CONFIG.upload.allowedTypes.includes(file.type) &&
    file.size <= PHOTO_TO_ANIME_CONFIG.upload.maxFileSize
  )
}

export const buildPrompt = (
  style: AnimeStyle,
  gender: 'male' | 'female',
  customPrompt?: string
): string => {
  // 基于prompt.js分析，转换prompt已经是完整的指令
  // 不需要添加性别前缀，直接使用style.promptTemplate
  let basePrompt = style.promptTemplate

  if (customPrompt && customPrompt.trim()) {
    return `${basePrompt}, ${customPrompt.trim()}`
  }

  return basePrompt
}
