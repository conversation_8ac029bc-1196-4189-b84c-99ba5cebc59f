'use client'

import React, { useState, useCallback, useMemo } from 'react'
import Image from 'next/image'
import { ChevronDown, ChevronUp, Sparkles } from 'lucide-react'
import { getAllStyles, getStylesByGender, type AnimeStyle } from '../config'

interface AnimeStyleExamplesProps {
  selectedGender: 'male' | 'female'
  onGenderChange: (gender: 'male' | 'female') => void
  selectedStyle: string
  onStyleSelect: (styleId: string) => void
}

const AnimeStyleExamples: React.FC<AnimeStyleExamplesProps> = ({
  selectedGender,
  onGenderChange,
  selectedStyle,
  onStyleSelect,
}) => {
  // 展开收起状态
  const [isMaleExpanded, setIsMaleExpanded] = useState(false)
  const [isFemaleExpanded, setIsFemaleExpanded] = useState(false)

  const maleStyles = getStylesByGender('male')
  const femaleStyles = getStylesByGender('female')

  // 根据展开状态决定显示的示例数量
  const displayedMaleStyles = useMemo(() => {
    return isMaleExpanded ? maleStyles : maleStyles.slice(0, 4)
  }, [isMaleExpanded, maleStyles])

  const displayedFemaleStyles = useMemo(() => {
    return isFemaleExpanded ? femaleStyles : femaleStyles.slice(0, 4)
  }, [isFemaleExpanded, femaleStyles])

  // 展开收起处理函数
  const handleMaleToggle = useCallback(() => {
    setIsMaleExpanded((prev) => !prev)
  }, [])

  const handleFemaleToggle = useCallback(() => {
    setIsFemaleExpanded((prev) => !prev)
  }, [])

  // 获取风格示例图片
  const getStyleExampleImage = (
    style: AnimeStyle,
    gender: 'male' | 'female'
  ): string => {
    // 如果是通用风格，根据性别选择示例图片
    if (style.maleExample && style.femaleExample) {
      return gender === 'male' ? style.maleExample : style.femaleExample
    }

    // 如果是性别专用风格，使用专用示例图片
    if (style.example) {
      return style.example
    }

    // 备用方案：使用第一个可用的示例图片
    return (
      style.maleExample ||
      style.femaleExample ||
      '/images/photo-to-anime/use-case-1.jpg'
    )
  }

  // 处理风格选择
  const handleStyleSelect = useCallback(
    (styleId: string, gender: 'male' | 'female') => {
      // 先设置性别
      onGenderChange(gender)
      // 再设置风格
      onStyleSelect(styleId)
    },
    [onGenderChange, onStyleSelect]
  )

  // 渲染风格示例网格的函数
  const renderStyleGrid = (
    styles: readonly AnimeStyle[],
    gender: 'male' | 'female'
  ) => (
    <div className="grid grid-cols-4 gap-2">
      {styles.map((style) => {
        const isSelected =
          selectedStyle === style.id && selectedGender === gender
        const exampleImage = getStyleExampleImage(style, gender)

        return (
          <div
            key={`${gender}-${style.id}`}
            className="group cursor-pointer"
            onClick={() => handleStyleSelect(style.id, gender)}
          >
            <div
              className={`relative aspect-square rounded-lg overflow-hidden bg-gray-100 border-2 transition-all duration-200 ${
                isSelected
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <Image
                src={exampleImage}
                alt={`${style.name} - ${gender} example`}
                fill
                className="object-cover transition-transform"
                sizes="(max-width: 640px) 25vw, 20vw"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-opacity" />

              {/* 选中指示器 */}
              {isSelected && (
                <div className="absolute top-1 right-1 bg-blue-500 text-white rounded-full p-0.5">
                  <Sparkles className="h-2.5 w-2.5" />
                </div>
              )}

              {/* 标题覆盖层 */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="absolute bottom-0 left-0 right-0 p-1.5">
                  <p className="text-white text-[10px] font-medium line-clamp-1">
                    {style.name}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )

  return (
    <div className="space-y-4">
      {/* 男性风格示例 */}
      <div className="bg-gray-50/70 rounded-xl border border-gray-100">
        <div className="p-3">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-900">Male Style</h3>
            <button
              type="button"
              onClick={handleMaleToggle}
              className="flex items-center gap-1 px-2 py-1 rounded-md border bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300 transition-all duration-200"
              title={
                isMaleExpanded ? 'Show less examples' : 'Show all examples'
              }
            >
              {isMaleExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-700 transition-transform duration-200" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-700 transition-transform duration-200" />
              )}
              <span className="text-xs font-medium">
                {isMaleExpanded ? 'Show Less' : 'Show All'}
              </span>
            </button>
          </div>

          <div
            className={`overflow-hidden transition-all duration-300 ease-in-out ${
              isMaleExpanded ? 'max-h-[400px]' : 'max-h-[100px]'
            }`}
          >
            <div className="overflow-y-auto pr-2 no-scrollbar">
              {renderStyleGrid(displayedMaleStyles, 'male')}
            </div>
          </div>
        </div>
      </div>

      {/* 女性风格示例 */}
      <div className="bg-gray-50/70 rounded-xl border border-gray-100">
        <div className="p-3">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-gray-900">
              Female Style
            </h3>
            <button
              type="button"
              onClick={handleFemaleToggle}
              className="flex items-center gap-1 px-2 py-1 rounded-md border bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300 transition-all duration-200"
              title={
                isFemaleExpanded ? 'Show less examples' : 'Show all examples'
              }
            >
              {isFemaleExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-700 transition-transform duration-200" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-700 transition-transform duration-200" />
              )}
              <span className="text-xs font-medium">
                {isFemaleExpanded ? 'Show Less' : 'Show All'}
              </span>
            </button>
          </div>

          <div
            className={`overflow-hidden transition-all duration-300 ease-in-out ${
              isFemaleExpanded ? 'max-h-[400px]' : 'max-h-[100px]'
            }`}
          >
            <div className="overflow-y-auto pr-2 no-scrollbar">
              {renderStyleGrid(displayedFemaleStyles, 'female')}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AnimeStyleExamples
