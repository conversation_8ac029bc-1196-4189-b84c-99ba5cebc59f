// Photo to Anime API 工具函数
import { PHOTO_TO_ANIME_CONFIG, getStyleById, buildPrompt } from '../config'

// 上传图片到OSS的函数
export async function uploadToOSS(file: File): Promise<string> {
  const formData = new FormData()
  formData.append('file', file)

  try {
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    })

    if (!response.ok) {
      throw new Error('Failed to upload image')
    }

    const data = await response.json()
    return data.url
  } catch (error) {
    console.error('Upload failed:', error)
    throw new Error('Failed to upload image to server')
  }
}

// 生成动漫风格的主要函数
export async function generateAnimeStyle({
  file,
  styleId,
  aspectRatio,
  imageCount,
  customPrompt,
  selectedGender,
}: {
  file: File
  styleId: string
  aspectRatio: string
  imageCount: number
  customPrompt: string
  selectedGender: 'male' | 'female'
}): Promise<{ taskId: string }> {
  try {
    // 步骤1: 上传图片到OSS
    const imageUrl = await uploadToOSS(file)

    // 步骤2: 获取风格配置
    const style = getStyleById(styleId)
    if (!style) {
      throw new Error(`Style not found: ${styleId}`)
    }

    // 步骤3: 构建提示词
    const finalPrompt = buildPrompt(style, selectedGender, customPrompt)

    // 步骤4: 调用生成API
    const response = await fetch(PHOTO_TO_ANIME_CONFIG.api.generateEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image_url: imageUrl,
        prompt: finalPrompt,
        aspect_ratio: aspectRatio,
        num_images: imageCount,
        style_id: styleId,
        gender: selectedGender,
      }),
    })

    if (!response.ok) {
      const errorData = await response.text()

      // 处理特定错误
      if (response.status === 401) {
        throw new Error('LOGIN_REQUIRED')
      } else if (response.status === 402) {
        throw new Error('INSUFFICIENT_POINTS')
      } else {
        throw new Error(`Generation failed: ${errorData}`)
      }
    }

    const data = await response.json()

    if (!data.task_id) {
      throw new Error('Invalid response: missing task_id')
    }

    return { taskId: data.task_id }
  } catch (error) {
    console.error('Generate anime style failed:', error)
    if (error instanceof Error) {
      throw error
    }
    throw new Error('Unknown error occurred during generation')
  }
}

// 轮询任务状态的函数
export function pollTaskStatus(
  taskId: string,
  onProgress: (progress: number) => void,
  onComplete: (resultUrls: string[]) => void,
  onError: (error: string) => void
): () => void {
  let isPolling = true
  const pollInterval = PHOTO_TO_ANIME_CONFIG.api.pollInterval
  const maxPollTime = PHOTO_TO_ANIME_CONFIG.api.maxPollTime
  let startTime = Date.now()

  const poll = async () => {
    if (!isPolling) return

    // 检查是否超时
    if (Date.now() - startTime > maxPollTime) {
      isPolling = false
      onError('Generation timeout. Please try again.')
      return
    }

    try {
      const response = await fetch(
        `${PHOTO_TO_ANIME_CONFIG.api.statusEndpoint}?task_id=${taskId}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )

      if (!response.ok) {
        throw new Error(`Status check failed: ${response.statusText}`)
      }

      const data = await response.json()

      switch (data.status) {
        case 'pending':
        case 'processing':
          // 更新进度
          const progress = data.progress || 0
          onProgress(Math.min(progress, 95)) // 最大95%，留5%给完成时

          // 继续轮询
          setTimeout(poll, pollInterval)
          break

        case 'completed':
          isPolling = false
          onProgress(100)

          if (data.result_urls && data.result_urls.length > 0) {
            onComplete(data.result_urls)
          } else {
            onError('Generation completed but no results found')
          }
          break

        case 'failed':
          isPolling = false
          onError(data.error || 'Generation failed')
          break

        default:
          isPolling = false
          onError(`Unknown status: ${data.status}`)
          break
      }
    } catch (error) {
      console.error('Polling error:', error)

      // 网络错误时继续尝试，除非超时
      if (Date.now() - startTime < maxPollTime) {
        setTimeout(poll, pollInterval * 2) // 延长轮询间隔
      } else {
        isPolling = false
        onError('Failed to check generation status')
      }
    }
  }

  // 开始轮询
  setTimeout(poll, 1000) // 1秒后开始第一次轮询

  // 返回停止轮询的函数
  return () => {
    isPolling = false
  }
}

// 验证输入参数的函数
export function validateGenerationParams({
  file,
  styleId,
  aspectRatio,
  imageCount,
}: {
  file: File | null
  styleId: string
  aspectRatio: string
  imageCount: number
}): string | null {
  // 检查文件
  if (!file) {
    return 'Please upload an image file'
  }

  if (!PHOTO_TO_ANIME_CONFIG.upload.allowedTypes.includes(file.type)) {
    return 'Please upload a JPG or PNG image'
  }

  if (file.size > PHOTO_TO_ANIME_CONFIG.upload.maxFileSize) {
    return 'File size must be less than 10MB'
  }

  // 检查风格
  const style = getStyleById(styleId)
  if (!style) {
    return 'Please select a valid anime style'
  }

  // 检查宽高比
  const validAspectRatios = PHOTO_TO_ANIME_CONFIG.generation.aspectRatios.map(
    (ratio) => ratio.value
  )
  if (!validAspectRatios.includes(aspectRatio)) {
    return 'Please select a valid aspect ratio'
  }

  // 检查图片数量
  if (imageCount < 1 || imageCount > 4) {
    return 'Number of images must be between 1 and 4'
  }

  return null
}

// 生成预览提示词的函数
export function generatePreviewPrompt(
  styleId: string,
  selectedGender: 'male' | 'female',
  customPrompt: string
): string {
  const style = getStyleById(styleId)
  if (!style) {
    return 'Invalid style selected'
  }

  return buildPrompt(style, selectedGender, customPrompt)
}

// 格式化错误消息的函数
export function formatErrorMessage(error: string): string {
  const errorMessages: Record<string, string> = {
    LOGIN_REQUIRED: 'Please log in to use the photo to anime converter.',
    INSUFFICIENT_POINTS:
      'Insufficient points. Please purchase more credits to continue.',
    UPLOAD_FAILED: 'Failed to upload image. Please try again.',
    GENERATION_FAILED: 'Failed to generate anime style. Please try again.',
    NETWORK_ERROR:
      'Network error occurred. Please check your connection and try again.',
    TIMEOUT: 'Generation took too long. Please try again.',
  }

  return errorMessages[error] || error
}
