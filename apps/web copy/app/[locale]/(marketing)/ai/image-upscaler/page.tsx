// apps/web/app/[locale]/(marketing)/ai/text-to-image/page.tsx
import { getTranslations } from 'next-intl/server'
import ClientContainer from './components/ClientContainer'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('seo-generate-image-t'),
    description: t('seo-generate-image-d'),
    keywords: t('seo-generate-image-k'),
  }
}
export default function CreatePage() {
  return (
    <main className="h-screen pt-[68px] bg-gray-50">
      <div className="h-full">
        <ClientContainer />
      </div>
    </main>
  )
}
