'use client'

import { useState } from 'react'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'

export default function TestConsumePage() {
  const {
    checkPermissions,
    isLoggedIn,
    isValidSubscriber,
    hasEnoughPoints,
    isBlacklisted,
    userInfo,
  } = usePermissionCheck()
  const [isChecking, setIsChecking] = useState(false)
  const [lastResult, setLastResult] = useState<any>(null)
  const [requiredPoints, setRequiredPoints] = useState(100)
  const [scenarioName, setScenarioName] = useState('测试功能')

  const handleTestPermissions = async () => {
    setIsChecking(true)
    setLastResult(null)

    //  const result = await checkPermissions({
    //     scenarioId: findCurrentMenuItem()?.title,
    //     requiredPoints: findCurrentMenuItem()?.point,
    //     scenarioName: findCurrentMenuItem()?.title,
    //   })

    try {
      const result = await checkPermissions()
      if (!result.success) {
        return
      }

      setLastResult(result)
      console.log('权限检查结果:', result)
    } catch (error) {
      console.error('权限检查异常:', error)
      setLastResult({
        success: false,
        error: 'SYSTEM_ERROR',
        message: '系统异常',
      })
    } finally {
      setIsChecking(false)
    }
  }

  const testScenarios = [
    { name: '人脸互换', points: 100, id: 'face-swap' },
    { name: '图片转视频', points: 200, id: 'photo-to-video' },
    { name: '背景移除', points: 50, id: 'background-removal' },
    { name: '图片放大', points: 80, id: 'image-upscaler' },
    { name: '老照片修复', points: 120, id: 'photo-restoration' },
  ]

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="bg-white rounded-3xl shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          🧪 权限校验系统测试
        </h1>

        {/* 用户状态展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              用户状态
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>登录状态:</span>
                <span
                  className={`font-medium ${
                    isLoggedIn ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {isLoggedIn ? '已登录' : '未登录'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>订阅状态:</span>
                <span
                  className={`font-medium ${
                    isValidSubscriber() ? 'text-green-600' : 'text-orange-600'
                  }`}
                >
                  {isValidSubscriber() ? '有效会员' : '非会员'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>积分数量:</span>
                <span className="font-medium text-blue-600">
                  {userInfo?.points || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span>账户状态:</span>
                <span
                  className={`font-medium ${
                    isBlacklisted() ? 'text-red-600' : 'text-green-600'
                  }`}
                >
                  {isBlacklisted() ? '受限' : '正常'}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              用户信息
            </h3>
            {userInfo ? (
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>用户名:</span>
                  <span className="font-medium">{userInfo.username}</span>
                </div>
                <div className="flex justify-between">
                  <span>邮箱:</span>
                  <span className="font-medium">{userInfo.email}</span>
                </div>
                <div className="flex justify-between">
                  <span>会员级别:</span>
                  <span className="font-medium">
                    {userInfo.membership_level}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>会员状态:</span>
                  <span className="font-medium">
                    {userInfo.membership_status}
                  </span>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-sm">请先登录查看用户信息</p>
            )}
          </div>
        </div>

        {/* 自定义测试 */}
        <div className="bg-purple-50 rounded-xl p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            自定义测试
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                功能名称
              </label>
              <input
                type="text"
                value={scenarioName}
                onChange={(e) => setScenarioName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="输入功能名称"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                所需积分
              </label>
              <input
                type="number"
                value={requiredPoints}
                onChange={(e) => setRequiredPoints(Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="输入所需积分"
                min="0"
              />
            </div>
          </div>
          <button
            onClick={handleTestPermissions}
            disabled={isChecking}
            className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"
          >
            {isChecking ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                检查中...
              </>
            ) : (
              '🚀 测试权限校验'
            )}
          </button>
        </div>

        {/* 预设场景测试 */}
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            预设场景测试
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {testScenarios.map((scenario) => (
              <button
                key={scenario.id}
                onClick={() => {
                  setScenarioName(scenario.name)
                  setRequiredPoints(scenario.points)
                }}
                className="p-4 bg-white border-2 border-gray-200 hover:border-purple-400 rounded-xl transition-colors duration-200 text-left"
              >
                <div className="font-medium text-gray-900">{scenario.name}</div>
                <div className="text-sm text-gray-500">
                  需要 {scenario.points} 积分
                </div>
                <div className="text-xs text-purple-600 mt-1">点击设置</div>
              </button>
            ))}
          </div>
        </div>

        {/* 测试结果展示 */}
        {lastResult && (
          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              最近测试结果
            </h3>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">
                  测试结果:
                </span>
                <span
                  className={`px-3 py-1 rounded-full text-sm font-medium ${
                    lastResult.success
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {lastResult.success ? '✅ 通过' : '❌ 失败'}
                </span>
              </div>

              {!lastResult.success && (
                <>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">
                      错误类型:
                    </span>
                    <span className="text-sm text-red-600 font-medium">
                      {lastResult.error}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">
                      错误信息:
                    </span>
                    <span className="text-sm text-red-600">
                      {lastResult.message}
                    </span>
                  </div>
                </>
              )}

              <div className="bg-gray-100 rounded-lg p-3 mt-3">
                <span className="text-xs font-medium text-gray-500">
                  完整响应:
                </span>
                <pre className="text-xs text-gray-700 mt-1 overflow-x-auto">
                  {JSON.stringify(lastResult, null, 2)}
                </pre>
              </div>
            </div>
          </div>
        )}

        {/* 说明文档 */}
        <div className="bg-blue-50 rounded-xl p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            📚 权限校验流程
          </h3>
          <ol className="space-y-2 text-sm text-gray-700">
            <li className="flex items-start gap-2">
              <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">
                1
              </span>
              <span>
                <strong>登录检查</strong>
                ：检查用户是否已登录，未登录则弹出登录框
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">
                2
              </span>
              <span>
                <strong>订阅检查</strong>
                ：检查用户是否为有效会员，非会员则弹出订阅框
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">
                3
              </span>
              <span>
                <strong>积分检查</strong>
                ：检查用户积分是否足够，不足则弹出订阅框并提示积分不足
              </span>
            </li>
            <li className="flex items-start gap-2">
              <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">
                4
              </span>
              <span>
                <strong>黑名单检查</strong>
                ：检查用户是否被限制，受限则弹出系统维护提示
              </span>
            </li>
          </ol>
        </div>
      </div>
    </div>
  )
}
