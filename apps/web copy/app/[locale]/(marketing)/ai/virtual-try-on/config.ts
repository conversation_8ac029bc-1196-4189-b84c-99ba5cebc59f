// Virtual Try-On Configuration
export const VIRTUAL_TRY_ON_CONFIG = {
  // Upload Configuration
  upload: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
  },

  // UI Configuration
  ui: {
    // Pagination
    historyPageSize: 10,

    // Sample person images for testing
    samplePersonImages: [
      {
        id: 1,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/3c8e5fb8-c89f-4d6e-8cc3-dd3b497e4f60.webp',
        alt: '专业男士肖像',
        badge: 'Exclusive',
      },
      {
        id: 2,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/d77c4ffc-a1d1-4f52-8bf1-85e9f451b28f.png',
        alt: '专业女士肖像',
        badge: 'Exclusive',
      },
      {
        id: 3,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/b01d5f2e-a99b-405b-89da-3445be234b24.png',
        alt: '休闲男士肖像',
        badge: 'Exclusive',
      },
      {
        id: 4,
        url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/uploads/4994712f-816b-4171-9911-8523337ff5e6.png',
        alt: '休闲女士肖像',
      },
    ],

    // Clothes categories and items
    clothes: {
      categories: [
        {
          id: 'mens-shirts',
          name: "Men's Shirts",
          items: [
            {
              id: 1,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-01.png',
              alt: '',
              category: 'mens-shirts',
            },
            {
              id: 2,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-02.png',
              alt: '',
              category: 'mens-shirts',
            },
            {
              id: 3,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-03.png',
              alt: '',
              category: 'mens-shirts',
            },
            {
              id: 4,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-04.png',
              alt: '',
              category: 'mens-shirts',
            },
            {
              id: 5,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-05.png',
              alt: '',
              category: 'mens-shirts',
            },
            {
              id: 6,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-06.png',
              alt: '',
              category: 'mens-shirts',
            },
            {
              id: 7,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-07.png',
              alt: '',
              category: 'mens-shirts',
            },
            {
              id: 8,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-08.png',
              alt: '',
              category: 'mens-shirts',
            },
            {
              id: 9,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-09.png',
              alt: '',
              category: 'mens-shirts',
            },
          ],
        },
        {
          id: 'womens-blouses',
          name: "Women's Blouses",
          items: [
            {
              id: 16,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-shirts-01.png',
              alt: '',
              category: 'womens-blouses',
            },
            {
              id: 17,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-shirts-02.png',
              alt: '',
              category: 'womens-blouses',
            },
            {
              id: 18,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-shirts-03.png',
              alt: '',
              category: 'womens-blouses',
            },
            {
              id: 19,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-shirts-04.png',
              alt: '',
              category: 'womens-blouses',
            },
            {
              id: 20,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-shirts-05.png',
              alt: '',
              category: 'womens-blouses',
            },
            {
              id: 21,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-shirts-06.png',
              alt: '',
              category: 'womens-blouses',
            },
            {
              id: 22,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-shirts-07.png',
              alt: '',
              category: 'womens-blouses',
            },
            {
              id: 23,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-tee-01.png',
              alt: '',
              category: 'womens-blouses',
            },
            {
              id: 24,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-tee-02.png',
              alt: '',
              category: 'womens-blouses',
            },
          ],
        },
        {
          id: 'mens-suits',
          name: "Men's Suits",
          items: [
            {
              id: 25,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-01.png',
              alt: '',
              category: 'mens-suits',
            },
            {
              id: 26,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-02.png',
              alt: '',
              category: 'mens-suits',
            },
            {
              id: 27,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-03.png',
              alt: '',
              category: 'mens-suits',
            },
            {
              id: 28,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-04.png',
              alt: '',
              category: 'mens-suits',
            },
            {
              id: 29,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-05.png',
              alt: '',
              category: 'mens-suits',
            },
            {
              id: 30,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-06.png',
              alt: '',
              category: 'mens-suits',
            },
            {
              id: 31,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-07.png',
              alt: '',
              category: 'mens-suits',
            },
            {
              id: 32,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-08.png',
              alt: '',
              category: 'mens-suits',
            },
            {
              id: 33,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/suit-set.png',
              alt: '',
              category: 'mens-suits',
            },
          ],
        },
        {
          id: 'womens-suits',
          name: "Women's Suits",
          items: [
            {
              id: 34,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-01.png',
              alt: '',
              category: 'womens-suits',
            },
            {
              id: 35,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-02.png',
              alt: '',
              category: 'womens-suits',
            },
            {
              id: 36,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-03.png',
              alt: '',
              category: 'womens-suits',
            },
            {
              id: 37,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-04.png',
              alt: '',
              category: 'womens-suits',
            },
            {
              id: 38,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-05.png',
              alt: '',
              category: 'womens-suits',
            },
            {
              id: 39,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-06.png',
              alt: '',
              category: 'womens-suits',
            },
            {
              id: 40,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-07.png',
              alt: '',
              category: 'womens-suits',
            },
            {
              id: 41,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-08.png',
              alt: '',
              category: 'womens-suits',
            },
          ],
        },
        {
          id: 'mens-pants',
          name: "Men's Pants",
          items: [
            {
              id: 42,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/dress-pants.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 43,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/slim-tailored-trousers.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 44,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/straight-leg-jeans.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 45,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/skinny-jeans.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 46,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/jogger-pants.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 47,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/cargo-pants.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 48,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/techwear-pants.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 49,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/leather-pants.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 50,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/color-block-sweatpants.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 51,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/knit-wool-trousers.png',
              alt: '',
              category: 'mens-pants',
            },
            {
              id: 52,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/slim-flared-pants.png',
              alt: '',
              category: 'mens-pants',
            },
          ],
        },
        {
          id: 'womens-pants-skirts',
          name: "Women's Pants/Skirts",
          items: [
            {
              id: 53,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/wool-knit-pants.png',
              alt: '',
              category: 'womens-pants-skirts',
            },
            {
              id: 54,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/casual-wide-leg-pants.png',
              alt: '',
              category: 'womens-pants-skirts',
            },
            {
              id: 55,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/layered-skirt.png',
              alt: '',
              category: 'womens-pants-skirts',
            },
            {
              id: 56,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/floor-length-maxi-skirt.png',
              alt: '',
              category: 'womens-pants-skirts',
            },
            {
              id: 57,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/a-line-skirt.png',
              alt: '',
              category: 'womens-pants-skirts',
            },
          ],
        },
        {
          id: 'suits',
          name: 'Suits',
          items: [
            {
              id: 58,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/racing-suit-set.png',
              alt: '',
              category: 'suits',
            },
            {
              id: 59,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/sportswear-set.png',
              alt: '',
              category: 'suits',
            },
            {
              id: 60,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/workwear-set.png',
              alt: '',
              category: 'suits',
            },
            {
              id: 61,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/chinese-style-set.png',
              alt: '',
              category: 'suits',
            },
            {
              id: 62,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/denim-set.png',
              alt: '',
              category: 'suits',
            },
          ],
        },
        {
          id: 'men-others',
          name: 'Men | Others',
          items: [
            {
              id: 10,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/polo-shirt-01.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 11,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/polo-shirt-02.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 12,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/tee-01.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 13,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/tee-02.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 14,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/tank-top-01.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 15,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/tank-top-02.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 63,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/sweatshirts-01.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 64,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/sweatshirts-02.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 65,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/hoodies-01.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 66,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/hoodies-02.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 67,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/pullover-sweaters-01.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 68,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/pullover-sweaters-02.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 69,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/sports-top-01.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 70,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/sports-top-02.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 71,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/jacket.png',
              alt: '',
              category: 'men-others',
            },
            {
              id: 72,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/leather-clothing.png',
              alt: '',
              category: 'men-others',
            },
          ],
        },
        {
          id: 'women-others',
          name: 'Women | Others',
          items: [
            {
              id: 73,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-sweatshirts-01.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 74,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-sweatshirts-02.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 75,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-sweatshirts-03.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 76,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-hoodies-01.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 77,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-hoodies-02.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 78,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-hoodies-03.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 79,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-pullover-sweaters-01.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 80,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-pullover-sweaters-02.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 81,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-sports-top-01.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 82,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-sports-top-02.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 83,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-sports-top-03.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 84,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-jacket.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 85,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/womens-leather-clothing.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 86,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/floral-dress.png',
              alt: '',
              category: 'women-others',
            },
            {
              id: 87,
              url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/french-tea-dress.png',
              alt: '',
              category: 'women-others',
            },
          ],
        },
      ],
    },

    // Animation and transition settings
    transitions: {
      duration: 200,
      easing: 'ease-in-out',
    },
  },

  // Feature Flags
  features: {
    enableHistory: true,
    enableSampleImages: true,
    enableDownload: true,
    enableWebhook: true,
    enableClothesUpload: false, // Temporarily disable user clothes upload feature

    // Development features
    enableDevTools: process.env.NODE_ENV === 'development',
    enableDebugLogs: process.env.NODE_ENV === 'development',
  },

  // Error Messages
  messages: {
    errors: {
      uploadFailed: 'Image upload failed, please try again.',
      generateFailed: 'Virtual try-on failed, please try again.',
      loginRequired: 'Please login to use this feature.',
      invalidFileType: 'Please select a valid image file (JPG, PNG).',
      fileTooLarge: 'File size cannot exceed 10MB.',
      networkError: 'Network error, please check your connection.',
      noPersonImage: 'Please upload a person image first.',
      noClothesSelected: 'Please select clothes to try on.',
    },
    success: {
      uploadComplete: 'Image upload successful!',
      generateComplete: 'Virtual try-on completed!',
      downloadStarted: 'Download started.',
    },
    info: {
      uploadInProgress: 'Uploading image...',
      generateInProgress: 'Generating virtual try-on...',
      processingTask: 'Processing your request...',
    },
  },
} as const

// Type helpers
export type ClothesItem = {
  readonly id: number
  readonly url: string
  readonly alt: string
  readonly category: string
}

export type ClothesCategory = {
  readonly id: string
  readonly name: string
  readonly items: readonly ClothesItem[]
}

export type SampleImage = {
  readonly id: number
  readonly url: string
  readonly alt: string
  readonly badge?: string
}

export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed'

export const isValidImageFile = (file: File): boolean => {
  const allowedTypes = VIRTUAL_TRY_ON_CONFIG.upload
    .allowedTypes as readonly string[]
  return (
    allowedTypes.includes(file.type) &&
    file.size <= VIRTUAL_TRY_ON_CONFIG.upload.maxFileSize
  )
}

export const getErrorMessage = (
  errorKey: keyof typeof VIRTUAL_TRY_ON_CONFIG.messages.errors
): string => {
  return VIRTUAL_TRY_ON_CONFIG.messages.errors[errorKey]
}

export const getSuccessMessage = (
  successKey: keyof typeof VIRTUAL_TRY_ON_CONFIG.messages.success
): string => {
  return VIRTUAL_TRY_ON_CONFIG.messages.success[successKey]
}

export const getInfoMessage = (
  infoKey: keyof typeof VIRTUAL_TRY_ON_CONFIG.messages.info
): string => {
  return VIRTUAL_TRY_ON_CONFIG.messages.info[infoKey]
}

// Get all clothes items from all categories
export const getAllClothesItems = (): ClothesItem[] => {
  const allItems: ClothesItem[] = []
  VIRTUAL_TRY_ON_CONFIG.ui.clothes.categories.forEach((category) => {
    allItems.push(...category.items)
  })
  return allItems
}

// Get clothes items by category
export const getClothesByCategory = (categoryId: string): ClothesItem[] => {
  const category = VIRTUAL_TRY_ON_CONFIG.ui.clothes.categories.find(
    (cat) => cat.id === categoryId
  )
  return category ? [...category.items] : []
}
