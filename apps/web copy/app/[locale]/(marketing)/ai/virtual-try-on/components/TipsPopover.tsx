'use client'

import React from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@ui/components/dropdown-menu'
import { CheckCircle, X, Info } from 'lucide-react'

interface TipsPopoverProps {
  children: React.ReactNode
}

export function TipsPopover({ children }: TipsPopoverProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        side="right"
        align="start"
        className="w-[600px] max-h-[500px] overflow-y-auto bg-white p-0"
        sideOffset={8}
      >
        <div className="p-4">
          <div className="space-y-4">
            {/* 正确示范 */}
            <div className="border rounded-lg overflow-hidden">
              {/* 正确示范标题 */}
              <div className="flex items-center gap-3 bg-green-50 px-6 py-4 border-b">
                <div className="flex items-center justify-center w-6 h-6 bg-green-500 rounded-full">
                  <CheckCircle className="w-4 h-4 text-white" />
                </div>
                <span className="text-base font-semibold text-green-700">
                  Correct Examples
                </span>
              </div>

              <div className="p-4">
                <div className="flex gap-4 items-start">
                  {/* 示例图片网格 - 2x2 */}
                  <div className="flex-shrink-0">
                    <div className="grid grid-cols-2 gap-2 w-32">
                      {[
                        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-shirts-01.png',
                        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-shirts-01.png',
                        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/men-suits-01.png',
                        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/clothes/clothes/women-formal-wear-01.png',
                      ].map((imageSrc, index) => (
                        <div
                          key={index}
                          className="aspect-square rounded overflow-hidden bg-gray-100 border border-gray-200"
                        >
                          <img
                            src={imageSrc}
                            alt={`Correct example ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // 如果图片加载失败，显示占位符
                              const target = e.target as HTMLImageElement
                              target.src = `https://via.placeholder.com/60x60/22c55e/ffffff?text=✓`
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 正确示范文字列表 */}
                  <div className="flex-1 space-y-2">
                    {[
                      'Clear clothing subject',
                      'Clean and unobstructed clothing',
                      'Simple and clear clothing details',
                      'Clothing laid flat',
                    ].map((text, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="flex items-center justify-center w-3 h-3 bg-green-500 rounded-full flex-shrink-0">
                          <CheckCircle className="w-2 h-2 text-white" />
                        </div>
                        <span className="text-sm text-gray-700">{text}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* 错误示范 */}
            <div className="border rounded-lg overflow-hidden">
              {/* 错误示范标题 */}
              <div className="flex items-center gap-3 bg-red-50 px-6 py-4 border-b">
                <div className="flex items-center justify-center w-6 h-6 bg-red-500 rounded-full">
                  <X className="w-4 h-4 text-white" />
                </div>
                <span className="text-base font-semibold text-red-700">
                  Incorrect Examples
                </span>
              </div>

              <div className="p-4">
                <div className="flex gap-4 items-start">
                  {/* 示例图片网格 - 2x2 */}
                  <div className="flex-shrink-0">
                    <div className="grid grid-cols-2 gap-2 w-32">
                      {[
                        '/images/clothes/wrong1.png',
                        '/images/clothes/wrong2.png',
                        '/images/clothes/wrong3.png',
                        '/images/clothes/wrong4.webp',
                      ].map((imageSrc, index) => (
                        <div
                          key={index}
                          className="aspect-square rounded overflow-hidden bg-gray-100 border border-gray-200"
                        >
                          <img
                            src={imageSrc}
                            alt={`Wrong example ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              // 如果图片加载失败，显示占位符
                              const target = e.target as HTMLImageElement
                              target.src = `https://via.placeholder.com/60x60/ef4444/ffffff?text=✗`
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 错误示范文字列表 */}
                  <div className="flex-1 space-y-2">
                    {[
                      'Multiple clothing items or people',
                      'Complex background',
                      'Folded or obscured clothing',
                      'Extra floating text',
                    ].map((text, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <div className="flex items-center justify-center w-3 h-3 bg-red-500 rounded-full flex-shrink-0">
                          <X className="w-2 h-2 text-white" />
                        </div>
                        <span className="text-sm text-gray-700">{text}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
