'use client'

import React, { useState, useCallback } from 'react'
import { ChevronRight, ChevronDown } from 'lucide-react'
import { ClothesCategory, ClothesItem } from '../config'
import Image from 'next/image'

interface ClothesSelectorProps {
  categories: readonly ClothesCategory[]
  onSelectClothes: (item: ClothesItem) => void
  selectedClothesId?: number
  title?: string
}

export function ClothesSelector({
  categories,
  onSelectClothes,
  selectedClothesId,
  title = 'Select Clothes',
}: ClothesSelectorProps) {
  // 使用对象管理每个分类的展开状态
  const [expandedCategories, setExpandedCategories] = useState<
    Record<string, boolean>
  >({})

  // 切换分类展开状态
  const toggleCategory = useCallback((categoryId: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }))
  }, [])

  return (
    <div className="w-full">
      <h3 className="text-lg font-medium text-gray-900 mb-4">{title}</h3>

      {/* 固定高度的滚动容器 */}
      <div
        className="h-[calc(100vh-26.5rem)] overflow-y-auto space-y-4 pr-2"
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: '#e5e7eb #f9fafb',
        }}
      >
        {categories.map((category) => {
          const isExpanded = expandedCategories[category.id] || false
          const displayItems = isExpanded
            ? category.items
            : category.items.slice(0, 4)

          return (
            <div key={category.id} className="bg-white  overflow-hidden">
              {/* 分类标题和See All按钮 */}
              <div className="px-2 flex items-center justify-between ">
                <h4 className="font-medium text-gray-900">{category.name}</h4>
                {category.items.length > 4 && (
                  <button
                    onClick={() => toggleCategory(category.id)}
                    className="text-xs text-blue-600 hover:text-blue-700 font-medium flex items-center gap-1 transition-all duration-300"
                  >
                    {isExpanded ? (
                      <>
                        <ChevronDown className="w-3 h-3 transition-transform duration-300" />
                        Back
                      </>
                    ) : (
                      <>
                        See All
                        <ChevronRight className="w-3 h-3 transition-transform duration-300" />
                      </>
                    )}
                  </button>
                )}
              </div>

              {/* 衣服网格 */}
              <div className="p-4">
                <div className="grid grid-cols-4 gap-3">
                  {displayItems.map((item) => (
                    <div
                      key={item.id}
                      className={`
                        relative aspect-square rounded-lg cursor-pointer
                        bg-gray-50 border-2 transition-all duration-300 ease-in-out
                        transform hover:scale-105 hover:shadow-md p-2
                        ${
                          selectedClothesId === item.id
                            ? 'border-blue-500 bg-blue-50 shadow-lg scale-105'
                            : 'border-gray-200 hover:border-gray-300'
                        }
                      `}
                      onClick={() => onSelectClothes(item)}
                    >
                      <Image
                        src={item.url}
                        alt={item.alt || ''}
                        className="w-full h-full object-cover rounded-md"
                        loading="eager"
                        width={100}
                        height={100}
                      />

                      {/* 选中状态指示器 */}
                      {selectedClothesId === item.id && (
                        <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center transform transition-all duration-300 ease-in-out scale-110">
                          <svg
                            className="w-3 h-3 text-white"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
