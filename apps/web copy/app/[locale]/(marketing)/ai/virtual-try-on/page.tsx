'use client'

import React, { useState, useCallback } from 'react'
import { ClothesSelector } from './components/ClothesSelector'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@ui/components/tabs'
import { useAuth } from '../../../../../modules/ui/hooks/use-auth'

import { HistoryIcon, ImageIcon, RotateCcw, Info } from 'lucide-react'
import {
  VIRTUAL_TRY_ON_CONFIG,
  ClothesItem,
  getAllClothesItems,
} from './config'
import { useUpload } from '../face-swap/hooks/useUpload'
import { GenerateButton } from '../face-swap/components/GenerateButton'
import { UploadImage } from '../face-swap/components/UploadImage'
import { TipsPopover } from './components/TipsPopover'

// 导入新的统一组件和hooks
import {
  useUnifiedGeneration,
  useUnifiedHistory,
  UnifiedHistoryTab,
  TaskType,
  GenerationProgress,
  useGenerationProgress,
} from '../components'
import { downloadImage } from './lib/utils'

const TASK_TYPE: TaskType = 'ai_try_on'

export default function VirtualTryOnPage() {
  // 服装选择状态
  const [selectedClothes, setSelectedClothes] = useState<ClothesItem | null>(
    null
  )
  const [activeTab, setActiveTab] = useState('creations')

  const { isLoggedIn } = useAuth()

  // 进度管理hook - 30秒时间
  const {
    progress: generationProgress,
    startProgress,
    stopProgress,
    resetProgress,
    setProgress: setGenerationProgress,
  } = useGenerationProgress(99, 120000) // 120秒时间

  // 图片上传hooks - 人物照片和衣服图片
  const personUpload = useUpload()
  const clothesUpload = useUpload()

  // 使用统一的生成hook
  const {
    isGenerating,
    taskId,
    error: generateError,
    taskDetail,
    isPolling,
    pollingError,
    generate,
    reset: resetGeneration,
    stopPolling,
  } = useUnifiedGeneration(TASK_TYPE)

  // 使用统一的历史记录hook
  const {
    items: historyItems,
    isLoading: isLoadingHistory,
    error: historyError,
    refreshHistory,
  } = useUnifiedHistory(TASK_TYPE)

  // 生成虚拟试衣
  const handleGenerate = useCallback(async () => {
    // 检查人物照片
    if (!personUpload.imageUrl) {
      return
    }

    // 检查衣服图片（优先使用上传的，其次使用选择的）
    const clothesImageUrl = clothesUpload.imageUrl || selectedClothes?.url
    if (!clothesImageUrl) {
      return
    }

    try {
      // 切换到 creations tab
      setActiveTab('creations')

      // 开始进度计时器
      startProgress()

      // 使用统一的生成接口
      await generate({
        model_input: personUpload.imageUrl,
        dress_input: clothesImageUrl,
      })
    } catch (error) {
      console.error('Failed to generate virtual try-on:', error)
      // 生成失败时重置进度
      resetProgress()
    }
  }, [
    personUpload.imageUrl,
    clothesUpload.imageUrl,
    selectedClothes?.url,
    generate,
    startProgress,
    resetProgress,
    setActiveTab,
  ])

  // 处理登录逻辑（现在由统一hook处理）
  const handleLogin = useCallback(() => {
    // 登录逻辑现在由统一的生成hook处理
  }, [])

  // 重置所有上传的图片和选择的服装
  const handleReset = useCallback(() => {
    personUpload.reset()
    clothesUpload.reset()
    setSelectedClothes(null)
    resetGeneration()
    stopPolling()

    // 重置进度状态
    resetProgress()
  }, [personUpload, clothesUpload, resetGeneration, stopPolling, resetProgress])

  // 服装选择
  const handleClothesSelect = useCallback(
    (clothesItem: ClothesItem) => {
      setSelectedClothes(clothesItem)
      // 同时在衣服上传组件中显示选中的衣服
      clothesUpload.setImageUrl(clothesItem.url)
    },
    [clothesUpload]
  )

  // 衣服上传处理（清除预设选择）
  const handleClothesUpload = useCallback(
    (file: File) => {
      // 用户手动上传衣服时，清除预设选择
      setSelectedClothes(null)
      clothesUpload.uploadImage(file)
    },
    [clothesUpload]
  )

  // 从历史记录重新生成
  const handleRegenerateFromHistory = useCallback(
    async (input: Record<string, any>) => {
      if (!input.model_input || !input.dress_input) return

      // 设置人物图片URL
      personUpload.setImageUrl(input.model_input)

      // 尝试找到对应的预设服装项
      const allItems = getAllClothesItems()
      const clothesItem = allItems.find(
        (item) => item.url === input.dress_input
      )

      if (clothesItem) {
        // 如果是预设服装，设置selectedClothes并在上传组件中显示
        setSelectedClothes(clothesItem)
        clothesUpload.setImageUrl(input.dress_input)
      } else {
        // 如果不是预设服装，说明是用户上传的，设置clothesUpload并清除selectedClothes
        clothesUpload.setImageUrl(input.dress_input)
        setSelectedClothes(null)
      }

      try {
        // 开始进度计时器
        startProgress()

        await generate({
          model_input: input.model_input,
          dress_input: input.dress_input,
        })
      } catch (error) {
        console.error('Failed to regenerate virtual try-on:', error)
        // 生成失败时重置进度
        resetProgress()
      }
    },
    [personUpload, clothesUpload, generate, startProgress, resetProgress]
  )

  // 初始化历史记录
  React.useEffect(() => {
    if (isLoggedIn && historyItems.length === 0) {
      refreshHistory()
    }
  }, [isLoggedIn, historyItems.length, refreshHistory])

  // 监听任务完成，自动刷新历史记录
  React.useEffect(() => {
    if (
      taskDetail &&
      (taskDetail.status.status === 'success' ||
        taskDetail.status.status === 'failed')
    ) {
      // 任务完成后延迟刷新历史记录，确保webhook已更新数据库
      setTimeout(() => {
        refreshHistory()
      }, 1000)
    }
  }, [taskDetail?.status.status, refreshHistory])

  // 监听任务完成状态，更新进度到100%
  React.useEffect(() => {
    if (taskDetail?.status.status === 'success') {
      // 任务成功完成，将进度设置为100%并停止计时器
      setGenerationProgress(100)
      stopProgress()
    } else if (taskDetail?.status.status === 'failed') {
      // 任务失败，重置进度
      resetProgress()
    }
  }, [
    taskDetail?.status.status,
    setGenerationProgress,
    stopProgress,
    resetProgress,
  ])

  // 适配现有的ResultDisplay组件
  const adaptedTaskDetail = taskDetail
    ? {
        taskId: taskDetail.taskId,
        status:
          taskDetail.status.status === 'success'
            ? 'completed'
            : taskDetail.status.status === 'failed'
            ? 'failed'
            : taskDetail.status.status === 'processing'
            ? 'processing'
            : 'pending',
        inputParams: taskDetail.input,
        resultData: taskDetail.output,
        error: taskDetail.error?.message,
      }
    : null

  return (
    <main className="h-screen pt-[68px] bg-gray-50">
      {/* Main Layout Container */}
      <div className="h-full flex bg-white border-t border-gray-200">
        {/* Left Sidebar - Operations Area */}
        <div className="w-[480px] border-r border-gray-200 bg-white flex flex-col">
          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Upload Person Image Section */}
              <div className="w-full flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Upload Image
                </h3>

                <div>
                  {/* Reset Button */}
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleReset}
                      className="flex items-center justify-center gap-2 bg-[#4B6BFB] hover:bg-[#4B6BFB]/80 px-2 py-1 text-white font-semibold text-sm rounded-md transition-all duration-200"
                    >
                      <RotateCcw className="h-4 w-4" />
                      <span>reset all</span>
                    </button>

                    <TipsPopover>
                      <button
                        className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
                        title="Upload Tips"
                      >
                        <Info className="h-4 w-4 text-gray-600" />
                      </button>
                    </TipsPopover>
                  </div>
                </div>
              </div>

              <div className="h-28 w-full flex items-center justify-center">
                <div className="w-1/2 h-full mx-2">
                  <UploadImage
                    title="Upload Photo"
                    imageUrl={personUpload.imageUrl}
                    isUploading={personUpload.isUploading}
                    progress={personUpload.progress}
                    onUpload={personUpload.uploadImage}
                    className="h-28"
                    featureType="virtual-try-on"
                  />
                </div>
                <div className="w-1/2 h-full mx-2">
                  <UploadImage
                    title="Upload Clothes"
                    imageUrl={clothesUpload.imageUrl}
                    isUploading={clothesUpload.isUploading}
                    progress={clothesUpload.progress}
                    onUpload={handleClothesUpload}
                    className="h-28"
                    featureType="virtual-try-on"
                  />
                </div>
              </div>

              {/* Clothes Selection Section */}
              <div className="space-y-4 w-full">
                <ClothesSelector
                  categories={VIRTUAL_TRY_ON_CONFIG.ui.clothes.categories}
                  onSelectClothes={handleClothesSelect}
                  selectedClothesId={selectedClothes?.id}
                  title="Select Clothes"
                />
              </div>
            </div>
          </div>

          {/* Fixed Generate Button */}
          <GenerateButton
            swapImageUrl={personUpload.imageUrl}
            targetImageUrl={clothesUpload.imageUrl || selectedClothes?.url}
            onGenerate={handleGenerate}
            isGenerating={isGenerating}
            generateError={generateError}
          />
        </div>

        {/* Right Content Area */}
        <div className="flex-1 flex flex-col bg-gray-50">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <div className="bg-white flex justify-center">
              <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
                <TabsTrigger
                  value="creations"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <ImageIcon className="w-4 h-4" />
                  <span>Creations</span>
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
                >
                  <HistoryIcon className="w-4 h-4" />
                  <span>History</span>
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent
              value="creations"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="h-full overflow-y-auto p-4 relative">
                <div className="h-full overflow-y-auto p-4 relative">
                  <div className="min-h-full flex items-center justify-center">
                    <div className="w-full max-w-2xl">
                      {/* Header Section */}
                      <div className="text-center mb-12">
                        <h1 className="text-4xl font-bold text-gray-900 mb-4">
                          Virtual Try-On
                        </h1>
                        <p className="text-lg text-gray-600">
                          Explore endless possibilities and fun of virtual
                          try-on
                        </p>
                      </div>

                      {/* Show unified result display */}
                      {isGenerating || isPolling ? (
                        <GenerationProgress
                          progress={generationProgress}
                          isGenerating={isGenerating || isPolling}
                          title="Generating Virtual Try-On"
                          description="AI is trying on the clothes for you..."
                          size="md"
                        />
                      ) : generateError || pollingError ? (
                        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                          <div className="text-center space-y-4">
                            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                              <RotateCcw className="w-8 h-8 text-red-500" />
                            </div>
                            <div className="space-y-2">
                              <h3 className="text-lg font-semibold text-gray-900">
                                Generation Failed
                              </h3>
                              <p className="text-sm text-red-600">
                                {generateError || pollingError}
                              </p>
                            </div>
                            <button
                              onClick={() => {
                                resetGeneration()
                                stopPolling()
                                resetProgress()
                              }}
                              className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200"
                            >
                              Try Again
                            </button>
                          </div>
                        </div>
                      ) : adaptedTaskDetail?.status === 'completed' &&
                        adaptedTaskDetail?.resultData?.imageUrl ? (
                        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                          <div className="space-y-6">
                            <div className="relative max-w-md mx-auto">
                              <img
                                src={adaptedTaskDetail.resultData.imageUrl}
                                alt="Virtual try-on result"
                                className="w-full h-auto rounded-lg shadow-lg"
                              />
                            </div>
                            <div className="flex justify-center space-x-4">
                              <button
                                onClick={() => {
                                  downloadImage(
                                    adaptedTaskDetail.resultData?.imageUrl
                                  )
                                }}
                                className="flex items-center space-x-2 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 hover:shadow-lg"
                              >
                                <svg
                                  className="w-5 h-5"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                  />
                                </svg>
                                <span>Download Image</span>
                              </button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
                          <div className="text-center space-y-4">
                            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                              <span className="text-2xl">👕</span>
                            </div>
                            <h3 className="text-lg font-semibold text-gray-900">
                              Ready for Virtual Try-On
                            </h3>
                            <p className="text-sm text-gray-600">
                              Upload a person photo and select clothes to see
                              how they look together
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="history"
              className="flex-1 overflow-hidden p-4 mt-0"
            >
              <div className="h-full bg-white">
                {/* 使用新的统一历史记录组件 */}
                <UnifiedHistoryTab
                  taskType={TASK_TYPE}
                  onRegenerate={handleRegenerateFromHistory}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Login modal is now handled by the unified generation hook */}
    </main>
  )
}
