'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { UploadCloud, X, Plus, Trash2, RefreshCw } from 'lucide-react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'
import type { ImageItem } from '@marketing/home/<USER>'

// 简单的Tooltip组件
interface TooltipProps {
  children: React.ReactNode
  content: string
}

function Tooltip({ children, content }: TooltipProps) {
  return (
    <div className="relative group">
      {children}
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-800 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
        {content}
      </div>
    </div>
  )
}

interface ImageUploadAreaProps {
  images: ImageItem[]
  onImagesChange: (images: ImageItem[]) => void
  maxUploadImages?: number
  isGenerating?: boolean
  onUploadStart?: (file: File, index: number) => void
  allowDelete?: boolean
}

export function ImageUploadArea({
  images,
  onImagesChange,
  maxUploadImages = 5,
  isGenerating = false,
  onUploadStart,
  allowDelete = true,
}: ImageUploadAreaProps) {
  const t = useTranslations()
  const [hoveredImage, setHoveredImage] = useState<ImageItem | null>(null)
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 })

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      // 如果正在生成图片，则禁止上传
      if (isGenerating) return

      if (acceptedFiles.length === 0) return

      // 如果 maxUploadImages 为 1，替换现有图片
      if (maxUploadImages === 1) {
        // 清理现有图片的 blob URL
        images.forEach((image) => {
          if (image.previewUrl?.startsWith('blob:')) {
            URL.revokeObjectURL(image.previewUrl)
          }
        })

        const file = acceptedFiles[0] // 只取第一个文件
        const filePreview = URL.createObjectURL(file)
        const newImageItem: ImageItem = {
          file,
          previewUrl: filePreview,
          uploading: true,
        }

        const newImages = [newImageItem]
        onImagesChange(newImages)

        // 通知父组件开始上传
        if (onUploadStart) {
          onUploadStart(file, 0)
        }
        return
      }

      // 原有的多图片上传逻辑
      const remainingSlots = maxUploadImages - images.length
      if (remainingSlots <= 0) return

      const filesToAdd = acceptedFiles.slice(0, remainingSlots)
      const newImages = [...images]

      for (const file of filesToAdd) {
        const filePreview = URL.createObjectURL(file)
        const newImageItem: ImageItem = {
          file,
          previewUrl: filePreview,
          uploading: true,
        }

        newImages.push(newImageItem)
        const currentIndex = newImages.length - 1

        // 通知父组件开始上传
        if (onUploadStart) {
          onUploadStart(file, currentIndex)
        }
      }

      onImagesChange(newImages)
    },
    [images, maxUploadImages, onImagesChange, onUploadStart, isGenerating]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/webp': ['.webp'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    disabled: isGenerating, // 生成时禁用拖拽上传
  })

  const removeImage = useCallback(
    (index?: number) => {
      if (typeof index === 'number') {
        const newImages = [...images]
        const imageToRemove = newImages[index]

        // 如果当前悬浮预览的是要删除的图片，清除悬浮状态
        if (hoveredImage === imageToRemove) {
          setHoveredImage(null)
        }

        if (imageToRemove?.previewUrl?.startsWith('blob:')) {
          URL.revokeObjectURL(imageToRemove.previewUrl)
        }
        newImages.splice(index, 1)
        onImagesChange(newImages)
      } else {
        // 清空所有图片时也要清除悬浮状态
        setHoveredImage(null)

        images.forEach((image) => {
          if (image.previewUrl?.startsWith('blob:')) {
            URL.revokeObjectURL(image.previewUrl)
          }
        })
        onImagesChange([])
      }
    },
    [images, onImagesChange, hoveredImage]
  )

  const handleMouseEnter = (image: ImageItem, event: React.MouseEvent) => {
    setHoveredImage(image)
    // 获取整个组件的位置，而不是缩略图的位置
    const componentElement = event.currentTarget.closest('[data-upload-area]')
    if (componentElement) {
      const rect = componentElement.getBoundingClientRect()
      setHoverPosition({
        x: rect.right + 20,
        y: rect.top + rect.height + 60,
      })
    }
  }

  const handleMouseLeave = () => {
    setHoveredImage(null)
  }

  const triggerFileInput = () => {
    // 如果正在生成图片，则禁止手动触发文件选择
    if (isGenerating) return

    const dropzoneElement = document.querySelector(
      '[data-testid="dropzone-input"]'
    )
    if (dropzoneElement) {
      ;(dropzoneElement as HTMLInputElement).click()
    }
  }

  return (
    <div className="relative">
      <div
        className={`relative flex items-center gap-2 p-2 rounded-xl border-2 border-dashed transition-all min-h-[64px] ${
          isGenerating
            ? 'border-gray-200 bg-gray-50/50 cursor-not-allowed opacity-60'
            : isDragActive
            ? 'border-blue-500 bg-blue-50/50'
            : 'border-gray-300 hover:border-blue-300 hover:bg-gray-50'
        }`}
        data-upload-area
        {...getRootProps()}
      >
        <input {...getInputProps()} data-testid="dropzone-input" />

        {/* 背景提示信息 - 始终显示，z-index较低 */}
        <div className="absolute inset-0 flex items-center justify-center z-0 pointer-events-none">
          <div className="flex flex-col items-center justify-center text-center">
            <UploadCloud
              className={`h-5 w-5 mb-1 ${
                isGenerating
                  ? 'text-gray-300/50'
                  : images.length > 0
                  ? 'text-gray-300/60'
                  : 'text-gray-400'
              }`}
            />
            <p
              className={`text-sm font-medium ${
                isGenerating
                  ? 'text-gray-400/50'
                  : images.length > 0
                  ? 'text-gray-500/50'
                  : 'text-gray-700'
              }`}
            >
              {isGenerating
                ? t('ImageStyleConverter.generatingDisabled')
                : t('ImageStyleConverter.uploadButton')}
            </p>
          </div>
        </div>

        {/* 已上传图片的缩略图区域 - z-index较高，可以覆盖提示信息 */}
        {images.length > 0 && (
          <div className="relative z-10 flex gap-1.5 flex-wrap">
            {images.map((image, index) => (
              <div
                key={index}
                className={`relative rounded-lg border shadow-sm cursor-pointer transition-transform hover:scale-105 border-gray-100 hover:border-gray-200`}
                onMouseEnter={(e) => handleMouseEnter(image, e)}
                onMouseLeave={handleMouseLeave}
              >
                <div className="relative w-[42px] h-[42px] rounded-lg overflow-hidden">
                  <Image
                    src={image.previewUrl}
                    alt={`Preview ${index + 1}`}
                    fill
                    className="object-cover"
                    unoptimized
                  />

                  {/* 上传状态指示器 */}
                  {image.uploading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg">
                      <div className="h-3 w-3 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                    </div>
                  )}

                  {/* 上传错误指示器 */}
                  {image.error && (
                    <div className="absolute inset-0 flex items-center justify-center bg-red-500/80 rounded-lg">
                      <span className="text-white text-xs font-medium">!</span>
                    </div>
                  )}
                </div>

                {/* 删除按钮 */}
                {allowDelete && (
                  <button
                    type="button"
                    className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-gray-800/80 hover:bg-gray-900 flex items-center justify-center text-white transition-all duration-200 hover:scale-110 shadow-lg backdrop-blur-sm border border-white/50"
                    onClick={(e) => {
                      e.stopPropagation()
                      removeImage(index)
                    }}
                    disabled={isGenerating}
                  >
                    <X className="h-2.5 w-2.5" />
                  </button>
                )}
              </div>
            ))}
          </div>
        )}

        {/* 操作按钮区域 - z-index较高 */}
        {images.length > 0 && (
          <div className="relative z-10 flex items-center gap-2 ml-auto">
            <span className="text-xs text-blue-500 font-medium bg-blue-50 px-2 py-1 rounded-full">
              {images.length}/{maxUploadImages}
            </span>
            <div className="flex gap-1">
              {(images.length < maxUploadImages || maxUploadImages === 1) && (
                <Tooltip
                  content={
                    maxUploadImages === 1
                      ? t('ImageStyleConverter.replace')
                      : t('ImageStyleConverter.addMore')
                  }
                >
                  <button
                    type="button"
                    className="h-6 w-6 flex items-center justify-center text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-all duration-200"
                    onClick={(e) => {
                      e.stopPropagation()
                      triggerFileInput()
                    }}
                    disabled={isGenerating}
                  >
                    {maxUploadImages === 1 ? (
                      <RefreshCw className="h-3.5 w-3.5" />
                    ) : (
                      <Plus className="h-3.5 w-3.5" />
                    )}
                  </button>
                </Tooltip>
              )}
              {allowDelete && (
                <Tooltip content={t('ImageStyleConverter.clearAll')}>
                  <button
                    type="button"
                    className="h-6 w-6 flex items-center justify-center text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-all duration-200"
                    onClick={(e) => {
                      e.stopPropagation()
                      removeImage()
                    }}
                    disabled={isGenerating}
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                  </button>
                </Tooltip>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 悬浮预览 */}
      {hoveredImage && (
        <div
          className="fixed z-50 pointer-events-none animate-in fade-in zoom-in-95 duration-200"
          style={{
            left: `${hoverPosition.x}px`,
            top: `${hoverPosition.y}px`,
            transform: 'translateY(-50%)', // 垂直居中对齐整个组件
          }}
        >
          <div className="bg-white rounded-lg shadow-xl border border-gray-200 p-3">
            <div className="relative w-[300px] h-[300px]">
              <Image
                src={hoveredImage.previewUrl}
                alt="Preview"
                fill
                className="object-contain rounded"
                unoptimized
              />
            </div>
            {hoveredImage.uploading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded">
                <div className="h-8 w-8 rounded-full border-3 border-white border-t-transparent animate-spin"></div>
              </div>
            )}
            {hoveredImage.error && (
              <div className="text-center mt-3">
                <span className="text-sm text-red-500 font-medium">
                  {t('ImageStyleConverter.uploadFailed')}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
