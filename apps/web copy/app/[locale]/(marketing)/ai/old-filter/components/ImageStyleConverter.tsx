'use client'

import { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { AlertCircle, Sparkles } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useAtom, useSet<PERSON>tom } from 'jotai'
import { Button } from '@ui/components/button'
import { Form } from '@ui/components/form'
//
import {
  imagesAtom,
  formData<PERSON>tom,
  generatedTaskIdAtom,
  formSchema,
  type FormData,
  generationProgressAtom,
  isGeneratingAtom,
  generationErrorAtom,
  generatedImageUrlAtom,
  generatedImageUrlsAtom,
  currentTaskIdAtom,
} from '../lib/state'
import { OLD_FILTER_OPTIONS } from '../lib/const'
import { useModal } from '@shared/hooks/useModal'
import { getUserIdFromCookie } from '@/utils/lib'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import { useTranslations } from 'next-intl'
import { TASK_TYPES } from '@/../constants'
import { consumePointsAtom } from '@marketing/stores'
import { CreateHistoryRequest } from '@/types/history'
import {
  saveGenerationHistory,
  updateGenerationHistory,
  uploadToOSS,
  uploadMultipleToOSS,
} from '../lib/utils'
import { ImageUploadArea } from './ImageUploadArea'
import PointIcon from '../../components/ui/PointIcon'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'

// API related configuration
const API_URL_GENERATE = '/api/images/generate/old-filter'
const API_URL_STATUS = '/api/images/record-info'
const POLL_INTERVAL = 5000
const MAX_POLL_TIME = 600000 // 5 minutes
const DEFAULT_MAX_UPLOAD_IMAGES = 1

type ImageStyleConverterProps = {
  onGenerated?: (generatedUrl: string) => void
  onStartGenerating?: () => void
  onError?: () => void
  maxUploadImages?: number
}

export function ImageStyleConverter({
  onGenerated,
  onStartGenerating,
  onError,
  maxUploadImages = DEFAULT_MAX_UPLOAD_IMAGES,
}: ImageStyleConverterProps) {
  const t = useTranslations()
  const router = useRouter()
  // 使用整合的图片数据
  const [images, setImages] = useAtom(imagesAtom)
  const { showLoginModal, showInsufficientCreditsModal } = useModal()
  const [formData, setFormData] = useAtom(formDataAtom)
  const [generatedTaskId, setGeneratedTaskId] = useAtom(generatedTaskIdAtom)

  const user = getUserFromClientCookies()
  const generationMode = TASK_TYPES.OLD_FILTER

  const { calculatePoints, updateUserInfo } = usePermissionCheck()

  const points = calculatePoints()

  // 默认表单数据
  const defaultFormValues: FormData = {
    age: 'baby-podcast',
  }

  // Form configuration - 只使用默认值初始化表单
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  })

  // Consume points
  const consumePoints = useSetAtom(consumePointsAtom)

  // Use global state
  const [isGenerating, setIsGenerating] = useAtom(isGeneratingAtom)
  const [, setGenerationProgress] = useAtom(generationProgressAtom)
  const [generationError, setGenerationError] = useAtom(generationErrorAtom)
  const [, setGeneratedImageUrl] = useAtom(generatedImageUrlAtom)
  const [, setGeneratedImageUrls] = useAtom(generatedImageUrlsAtom)

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTaskId, setCurrentTaskId] = useAtom(currentTaskIdAtom)
  const [currentHistoryId, setCurrentHistoryId] = useState<number | null>(null)
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number | null>(null)

  // Create initial history data (PENDING status)
  const createInitialHistoryData = (
    taskId: string,
    originalImageUrls: string[]
  ): CreateHistoryRequest => {
    const userId = getUserIdFromCookie()
    return {
      userId,
      taskType: TASK_TYPES.OLD_FILTER,
      externalTaskId: taskId,
      status: 'PENDING',
      inputParams: {
        age: form.getValues('age'),
        originalImageUrls: originalImageUrls,
        generationMode: generationMode,
        selectedStyle: null,
      },
      metadata: {
        description: 'Image generation task started',
        originalImageCount: originalImageUrls.length,
      },
    }
  }

  const resetLoading = useCallback(() => {
    setIsGenerating(false)
    setGenerationProgress(0)
    setGeneratedTaskId(null)
    setIsSubmitting(false)
    setCurrentTaskId(null)
    setCurrentHistoryId(null)
    setGeneratedImageUrl(null)
    setGeneratedImageUrls([])
    onError?.()
  }, [
    setIsGenerating,
    setGenerationProgress,
    setGeneratedTaskId,
    setIsSubmitting,
    setCurrentTaskId,
    setGeneratedImageUrl,
    setGeneratedImageUrls,
    onError,
  ])

  // Start polling task status
  const startPolling = useCallback(
    (id: string, originalImageUrls: string[]) => {
      // If this task has already been successfully generated, return directly
      if (generatedTaskId === id) {
        setIsSubmitting(false)
        setCurrentTaskId(null)
        return
      }

      // 确保每次开始轮询时都重置开始时间
      startTimeRef.current = Date.now()
      console.log(
        '开始轮询任务:',
        id,
        '开始时间:',
        new Date(startTimeRef.current).toLocaleString()
      )

      const pollForResult = async () => {
        try {
          const response = await fetch(`${API_URL_STATUS}?taskId=${id}`, {
            method: 'GET',
            headers: {
              Accept: 'application/json',
            },
          })

          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`)
          }

          const data = await response.json()

          if (data.code === 200) {
            if (data.data.status === 'SUCCESS' && data.data.successFlag === 1) {
              // Generation successful
              const resultUrls = data.data.response.resultUrls || []

              // 保存所有生成的图片URL
              setGeneratedImageUrls(resultUrls)

              // 为了兼容性，仍然设置第一张图片为主图片
              const resultUrl = resultUrls.length > 0 ? resultUrls[0] : null
              setIsGenerating(false)
              setGenerationProgress(100)
              setGeneratedTaskId(id)

              if (resultUrls.length > 0) {
                // 设置第一张图片为主图片（兼容旧版本）
                if (resultUrl) {
                  setGeneratedImageUrl(resultUrl)
                }

                // Update history record with successful results
                if (currentHistoryId) {
                  try {
                    await updateGenerationHistory(
                      currentHistoryId,
                      getUserIdFromCookie(),
                      {
                        status: 'SUCCESS',
                        resultData: {
                          generatedImageUrls: resultUrls,
                          originalImageUrls: originalImageUrls,
                        },
                        metadata: {
                          description:
                            'Image generation task completed successfully',
                          originalImageCount: originalImageUrls.length,
                          generatedImageCount: resultUrls.length,
                        },
                        completedAt: new Date().toISOString(),
                      }
                    )
                  } catch (error) {
                    console.error('Failed to update history record:', error)
                  }
                }

                if (onGenerated && resultUrl) {
                  onGenerated(resultUrl)
                }
              }

              setIsSubmitting(false)
              setCurrentTaskId(null)

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else if (
              data.data.status === 'FAILED' ||
              (data.data.status === 'GENERATE_FAILED' &&
                data.data.successFlag === 3)
            ) {
              // Generation failed - update history record
              const errorMessage = `Generation failed: ${
                data.data.errorMessage || 'Unknown error'
              }`

              if (currentHistoryId) {
                try {
                  await updateGenerationHistory(
                    currentHistoryId,
                    getUserIdFromCookie(),
                    {
                      status: 'FAILED',
                      errorMessage: errorMessage,
                      metadata: {
                        description: 'Image generation task failed',
                        originalImageCount: originalImageUrls.length,
                        errorDetails: data.data.errorMessage || 'Unknown error',
                      },
                      completedAt: new Date().toISOString(),
                    }
                  )
                } catch (error) {
                  console.error(
                    'Failed to update failed history record:',
                    error
                  )
                }
              }

              setGenerationError(errorMessage)
              resetLoading()

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else {
              // Still processing, update progress
              const elapsed = Date.now() - (startTimeRef.current || Date.now())
              const progress = Number(data.data.progress) * 100

              // 确保进度只能向前增长，不会倒退
              setGenerationProgress((currentProgress) => {
                const newProgress = Math.max(currentProgress, progress)
                return newProgress
              })

              // Check if timeout
              if (elapsed > MAX_POLL_TIME) {
                console.log('Generation timed out, please try again')
                setGenerationError(t('ImageStyleConverter.generationTimeout'))
                resetLoading()

                if (pollIntervalRef.current) {
                  clearInterval(pollIntervalRef.current)
                  pollIntervalRef.current = null
                }
              }
            }
          } else {
            throw new Error(data.msg || 'Request failed')
          }
        } catch (err) {
          console.error('Error polling for result:', err)
          setGenerationError(t('ImageStyleConverter.generationError'))
          resetLoading()

          if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current)
            pollIntervalRef.current = null
          }
        }
      }

      // Execute immediately, then set timer
      pollForResult()
      pollIntervalRef.current = setInterval(pollForResult, POLL_INTERVAL)
    },
    [
      onGenerated,
      setGeneratedTaskId,
      generatedTaskId,
      resetLoading,
      setIsGenerating,
      setGenerationProgress,
      setGenerationError,
      setGeneratedImageUrl,
    ]
  )

  // Check if form is valid
  const isFormValid = useMemo(() => {
    return images.length > 0 && form.watch('age')
  }, [images, form])

  // Form submission handling
  const onSubmit = useCallback(
    async (values: FormData) => {
      if (!images.length) {
        setGenerationError('Please upload an image')
        return
      }

      // Check points first
      if (!user) {
        showLoginModal({
          title: t('loginTipsTitle'),
          content: t('tipLogin'),
          props: {
            needBottomArea: true, // 显示会员权益
          },
        })
        return
      }

      // If already submitting or there's an ongoing task, don't submit again
      if (isSubmitting || currentTaskId) {
        return
      }

      setGenerationError(null)
      setIsSubmitting(true)
      setIsGenerating(true)

      // 只有在没有当前任务ID时才重置进度为5%（表示这是一个新任务）
      if (!currentTaskId) {
        setGenerationProgress(5)
      }

      onStartGenerating?.()

      try {
        let filesUrl: string[] = []

        // 收集所有已上传完成的图片的 OSS URL
        const ossUrls = images
          .filter((image) => image.ossUrl) // 只选择已有 OSS URL 的图片
          .map((image) => image.ossUrl as string)

        // 检查是否所有图片都已上传完成
        if (ossUrls.length === images.length) {
          // 所有图片都已上传完成，直接使用 OSS URL
          filesUrl = ossUrls
        } else {
          // 如果有图片还未上传完成，收集所有图片文件对象
          const files = images
            .filter((image) => image.file) // 只选择有文件对象的图片
            .map((image) => image.file as File)

          // 重新上传所有图片
          filesUrl = await uploadMultipleToOSS(files)
        }

        const response = await fetch(API_URL_GENERATE, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify({
            filesUrl,
            callBackUrl: 'https://your-callback-url.com/callback',
            mode: generationMode, // 添加生成模式参数
            age: values.age || '1', // 添加变体数量参数
            size: '1:1',
          }),
        })

        if (!response.ok) {
          console.log(`HTTP error! Status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 401000) {
          showLoginModal({
            title: t('loginTipsTitle'),
            content: t('tipLogin'),
          })
          resetLoading()
          return
        }

        if (
          data.code === 400000 &&
          data.message?.en === 'Insufficient points.'
        ) {
          resetLoading()
          showInsufficientCreditsModal({
            content: t('ImageStyleConverter.insufficientPoints'),
          })
          return
        }

        if (data.code === 100000) {
          const newTaskId = data.data.taskId
          setCurrentTaskId(newTaskId)
          updateUserInfo()
          // // Save initial history record with PENDING status
          // try {
          //   const initialHistoryData = createInitialHistoryData(
          //     newTaskId,
          //     filesUrl
          //   )
          //   const historyRecord = await saveGenerationHistory(
          //     initialHistoryData
          //   )
          //   setCurrentHistoryId(historyRecord.id)
          // } catch (error) {
          //   console.error('Failed to save initial history record:', error)
          // }

          startPolling(newTaskId, filesUrl)

          // 根据不同模式消耗积分（这里假设两种模式消耗相同积分，实际可能不同）
          consumePoints(points)
        } else {
          throw new Error(data.msg || 'Request failed')
        }
      } catch (err) {
        console.error('Generation request failed:', err)
        setGenerationError(
          'Failed to initiate generation request, please try again'
        )
        resetLoading()
      }
    },
    [
      images,
      router,
      setFormData,
      onStartGenerating,
      startPolling,
      resetLoading,
      isSubmitting,
      showLoginModal,
      showInsufficientCreditsModal,
      user,
      t,
      consumePoints,
    ]
  )

  // 包装上传方法以处理错误状态
  const handleUploadToOSS = async (file: File): Promise<string> => {
    try {
      return await uploadToOSS(file)
    } catch (error) {
      console.error('Failed to upload to OSS:', error)
      setGenerationError('Failed to upload image, please try again')
      throw error
    }
  }

  // Listen for form changes
  useEffect(() => {
    const subscription = form.watch((values, { name }) => {
      console.log('Form value changed:', name, values)

      if (name === 'age') {
        // 保存表单数据
        if (values.age) {
          const formData = {
            age: values.age || '1',
          }
          // 同时更新 formData atom
          setFormData(formData)
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, setFormData])

  // Clean up polling
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current)
      }
    }
  }, [])

  return (
    <>
      <div className="w-full h-full image-style-converter">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col h-full"
          >
            <div className="flex-shrink-0 mb-3">
              <ImageUploadArea
                images={images}
                onImagesChange={setImages}
                maxUploadImages={maxUploadImages}
                isGenerating={isGenerating}
                allowDelete={false}
                onUploadStart={async (file: File, index: number) => {
                  try {
                    // 上传文件到 OSS
                    const ossUrl = await handleUploadToOSS(file)

                    // 更新图片数据
                    setImages((prevImages) => {
                      const updatedImages = [...prevImages]
                      if (index < updatedImages.length) {
                        updatedImages[index] = {
                          ...updatedImages[index],
                          ossUrl,
                          uploading: false,
                        }
                      }
                      return updatedImages
                    })
                  } catch (error) {
                    // 更新图片数据，标记错误
                    setImages((prevImages) => {
                      const updatedImages = [...prevImages]
                      if (index < updatedImages.length) {
                        updatedImages[index] = {
                          ...updatedImages[index],
                          uploading: false,
                          error: t('ImageStyleConverter.uploadFailed'),
                        }
                      }
                      return updatedImages
                    })
                    console.error(`Upload failed at index ${index}:`, error)
                  }
                }}
              />
            </div>

            <div className="overflow-y-auto">
              {/* Age selection */}
              <div className="mb-4">
                <div className="p-4">
                  <div className="grid grid-cols-4 gap-2 sm:gap-3">
                    {OLD_FILTER_OPTIONS.map((option) => (
                      <div
                        key={option.value}
                        className={`group transition-all duration-300 ease-out transform ${
                          isGenerating
                            ? 'opacity-50 cursor-not-allowed'
                            : 'cursor-pointer hover:scale-[1.01]'
                        }`}
                      >
                        {/* 图片预览 */}
                        <button
                          type="button"
                          onClick={() => {
                            form.setValue('age', option.value as any)
                          }}
                          disabled={isGenerating}
                          className={`w-full aspect-square relative overflow-hidden rounded-lg border-2 transition-all duration-300 ease-out ${
                            form.watch('age') === option.value
                              ? 'border-blue-500 shadow-lg'
                              : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                          }`}
                        >
                          <img
                            src={option.image}
                            alt={option.label}
                            className="w-full h-full object-cover transition-transform duration-300 ease-out group-hover:scale-105"
                          />
                        </button>
                        {/* 标签 */}
                        <div className="mt-1.5 px-0.5">
                          <p
                            className={`text-[9px] sm:text-[10px] font-medium text-center leading-tight transition-colors duration-200 whitespace-nowrap overflow-hidden text-ellipsis ${
                              form.watch('age') === option.value
                                ? 'text-blue-600'
                                : 'text-gray-900 group-hover:text-blue-500'
                            }`}
                          >
                            {option.label}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Generate button and progress */}
            <div className="space-y-4 flex-shrink-0">
              <Button
                type="submit"
                className="w-full bg-gradient-to-r gap-1 from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200"
                disabled={!isFormValid}
                loading={isGenerating}
                size="lg"
              >
                {isGenerating
                  ? t('ImageStyleConverter.generating')
                  : t('ImageStyleConverter.generateButton')}

                <PointIcon points={calculatePoints()} />
              </Button>

              {generationError && (
                <div
                  className={`bg-red-50 p-3 rounded-md flex items-start ${'bg-opacity-80'}`}
                >
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-600">{generationError}</p>
                </div>
              )}
            </div>
          </form>
        </Form>
      </div>
    </>
  )
}
