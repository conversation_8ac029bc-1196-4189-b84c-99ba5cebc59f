'use client'

import { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import ReactCompareImage from 'react-compare-image'
import { motion, AnimatePresence } from 'framer-motion'

// 示例图片数组
const EXAMPLE_IMAGES = [
  {
    originalImage: '/gulika/ghibli-original.jpeg',
    generatedImage: '/gulika/ghibli-generated.jpeg',
  },
  {
    originalImage: '/gulika/lego-original.jpeg',
    generatedImage: '/gulika/lego-generated.jpeg',
  },
  {
    originalImage: '/gulika/simpson-original.jpeg',
    generatedImage: '/gulika/simpson-generated.jpeg',
  },
  {
    originalImage: '/gulika/irasyo-original.jpeg',
    generatedImage: '/gulika/irasyo-generated.jpeg',
  },
  {
    originalImage: '/gulika/labi-original.jpeg',
    generatedImage: '/gulika/labi-generated.jpeg',
  },
  {
    originalImage: '/gulika/youhua-original.jpeg',
    generatedImage: '/gulika/youhua-generated.jpeg',
  },
]

// 动画变体
const slideVariants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 1000 : -1000,
    opacity: 0,
  }),
  center: {
    zIndex: 1,
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    zIndex: 0,
    x: direction < 0 ? 1000 : -1000,
    opacity: 0,
  }),
}

interface ImageCarouselProps {
  autoPlay?: boolean
  autoPlayInterval?: number
  className?: string
}

export default function ImageCarousel({
  autoPlay = true,
  autoPlayInterval = 5000,
  className = '',
}: ImageCarouselProps) {
  const [[page, direction], setPage] = useState([0, 0])

  // 轮播控制函数
  const paginate = (newDirection: number) => {
    const newPage = page + newDirection
    if (newPage < 0) {
      setPage([EXAMPLE_IMAGES.length - 1, newDirection])
    } else if (newPage >= EXAMPLE_IMAGES.length) {
      setPage([0, newDirection])
    } else {
      setPage([newPage, newDirection])
    }
  }

  // 自动轮播
  useEffect(() => {
    if (autoPlay) {
      const timer = setInterval(() => {
        paginate(1)
      }, autoPlayInterval)
      return () => clearInterval(timer)
    }
  }, [autoPlay, autoPlayInterval, page])

  return (
    <div
      className={`relative w-full max-w-3xl aspect-[16/9] overflow-hidden rounded-lg ${className}`}
    >
      <AnimatePresence initial={false} custom={direction}>
        <motion.div
          key={page}
          custom={direction}
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{
            x: {
              type: 'spring',
              stiffness: 300,
              damping: 30,
            },
            opacity: { duration: 0.2 },
          }}
          className="absolute w-full h-full rounded-lg overflow-hidden"
        >
          <ReactCompareImage
            leftImage={EXAMPLE_IMAGES[page].originalImage}
            rightImage={EXAMPLE_IMAGES[page].generatedImage}
            sliderLineWidth={2}
            sliderLineColor="#3B82F6"
            leftImageCss={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '0.5rem',
            }}
            rightImageCss={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              borderRadius: '0.5rem',
            }}
            handle={
              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center shadow-md rotate-90">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  className="w-5 h-5 text-white"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 9l4-4 4 4m0 6l-4 4-4-4"
                  />
                </svg>
              </div>
            }
            hover={false}
            skeleton={
              <div className="w-full h-full bg-gray-100 animate-pulse" />
            }
          />
        </motion.div>
      </AnimatePresence>

      {/* 轮播控制按钮 */}
      <button
        onClick={() => paginate(-1)}
        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-black/30 text-white hover:bg-black/50 transition-colors z-10"
      >
        <ChevronLeft className="h-5 w-5" />
      </button>
      <button
        onClick={() => paginate(1)}
        className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full bg-black/30 text-white hover:bg-black/50 transition-colors z-10"
      >
        <ChevronRight className="h-5 w-5" />
      </button>

      {/* 轮播指示器 */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 z-10">
        {EXAMPLE_IMAGES.map((_, index) => (
          <button
            key={index}
            onClick={() => setPage([index, index > page ? 1 : -1])}
            className={`w-2 h-2 rounded-full transition-colors ${
              page === index ? 'bg-blue-500' : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
    </div>
  )
}
