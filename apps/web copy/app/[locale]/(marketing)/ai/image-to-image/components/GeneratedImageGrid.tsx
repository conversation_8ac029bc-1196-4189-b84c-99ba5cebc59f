'use client'

import { useTranslations } from 'next-intl'
import { Download } from 'lucide-react'
import { useState, useEffect } from 'react'

interface GeneratedImageGridProps {
  imageUrls: string[]
  onDownload: (imageUrl: string, index: number) => void
  onDownloadAll: () => void
}

export default function GeneratedImageGrid({
  imageUrls,
  onDownload,
  onDownloadAll,
}: GeneratedImageGridProps) {
  const t = useTranslations()
  const [imageDimensions, setImageDimensions] = useState<
    { width: number; height: number; aspectRatio: number }[]
  >([])

  // 获取图片尺寸信息
  useEffect(() => {
    const loadImageDimensions = async () => {
      const dimensions = await Promise.all(
        imageUrls.map(
          (url) =>
            new Promise<{ width: number; height: number; aspectRatio: number }>(
              (resolve) => {
                const img = new Image()
                img.onload = () => {
                  resolve({
                    width: img.naturalWidth,
                    height: img.naturalHeight,
                    aspectRatio: img.naturalWidth / img.naturalHeight,
                  })
                }
                img.onerror = () => {
                  // 如果图片加载失败，使用默认比例
                  resolve({
                    width: 512,
                    height: 512,
                    aspectRatio: 1,
                  })
                }
                img.src = url
              }
            )
        )
      )
      setImageDimensions(dimensions)
    }

    if (imageUrls.length > 0) {
      loadImageDimensions()
    }
  }, [imageUrls])

  if (imageUrls.length === 0) {
    return null
  }

  // 根据图片数量决定网格布局
  const getGridClasses = () => {
    switch (imageUrls.length) {
      case 1:
        return 'grid-cols-1 max-w-md mx-auto'
      case 2:
        return 'grid-cols-2 gap-3 max-w-xl'
      case 4:
        return 'grid-cols-2 gap-3 max-w-xl'
      default:
        return 'grid-cols-2 sm:grid-cols-3 gap-3 max-w-xl'
    }
  }

  // 获取单个图片容器的样式
  const getImageContainerStyle = (index: number) => {
    const dimension = imageDimensions[index]
    if (!dimension) {
      // 如果尺寸还没加载完成，使用默认样式
      return {
        aspectRatio: '1',
        maxWidth: imageUrls.length === 1 ? '100%' : '200px',
      }
    }

    // 保持原始比例
    const { aspectRatio } = dimension
    return {
      aspectRatio: aspectRatio.toString(),
      maxWidth: imageUrls.length === 1 ? '100%' : '200px',
    }
  }

  return (
    <div className="w-full max-w-3xl flex flex-col items-center justify-center">
      {/* 网格布局展示生成的图片 */}
      <div className={`w-full grid ${getGridClasses()}`}>
        {imageUrls.map((imageUrl, index) => (
          <div
            key={index}
            className="relative group flex items-center justify-center bg-gray-50 rounded-lg overflow-hidden"
            style={getImageContainerStyle(index)}
          >
            <img
              src={imageUrl}
              alt={`${t('ImageGenerator.generatedImage')} ${index + 1}`}
              className="w-full h-full object-cover rounded-lg"
            />

            {/* 下载按钮 - 放在图片正中央，悬停时显示 */}
            <button
              onClick={() => onDownload(imageUrl, index)}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 p-2 rounded-full bg-black/40 text-white hover:bg-black/60 transition-colors opacity-0 group-hover:opacity-100"
              title={t('ImageGenerator.download')}
            >
              <Download className="h-4 w-4" />
            </button>
          </div>
        ))}
      </div>

      {/* 下载所有图片按钮 - 放在图片展示的正下方 */}
      {imageUrls.length > 1 && (
        <div className="w-full flex justify-center mt-4">
          <button
            onClick={onDownloadAll}
            className="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors border border-gray-300"
          >
            <Download className="mr-2 h-4 w-4" />
            Download All Images
          </button>
        </div>
      )}
    </div>
  )
}
