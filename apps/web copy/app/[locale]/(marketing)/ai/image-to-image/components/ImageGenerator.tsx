'use client'

import { useTranslations } from 'next-intl'
import { ImageStyleConverter } from './ImageStyleConverter'
import ImageHistory from './ImageHistory'
import ImageCarousel from './ImageCarousel'
import { Upload, Download, History, Image as ImageIcon } from 'lucide-react'
import { useAtom } from 'jotai'
import {
  isGeneratingAtom,
  generationProgressAtom,
  generatedImageUrlAtom,
  generatedImageUrlsAtom,
} from '../lib/state'
import { downloadImage, downloadAllImages } from '../lib/utils'
import ImageGenerationLoader from './ImageGenerationLoader'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@ui/components/tabs'
import GeneratedImageGrid from './GeneratedImageGrid'

export default function ImageGenerator() {
  const t = useTranslations()

  // 使用全局状态
  const [isGenerating] = useAtom(isGeneratingAtom)
  const [generationProgress] = useAtom(generationProgressAtom)
  const [generatedImageUrl] = useAtom(generatedImageUrlAtom)
  const [generatedImageUrls] = useAtom(generatedImageUrlsAtom)

  // 触发上传图片事件
  const handleUploadClick = () => {
    const uploadInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement
    if (uploadInput) {
      uploadInput.click()
    }
  }

  // 下载单个图片的处理函数
  const handleDownload = async (imageUrl?: string, index?: number) => {
    try {
      await downloadImage(imageUrl, generatedImageUrl, index)
    } catch (error) {
      console.error('Download failed:', error)
    }
  }

  // 下载所有图片的处理函数
  const handleDownloadAll = async () => {
    try {
      await downloadAllImages(generatedImageUrls)
    } catch (error) {
      console.error('Download all failed:', error)
    }
  }

  return (
    <div className="h-full flex bg-white border-t border-gray-200">
      {/* 左侧：图片生成器 */}
      <div className="w-[480px] border-r border-gray-200 bg-white">
        <div className="h-full overflow-y-auto p-4">
          <ImageStyleConverter redirectOnGenerate={false} maxUploadImages={5} />
        </div>
      </div>

      {/* 右侧：生成结果展示和历史记录 */}
      <div className="flex-1 flex flex-col bg-gray-50">
        <Tabs defaultValue="creations" className="h-full flex flex-col">
          {/* Tab 切换头部 */}
          <div className="bg-white flex justify-center">
            <TabsList className="bg-transparent border-b border-gray-200 rounded-none">
              <TabsTrigger
                value="creations"
                className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
              >
                <ImageIcon className="h-4 w-4" />
                <span>Creations</span>
              </TabsTrigger>
              <TabsTrigger
                value="history"
                className="px-8 py-4 gap-2 bg-transparent data-[state=active]:bg-transparent"
              >
                <History className="h-4 w-4" />
                <span>History</span>
              </TabsTrigger>
            </TabsList>
          </div>

          {/* Tab 内容 */}
          <TabsContent
            value="creations"
            className="flex-1 overflow-hidden p-4 mt-0"
          >
            <div className="h-full overflow-y-auto p-4">
              <div className="min-h-full flex items-center justify-center">
                {/* Generation content area */}
                {isGenerating ? (
                  <div className="w-full flex flex-col items-center justify-center">
                    <ImageGenerationLoader progress={generationProgress} />
                  </div>
                ) : generatedImageUrls.length > 0 ? (
                  <div className="min-h-full flex items-center justify-center">
                    <GeneratedImageGrid
                      imageUrls={generatedImageUrls}
                      onDownload={handleDownload}
                      onDownloadAll={handleDownloadAll}
                    />
                  </div>
                ) : (
                  <div className="w-full max-w-3xl flex flex-col items-center">
                    {/* 轮播图 */}
                    <ImageCarousel autoPlay={!isGenerating} className="mb-4" />

                    <div className="text-center mb-4">
                      <h4 className="text-base font-medium text-gray-700 mb-2">
                        {t('ImageGenerator.imagineUnlimited')}
                      </h4>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {t('ImageGenerator.uploadPhoto')}
                      </p>
                    </div>

                    {/* 上传按钮 */}
                    <button
                      onClick={handleUploadClick}
                      className="w-full max-w-sm px-6 py-3 border-2 border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition-all duration-200 flex items-center justify-center gap-3 group"
                    >
                      <Upload className="h-5 w-5 group-hover:scale-110 transition-transform" />
                      <span className="text-base font-medium">
                        {t('ImageGenerator.uploadButtonText')}
                      </span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent
            value="history"
            className="flex-1 overflow-hidden p-4 mt-0"
          >
            <ImageHistory className="h-full" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
