'use client'

import { useState, useEffect, useRef } from 'react'
import { useTranslations } from 'next-intl'
import { Calendar, Trash2, RefreshCw } from 'lucide-react'
import HistoryImageGrid from './HistoryImageGrid'
import { getUserIdFromCookie } from '@/utils/lib'
import { HistoryItem, TaskStatus } from '@/types/history'
import { TASK_TYPES } from '@/../constants'
import toast from 'react-hot-toast'
import { updateGenerationHistory } from '../lib/utils'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@ui/components/alert-dialog'

interface ImageHistoryProps {
  className?: string
}

interface HistoryResponse {
  success: boolean
  data: HistoryItem[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasMore: boolean
  }
}

const STATUS_COLORS: Record<TaskStatus, string> = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  PROCESSING: 'bg-blue-100 text-blue-800',
  SUCCESS: 'bg-green-100 text-green-800',
  FAILED: 'bg-red-100 text-red-800',
}

export default function ImageHistory({ className = '' }: ImageHistoryProps) {
  const t = useTranslations()
  const [historyData, setHistoryData] = useState<HistoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(false)
  const [total, setTotal] = useState(0)
  const [loadingMore, setLoadingMore] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [deleteItemId, setDeleteItemId] = useState<number | null>(null)

  // 本地化状态标签
  const getStatusLabel = (status: TaskStatus): string => {
    switch (status) {
      case 'PENDING':
        return t('ImageHistory.status.pending', { fallback: 'Pending' })
      case 'PROCESSING':
        return t('ImageHistory.status.processing', { fallback: 'Processing' })
      case 'SUCCESS':
        return t('ImageHistory.status.success', { fallback: 'Success' })
      case 'FAILED':
        return t('ImageHistory.status.failed', { fallback: 'Failed' })
      default:
        return status
    }
  }

  // 用于防止并发更新的锁
  const updateLockRef = useRef<Set<number>>(new Set())

  // 检查并更新 PENDING 和 PROCESSING 任务状态
  const checkAndUpdatePendingTasks = async (tasks: HistoryItem[]) => {
    const pendingTasks = tasks.filter(
      (task) =>
        (task.status === 'PENDING' || task.status === 'PROCESSING') &&
        task.external_task_id &&
        !updateLockRef.current.has(task.id) // 避免重复更新同一个任务
    )

    if (pendingTasks.length === 0) return

    // 为正在更新的任务加锁
    pendingTasks.forEach((task) => updateLockRef.current.add(task.id))

    try {
      // 批量检查 PENDING 任务状态
      const checkPromises = pendingTasks.map(async (task) => {
        try {
          const response = await fetch(
            `/api/images/record-info?taskId=${task.external_task_id}`,
            {
              method: 'GET',
              headers: {
                Accept: 'application/json',
              },
            }
          )

          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`)
          }

          const data = await response.json()

          if (data.code === 200) {
            let newStatus: TaskStatus = 'PENDING'
            let updateData: any = {}

            if (data.data.status === 'SUCCESS' && data.data.successFlag === 1) {
              // 任务成功完成
              const resultUrls = data.data.response.resultUrls || []
              newStatus = 'SUCCESS'
              updateData = {
                status: newStatus,
                resultData: {
                  generatedImageUrls: resultUrls,
                  originalImageUrls: task.input_params?.originalImageUrls || [],
                },
                metadata: {
                  ...task.metadata,
                  description: 'Image generation task completed successfully',
                  generatedImageCount: resultUrls.length,
                },
                completedAt: new Date().toISOString(),
              }
            } else if (
              data.data.status === 'FAILED' ||
              (data.data.status === 'GENERATE_FAILED' &&
                data.data.successFlag === 3)
            ) {
              // 任务失败
              newStatus = 'FAILED'
              updateData = {
                status: newStatus,
                errorMessage: `Generation failed: ${
                  data.data.errorMessage || 'Unknown error'
                }`,
                metadata: {
                  ...task.metadata,
                  description: 'Image generation task failed',
                  errorDetails: data.data.errorMessage || 'Unknown error',
                },
                completedAt: new Date().toISOString(),
              }
            } else if (
              data.data.status === 'PROCESSING' ||
              data.data.progress
            ) {
              // 任务仍在处理中，更新状态为 PROCESSING
              newStatus = 'PROCESSING'
              updateData = {
                status: newStatus,
                metadata: {
                  ...task.metadata,
                  description: 'Image generation task in progress',
                  progress: data.data.progress,
                },
              }
            }

            // 如果状态有变化，更新历史记录
            if (newStatus !== task.status) {
              // 检查任务是否已经完成，避免重复更新已完成的任务
              if (task.status === 'SUCCESS' || task.status === 'FAILED') {
                console.log(
                  `Task ${task.id} is already completed with status: ${task.status}, skipping update`
                )
                return
              }

              console.log(
                `Updating task ${task.id} (external_task_id: ${task.external_task_id}) from ${task.status} to ${newStatus}`
              )

              await updateGenerationHistory(task.id, task.user_id!, updateData)

              // 更新本地状态
              setHistoryData((prevData) =>
                prevData.map((item) => {
                  if (item.id === task.id) {
                    const updatedItem = {
                      ...item,
                      status: newStatus,
                      // 确保正确合并 result_data，将 resultData 映射为 result_data
                      result_data: updateData.resultData || item.result_data,
                      metadata: updateData.metadata || item.metadata,
                      error_message:
                        updateData.errorMessage || item.error_message,
                      completed_at: updateData.completedAt || item.completed_at,
                    }

                    console.log(
                      `Local state updated for task ${task.id}:`,
                      updatedItem
                    )
                    return updatedItem
                  }
                  return item
                })
              )
            }
          }
        } catch (error) {
          console.error(`Failed to check task ${task.external_task_id}:`, error)
        } finally {
          // 无论成功还是失败，都要释放锁
          updateLockRef.current.delete(task.id)
        }
      })

      await Promise.all(checkPromises)
    } finally {
      // 确保所有锁都被释放
      pendingTasks.forEach((task) => updateLockRef.current.delete(task.id))
    }
  }

  // 获取历史记录列表
  const fetchHistory = async (pageNum = 1, append = false) => {
    try {
      if (!append) {
        setLoading(true)
      } else {
        setLoadingMore(true)
      }
      setError(null)

      const userId = getUserIdFromCookie()
      if (!userId) {
        throw new Error('User not logged in')
      }

      const response = await fetch('/api/history/getList', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          page: pageNum,
          pageSize: 10,
          taskType: TASK_TYPES.IMAGE_TO_IMAGE,
          sortBy: 'created_at',
          sortOrder: 'desc',
          includeDeleted: false,
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: HistoryResponse = await response.json()

      if (!data.success) {
        throw new Error('Failed to fetch history')
      }

      if (append) {
        setHistoryData((prev) => [...prev, ...data.data])
      } else {
        setHistoryData(data.data)
      }

      setHasMore(data.pagination.hasMore)
      setTotal(data.pagination.total)
      setPage(pageNum)

      // 检查并更新 PENDING 任务状态
      setTimeout(() => {
        checkAndUpdatePendingTasks(data.data)
      }, 1000) // 延迟1秒执行，避免阻塞UI
    } catch (err) {
      console.error('Failed to fetch history:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch history')
      toast.error('Failed to load history')
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  // 打开删除确认对话框
  const openDeleteDialog = (id: number) => {
    setDeleteItemId(id)
    setShowDeleteDialog(true)
  }

  // 删除历史记录
  const deleteHistoryItem = async () => {
    if (!deleteItemId) return

    try {
      const userId = getUserIdFromCookie()
      if (!userId) {
        throw new Error('User not logged in')
      }

      const response = await fetch('/api/history/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: [deleteItemId],
          userId,
          hardDelete: false, // 软删除
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error('Failed to delete history item')
      }

      // 从列表中移除被删除的项目
      setHistoryData((prev) => prev.filter((item) => item.id !== deleteItemId))
      setTotal((prev) => prev - 1)
      toast.success(t('ImageHistory.deleteSuccess'))
    } catch (err) {
      console.error('Failed to delete history item:', err)
      toast.error(t('ImageDetail.deleteError'))
    } finally {
      setShowDeleteDialog(false)
      setDeleteItemId(null)
    }
  }

  // 下载图片
  const downloadImage = async (imageUrl: string, index?: number) => {
    try {
      const timestamp = Date.now()
      const filename =
        index !== undefined
          ? `history-image-${index + 1}-${timestamp}.png`
          : `history-image-${timestamp}.png`

      const response = await fetch(
        `/api/download?url=${encodeURIComponent(
          imageUrl
        )}&filename=${encodeURIComponent(filename)}`,
        { method: 'GET' }
      )

      if (!response.ok) {
        throw new Error(`Download failed: ${response.statusText}`)
      }

      const blob = await response.blob()
      const blobUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)

      toast.success('Image downloaded successfully')
    } catch (error) {
      console.error('Error downloading image:', error)
      toast.error('Failed to download image')
    }
  }

  // 加载更多
  const loadMore = () => {
    if (!loadingMore && hasMore) {
      fetchHistory(page + 1, true)
    }
  }

  // 刷新列表
  const refresh = () => {
    fetchHistory(1, false)
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return (
      date.toLocaleDateString() +
      ' ' +
      date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      })
    )
  }

  // 获取生成的图片URLs
  const getGeneratedImages = (item: HistoryItem): string[] => {
    if (item.result_data?.generatedImageUrls) {
      return Array.isArray(item.result_data.generatedImageUrls)
        ? item.result_data.generatedImageUrls
        : [item.result_data.generatedImageUrls]
    }
    return []
  }

  // 获取原始图片URLs
  const getOriginalImages = (item: HistoryItem): string[] => {
    if (item.result_data?.originalImageUrls) {
      return Array.isArray(item.result_data.originalImageUrls)
        ? item.result_data.originalImageUrls
        : [item.result_data.originalImageUrls]
    }
    if (item.input_params?.originalImageUrls) {
      return Array.isArray(item.input_params.originalImageUrls)
        ? item.input_params.originalImageUrls
        : [item.input_params.originalImageUrls]
    }
    return []
  }

  // 获取输入提示词
  const getPrompt = (item: HistoryItem): string => {
    return item.input_params?.prompt || 'No prompt available'
  }

  useEffect(() => {
    // 定期检查未完成的任务（每30秒检查一次）
    const interval = setInterval(() => {
      const incompleteTasks = historyData.filter(
        (task) =>
          (task.status === 'PENDING' || task.status === 'PROCESSING') &&
          task.external_task_id
      )

      if (incompleteTasks.length > 0) {
        checkAndUpdatePendingTasks(incompleteTasks)
      }
    }, 30000) // 30秒

    return () => {
      clearInterval(interval)
    }
  }, [historyData])

  useEffect(() => {
    fetchHistory()
  }, [])

  if (loading) {
    return (
      <div
        className={`flex flex-col items-center justify-center h-64 ${className}`}
      >
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-gray-600">Loading history...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div
        className={`flex flex-col items-center justify-center h-64 ${className}`}
      >
        <div className="text-red-500 mb-4">
          <svg
            className="w-12 h-12"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={refresh}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className={`w-full h-full flex flex-col ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            Generation History
          </h3>
          <p className="text-sm text-gray-600">Total: {total} items</p>
        </div>
        <button
          onClick={refresh}
          disabled={loading}
          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50"
          title="Refresh"
        >
          <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* 历史记录列表 */}
      <div className="flex-1 overflow-y-auto">
        {historyData.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <Calendar className="h-12 w-12 mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">No history found</p>
            <p className="text-sm">
              Start generating images to see your history here
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {historyData.map((item) => {
              const generatedImages = getGeneratedImages(item)
              const originalImages = getOriginalImages(item)
              const prompt = getPrompt(item)

              return (
                <div
                  key={item.id}
                  className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  {/* 状态和时间 */}
                  <div className="flex items-center justify-between mb-3">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        STATUS_COLORS[item.status]
                      }`}
                    >
                      {getStatusLabel(item.status)}
                    </span>
                    <span className="text-xs text-gray-500 flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {formatDate(item.created_at)}
                    </span>
                  </div>

                  {/* 提示词 */}
                  <div className="mb-3">
                    <h4 className="text-sm font-medium text-gray-900 mb-1">
                      Prompt:
                    </h4>
                    <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded max-h-24 overflow-y-auto">
                      {prompt}
                    </div>
                  </div>

                  {/* 原始图片和生成图片 */}
                  <div className="space-y-3">
                    {/* 原始图片 */}
                    {originalImages.length > 0 && (
                      <HistoryImageGrid
                        images={originalImages}
                        title="Original Images"
                        onDownload={downloadImage}
                      />
                    )}

                    {/* 生成的图片 */}
                    {generatedImages.length > 0 && (
                      <HistoryImageGrid
                        images={generatedImages}
                        title="Generated Images"
                        onDownload={downloadImage}
                      />
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-end mt-3 pt-3 border-t border-gray-100">
                    <button
                      onClick={() => openDeleteDialog(item.id)}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 hover:text-red-800 transition-colors"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      {t('ImageDetail.delete')}
                    </button>
                  </div>
                </div>
              )
            })}

            {/* 加载更多按钮 */}
            {hasMore && (
              <div className="flex justify-center pt-4">
                <button
                  onClick={loadMore}
                  disabled={loadingMore}
                  className="px-4 py-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loadingMore ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-600 mr-2 inline-block"></div>
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="bg-white border border-gray-200 shadow-xl">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-gray-900 text-lg font-semibold">
              {t('ImageDetail.delete')}
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600">
              {t('ImageDetail.deleteConfirm')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              className="bg-gray-100 text-gray-700 hover:bg-gray-200 border-gray-300"
              onClick={() => setShowDeleteDialog(false)}
            >
              {t('common.confirmation.cancel')}
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-500 hover:bg-red-600 text-white border-red-500 hover:border-red-600"
              onClick={deleteHistoryItem}
            >
              {t('ImageDetail.deleteButton')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
