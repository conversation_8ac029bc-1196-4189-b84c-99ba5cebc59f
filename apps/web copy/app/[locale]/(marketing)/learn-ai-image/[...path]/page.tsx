import { Link, redirect } from '@i18n/routing'
import { PostContent } from '@marketing/blog/components/PostContent'
import { getActivePathFromUrlParam } from '@shared/lib/content'
import { allLearnAiImagePosts } from 'content-collections'
import { getLocale } from 'next-intl/server'
import Image from 'next/image'
import { getBaseUrl } from 'utils'
import { TableOfContents } from '@marketing/shared/components/TableOfContents'

type Params = {
  path: string
  locale: string
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params

  const { path } = params

  const locale = await getLocale()
  const activePath = getActivePathFromUrlParam(path)
  const post = allLearnAiImagePosts.find(
    (post) => post.path === activePath && locale === post.locale
  )

  return {
    title: post?.title,
    description: post?.excerpt,
    openGraph: {
      title: post?.title,
      description: post?.excerpt,
      images: post?.image
        ? [new URL(post?.image ?? '', getBaseUrl()).toString()]
        : [],
    },
  }
}

export default async function LearnAiImagePostPage(props: {
  params: Promise<Params>
}) {
  const params = await props.params

  const { path } = params

  const locale = await getLocale()
  const activePath = getActivePathFromUrlParam(path)
  const post = allLearnAiImagePosts.find(
    (post) => post.path === activePath && locale === post.locale
  )

  if (!post) {
    return redirect({ href: '/learn-ai-image', locale })
  }

  const { title, date, authorName, authorImage, tags, image } = post

  // 处理目录数据
  const decodeEscaped = (str: string) => {
    str = str.replace(/\\u\{([0-9A-Fa-f]+)\}/g, (_, hex) =>
      String.fromCodePoint(parseInt(hex, 16))
    )
    str = str.replace(/\\u([0-9A-Fa-f]{4})/g, (_, hex) =>
      String.fromCharCode(parseInt(hex, 16))
    )
    return str.replace(/\\x([0-9A-Fa-f]{2})/g, (_, hex) =>
      String.fromCharCode(parseInt(hex, 16))
    )
  }

  const headings = (post.toc || []).map((item) => ({
    ...item,
    slug: decodeEscaped(item.slug),
    content: decodeEscaped(item.content),
  }))

  return (
    <div className="container max-w-8xl pt-32 pb-24 relative min-[1230px]:pl-[300px]">
      {/* 显示目录 - 隐藏在小屏幕上，大屏幕上显示 */}
      {headings.length > 0 && (
        <div className="hidden min-[1230px]:block">
          <TableOfContents headings={headings} />
        </div>
      )}

      <div className="mx-auto max-w-2xl">
        <div className="mb-12">
          <Link href="/learn-ai-image">&larr; Back to Learn AI Image</Link>
        </div>

        <h1 className="font-bold text-4xl">{title}</h1>

        <div className="mt-4 flex items-center justify-start gap-6">
          {authorName && (
            <div className="flex items-center">
              {authorImage && (
                <div className="relative mr-2 size-8 overflow-hidden rounded-full">
                  <Image
                    src={authorImage}
                    alt={authorName}
                    fill
                    sizes="96px"
                    className="object-cover object-center"
                  />
                </div>
              )}
              <div>
                <p className="font-semibold text-sm opacity-50">{authorName}</p>
              </div>
            </div>
          )}

          <div className="mr-0 ml-auto">
            <p className="text-sm opacity-30">
              {Intl.DateTimeFormat('en-US').format(new Date(date))}
            </p>
          </div>

          {tags && (
            <div className="flex flex-1 flex-wrap gap-2">
              {tags.map((tag) => (
                <span
                  key={tag}
                  className="font-semibold text-primary text-xs uppercase tracking-wider"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>

      {image && (
        <div className="relative mt-6 aspect-[16/9] overflow-hidden rounded-xl">
          <Image
            src={image}
            alt={title}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className="object-cover object-center"
          />
        </div>
      )}

      <div className="pb-8">
        <PostContent post={post} />
      </div>
    </div>
  )
}
