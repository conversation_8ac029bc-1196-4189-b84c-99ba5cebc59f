'use client'

import { Foot<PERSON> } from '@marketing/shared/components/Footer'
import { NavBar } from '@marketing/shared/components/NavBar'
import { UserContextProvider } from '@saas/auth/lib/user-context'
import type { PropsWithChildren } from 'react'
import { Toaster } from 'react-hot-toast'

//
export default function MarketingLayout({ children }: PropsWithChildren) {
  return (
    <UserContextProvider initialUser={null}>
      <Toaster
        position="top-center"
        reverseOrder={false}
        toastOptions={{
          // 默认样式配置
          duration: 2000,
          style: {
            background: '#333',
            color: '#fff',
          },
        }}
      />

      <NavBar />
      <main className="min-h-screen">{children}</main>
      <Footer />
    </UserContextProvider>
  )
}
