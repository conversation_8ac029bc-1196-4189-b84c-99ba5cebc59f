import { redirect } from '@i18n/routing'
import { PostContent } from '@marketing/blog/components/PostContent'
import {
  getActivePathFromUrlParam,
  getLocalizedDocumentWithFallback,
} from '@shared/lib/content'
import { allLegalPages } from 'content-collections'
import { getLocale } from 'next-intl/server'

type Params = {
  path: string
  locale: string
}

export async function generateMetadata(props: { params: Promise<Params> }) {
  const params = await props.params

  const { path } = params

  const locale = await getLocale()
  const activePath = getActivePathFromUrlParam(path)
  const page = getLocalizedDocumentWithFallback(
    allLegalPages,
    activePath,
    locale
  )

  return {
    title: page?.title,
    description: page?.description,
    openGraph: {
      title: page?.title,
    },
  }
}

export default async function BlogPostPage(props: { params: Promise<Params> }) {
  const params = await props.params

  const { path } = params

  const locale = await getLocale()
  const activePath = getActivePathFromUrlParam(path)
  const page = getLocalizedDocumentWithFallback(
    allLegalPages,
    activePath,
    locale
  )

  if (!page) {
    redirect({ href: '/', locale })
  }

  const { title, date, description, body } = page

  return (
    <div className="bg-gradient-to-br from-gray-900 via-slate-900 to-black min-h-screen text-white">
      <div className="container pt-32 pb-24">
        <div className="mb-12 text-center space-y-4">
          <h1 className="font-bold text-4xl text-white">{title}</h1>
          {date && <p className="text-gray-300">Last Updated: {date}</p>}
          {description && (
            <p className="mx-auto max-w-2xl text-gray-200">{description}</p>
          )}
        </div>

        <div className="bg-transparent">
          <PostContent content={body} />
        </div>
      </div>
    </div>
  )
}
