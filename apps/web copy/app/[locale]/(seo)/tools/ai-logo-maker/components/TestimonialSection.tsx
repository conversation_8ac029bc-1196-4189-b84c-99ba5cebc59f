import React from 'react'
import { Star } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

const TestimonialSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('aiLogoMaker')

  const testimonials = [
    {
      content: t('testimonial1Content'),
      author: t('testimonial1Author'),
      role: t('testimonial1Role'),
      rating: 5,
      avatar: 'SC',
    },
    {
      content: t('testimonial2Content'),
      author: t('testimonial2Author'),
      role: t('testimonial2Role'),
      rating: 5,
      avatar: 'MR',
    },
    {
      content: t('testimonial3Content'),
      author: t('testimonial3Author'),
      role: t('testimonial3Role'),
      rating: 5,
      avatar: 'EJ',
    },
    {
      content: t('testimonial4Content'),
      author: t('testimonial4Author'),
      role: t('testimonial4Role'),
      rating: 5,
      avatar: 'DP',
    },
  ]
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-6">
          {t('testimonialSectionTitle')}
        </h2>
        <p className="text-lg text-gray-400 text-center mb-16 max-w-3xl mx-auto">
          {t('testimonialSectionDescription')}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 flex flex-col"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.1}s ease-out both`,
              }}
            >
              {/* Rating Stars */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-4 h-4 text-yellow-400 fill-current"
                  />
                ))}
              </div>

              {/* Testimonial Content */}
              <blockquote className="text-gray-300 mb-6 italic leading-relaxed flex-grow">
                "{testimonial.content}"
              </blockquote>

              {/* Author Info */}
              <div className="flex items-center">
                {/* Avatar */}
                <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-500 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3 flex-shrink-0">
                  {testimonial.avatar}
                </div>

                {/* Author Details */}
                <div className="min-w-0">
                  <p className="text-white font-semibold text-sm truncate">
                    {testimonial.author}
                  </p>
                  <p className="text-gray-400 text-xs truncate">
                    {testimonial.role}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="text-3xl font-bold text-fuchsia-400 mb-2">
              {t('statsLogosCreated')}
            </div>
            <div className="text-gray-400">{t('statsLogosCreatedLabel')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400 mb-2">{t('statsAverageRating')}</div>
            <div className="text-gray-400">{t('statsAverageRatingLabel')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-pink-400 mb-2">{t('statsHappyReviews')}</div>
            <div className="text-gray-400">{t('statsHappyReviewsLabel')}</div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
