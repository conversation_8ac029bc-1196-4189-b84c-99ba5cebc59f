'use client'
import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
//

const FAQSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiLogoMaker')

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
    {
      question: t('faq5Question'),
      answer: t('faq5Answer'),
    },
    {
      question: t('faq6Question'),
      answer: t('faq6Answer'),
    },
  ]
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems((prev) =>
      prev.includes(index)
        ? prev.filter((item) => item !== index)
        : [...prev, index]
    )
  }

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-6">
          {t('faqSectionTitle')}
        </h2>
        <p className="text-lg text-gray-400 text-center mb-16 max-w-3xl mx-auto">
          {t('faqSectionDescription')}
        </p>

        <div className="max-w-4xl mx-auto space-y-4">
          {faqs.map((faq, index) => {
            const isOpen = openItems.includes(index)

            return (
              <div
                key={index}
                className="bg-slate-900/50 backdrop-blur-sm rounded-2xl border border-slate-800 transition-all duration-300 hover:border-purple-500 overflow-hidden"
                style={{
                  animation: `fade-in-up 0.5s ${index * 0.1}s ease-out both`,
                }}
              >
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-slate-800/30 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-white pr-8">
                    {faq.question}
                  </h3>
                  <div className="flex-shrink-0 w-6 h-6 text-purple-400">
                    {isOpen ? (
                      <ChevronUp className="w-6 h-6" />
                    ) : (
                      <ChevronDown className="w-6 h-6" />
                    )}
                  </div>
                </button>

                <div
                  className={`px-8 transition-all duration-300 ease-in-out ${
                    isOpen
                      ? 'pb-6 opacity-100 max-h-96'
                      : 'pb-0 opacity-0 max-h-0'
                  } overflow-hidden`}
                >
                  <div className="text-gray-300 leading-relaxed">
                    {faq.answer}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <h3 className="text-2xl font-semibold text-white mb-4">
            {t('stillHaveQuestionsTitle')}
          </h3>
          <p className="text-gray-400 mb-8">
            {t('stillHaveQuestionsDescription')}
          </p>
          <Link href={toolUrl}>
            <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 inline-block px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20">
              {t('tryAiLogoMakerButton')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default FAQSection
