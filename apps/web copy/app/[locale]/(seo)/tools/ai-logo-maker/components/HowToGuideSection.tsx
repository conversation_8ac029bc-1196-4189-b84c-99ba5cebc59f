import React from 'react'
import { FileText, Wand2 } from 'lucide-react'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

const HowToGuideSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('aiLogoMaker')

  const logos = [
    {
      url: '/images/ai-logo-maker/movie-logo.png',
      color: 'black',
    },
    {
      url: '/images/ai-logo-maker/glass-logo.png',
      color: 'white',
    },
    {
      url: '/images/ai-logo-maker/furniture-logo.png',
      color: 'black',
    },
  ]
  const steps = [
    {
      number: 1,
      icon: <FileText className="w-8 h-8" />,
      title: t('step1Title'),
      description: t('step1Description'),
      color: 'from-purple-600 to-pink-500',
    },
    {
      number: 2,
      icon: <Wand2 className="w-8 h-8" />,
      title: t('step2Title'),
      description: t('step2Description'),
      color: 'from-blue-600 to-purple-500',
    },
  ]
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-6">
          {t('howToSectionTitle')}
        </h2>
        <p className="text-lg text-gray-400 text-center mb-16 max-w-3xl mx-auto">
          {t('howToSectionDescription')}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {steps.map((step, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              {/* Step Number and Icon */}
              <div className="flex items-center mb-6">
                <div
                  className={`bg-gradient-to-r ${step.color} w-16 h-16 rounded-full flex items-center justify-center mr-4 shadow-lg`}
                >
                  <span className="text-white font-bold text-xl">
                    {step.number}
                  </span>
                </div>
                <div
                  className={`bg-gradient-to-br from-slate-800 to-slate-900 p-3 rounded-xl text-purple-400`}
                >
                  {step.icon}
                </div>
              </div>

              {/* Content */}
              <h3 className="text-2xl font-semibold text-white mb-4">
                {step.title}
              </h3>
              <p className="text-gray-300 leading-relaxed text-lg">
                {step.description}
              </p>

              {/* Visual indicator */}
              <div className="mt-6 p-4 bg-slate-800/30 rounded-xl border border-slate-700/50">
                <div className="flex items-center justify-center space-x-4">
                  {step.number === 1 ? (
                    // Step 1: Text input representation
                    <div className="flex-1">
                      <div className="bg-slate-700/50 rounded-lg p-3 text-gray-400 text-sm">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                          <span>{t('step1PlaceholderText')}</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Step 2: Logo generation representation
                    <div className="grid grid-cols-3 gap-2 flex-1">
                      {logos.map((item,index) => (
                        <div
                          key={index}
                          style={{
                            backgroundColor: item.color
                          }}
                          className={`bg-black rounded-md overflow-hidden aspect-square flex items-center justify-center`}
                        >
                          <img className='w-full h-full inset-0 object-contain' src={item.url} alt="" />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Link href={toolUrl}>
            <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 inline-block px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20 text-lg">
              {t('generateLogoButton')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default HowToGuideSection
