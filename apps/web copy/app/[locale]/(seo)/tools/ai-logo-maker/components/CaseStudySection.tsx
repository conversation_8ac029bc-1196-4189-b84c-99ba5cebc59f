import React from 'react'
import { <PERSON><PERSON>, <PERSON>, Palette, MessageSquare } from 'lucide-react'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

const CaseStudySection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('aiLogoMaker')

  const caseStudies = [
    {
      title: t('caseStudy1Title'),
      description: t('caseStudy1Description'),
      icon: <Shirt className="w-16 h-16 text-purple-400" />,
      ctaText: t('caseStudy1CtaText'),
      layout: 'left-image',
      imageDescription: t('caseStudy1ImageDescription'),
      altText: t('caseStudy1AltText'),
      image: '/images/ai-logo-maker/cloth-logos.png'
    },
    {
      title: t('caseStudy2Title'),
      description: t('caseStudy2Description'),
      icon: <Monitor className="w-16 h-16 text-blue-400" />,
      ctaText: t('caseStudy2CtaText'),
      layout: 'right-image',
      imageDescription: t('caseStudy2ImageDescription'),
      altText: t('caseStudy2AltText'),
       image: '/images/ai-logo-maker/web-logos.png'
    },
    {
      title: t('caseStudy3Title'),
      description: t('caseStudy3Description'),
      icon: <Palette className="w-16 h-16 text-pink-400" />,
      ctaText: t('caseStudy3CtaText'),
      layout: 'left-image',
      imageDescription: t('caseStudy3ImageDescription'),
      altText: t('caseStudy3AltText'),
      image: '/images/ai-logo-maker/art-logos.webp'
    },
    {
      title: t('caseStudy4Title'),
      description: t('caseStudy4Description'),
      icon: <MessageSquare className="w-16 h-16 text-green-400" />,
      ctaText: t('caseStudy4CtaText'),
      layout: 'right-image',
      imageDescription: t('caseStudy4ImageDescription'),
      altText: t('caseStudy4AltText'),
      image: '/images/ai-logo-maker/colors-logos.webp'
    },
  ]
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-6">
          {t('caseStudySectionTitle')}
        </h2>
        <p className="text-lg text-gray-400 text-center mb-24 max-w-3xl mx-auto">
          {t('caseStudySectionDescription')}
        </p>

        <div className="space-y-20">
          {caseStudies.map((caseStudy, index) => (
            <div
              key={index}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                caseStudy.layout === 'right-image'
                  ? 'lg:grid-flow-col-dense'
                  : ''
              }`}
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              {/* Content */}
              <div
                className={
                  caseStudy.layout === 'right-image' ? 'lg:col-start-2' : ''
                }
              >
                <h3 className="text-3xl font-semibold mb-6 text-white">
                  {caseStudy.title}
                </h3>
                <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                  {caseStudy.description}
                </p>
                <Link href={toolUrl}>
                  <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 inline-block px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20">
                    {caseStudy.ctaText}
                  </button>
                </Link>
              </div>

              {/* Visual Example */}
              <div
                className={
                  caseStudy.layout === 'right-image' ? 'lg:col-start-1' : ''
                }
              >
                <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10">
                  {/* Logo Design Preview */}
                  <div className="relative">
                    <div className="aspect-video relative overflow-hidden bg-gradient-to-br from-slate-900/80 to-purple-900/20 rounded-xl flex items-center justify-center border border-slate-600/30">
                    <img className='absolute inset-0 w-full h-full object-cover' src={caseStudy.image} alt={caseStudy.altText} />
                      <div className="text-center">
                        {/* Icon representation */}
                        <div className="mb-6 flex justify-center">
                          <div className="bg-gradient-to-br from-slate-700 to-slate-800 p-4 rounded-xl">
                            {caseStudy.icon}
                          </div>
                        </div>

                        {/* Mock logo examples */}
                        <div className="grid grid-cols-2 gap-4 max-w-xs mx-auto">
                          {[1, 2, 3, 4].map((i) => (
                            <div
                              key={i}
                              className="bg-white rounded-lg p-3 aspect-square flex items-center justify-center"
                            >
                              <div
                                className={`w-8 h-8 rounded-lg ${
                                  index === 0
                                    ? 'bg-gradient-to-br from-red-500 to-orange-500'
                                    : index === 1
                                    ? 'bg-gradient-to-br from-blue-500 to-purple-500'
                                    : index === 2
                                    ? 'bg-gradient-to-br from-pink-500 to-purple-500'
                                    : 'bg-gradient-to-br from-green-500 to-teal-500'
                                }`}
                              ></div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Example label */}
                    <div className="absolute top-4 left-4">
                      <span className="bg-purple-600/80 text-white text-xs font-medium px-3 py-1 rounded-full backdrop-blur-sm">
                        {t('aiGeneratedLabel')}
                      </span>
                    </div>
                  </div>

                  {/* Description */}
                  {/* <div className="mt-4 text-center">
                    <p className="text-gray-400 text-sm">
                      {caseStudy.imageDescription}
                    </p>
                  </div> */}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CaseStudySection
