'use client'
import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import { Paintbrush } from 'lucide-react'

const HeroSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiLogoMaker')

  const images = [
    {
      url: '/images/ai-logo-maker/vet-logo.png'
    },
     {
      url: '/images/ai-logo-maker/cafe-logo.png'
    },
    {
      url: '/images/ai-logo-maker/cat-logo.webp'
    },
   
    {
      url: '/images/ai-logo-maker/music-logo.png'
    },
  ]

  

  return (
<div className="relative z-10 my-20">
        <div className="relative container mx-auto px-4 md:px-8 py-12 md:py-24">
          <div className="text-center animate-fade-in-up">
            {/* Hero Section */}
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-fuchsia-400">
              {t('heroTitle')}
            </h1>
            <p className="text-lg md:text-xl text-gray-400 mb-12 font-normal max-w-4xl mx-auto">
              {t('heroDescription')}
            </p>

            {/* Main Content Section - Left Right Layout */}
            <div className="max-w-7xl mx-auto mb-16">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center relative">
                {/* Left Side - Upload Section */}
                <div className="space-y-8">
                  <div className="w-full group [perspective:1000px]">
                    <div className="border-2 border-dashed border-gray-700 rounded-xl p-8 bg-slate-900/20 backdrop-blur-sm transition-all duration-500 group-hover:border-purple-500 [transform:rotateX(0deg)] group-hover:[transform:rotateX(10deg)]">
                      <div className="flex flex-col items-center">
                        <div className="relative">
                          <Link href={toolUrl}>
                            <button className="relative inline-block p-px font-semibold leading-6 text-white bg-gray-800 shadow-2xl cursor-pointer group rounded-xl shadow-zinc-900 transition-transform duration-300 ease-in-out hover:scale-105 active:scale-95">
                              <span className="absolute inset-0 rounded-xl bg-gradient-to-r from-pink-500 to-purple-600 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>
                              <span className="relative z-10 block px-6 py-3 rounded-xl bg-slate-950 text-gray-400 hover:text-gray-300 hover:font-semibold font-normal">
                                <div className="relative z-10 flex items-center space-x-2">
                                  <Paintbrush className="w-5 h-5 transition-transform duration-500" />
                                  <span className="transition-all duration-500">
                                    {t('startCreatingButton')}
                                  </span>
                                </div>
                              </span>
                            </button>
                          </Link>
                        </div>
                        <p className="text-gray-400 mt-4">
                          {t('visionDescription')}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Features List */}
                  <div className="space-y-4">
                    {[
                      { icon: '✨', text: t('feature1Text') },
                      { icon: '🎨', text: t('feature2Text') },
                      { icon: '⚡', text: t('feature3Text') },
                      { icon: '🆓', text: t('feature4Text') },
                    ].map((feature, index) => (
                      <div
                        key={index}
                        className="flex items-center space-x-3 text-gray-300"
                      >
                        <span className="text-lg">{feature.icon}</span>
                        <span className="text-lg">{feature.text}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Right Side - Logo Examples */}
                <div className="flex items-center justify-center h-full">
                  <div className="relative bg-gradient-to-br from-slate-900/60 to-purple-900/20 backdrop-blur-sm rounded-2xl p-8 border border-slate-700/50 w-full max-w-lg">
                    <div className="grid grid-cols-2 gap-4">
                      {images.map((item,index) => (
                        <div
                          key={index}
                          className=" relative rounded-lg bg-white overflow-hidden p-4 aspect-square flex items-center justify-center"
                        >
                            <img className='w-full h-full rounded-lg absolute inset-0 object-contain' src={item.url} alt="" />
                        </div>
                      ))}
                    </div>
                    
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  )
}

export default HeroSection
