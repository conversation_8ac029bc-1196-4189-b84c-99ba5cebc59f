import React from 'react'
import FeatureSection from './components/FeatureSection'
import CaseStudySection from './components/CaseStudySection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'
import type { Metadata } from 'next'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'
import HeroSection from './components/HeroSection'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('aiLogoMaker')

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      url: 'https://www.imggen.org/tools/ai-logo-maker',
      type: 'website',
      images: [
        {
          url: 'https://www.imggen.org/images/og-ai-logo-maker.jpg',
          width: 1200,
          height: 630,
          alt: t('openGraphAlt'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: ['https://www.imggen.org/images/twitter-ai-logo-maker.jpg'],
    },
  }
}

const AILogoMakerPage = async () => {
  const t = await getTranslations('aiLogoMaker')

  // JSON-LD Schema
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('schemaName'),
    description: t('schemaDescription'),
    applicationCategory: 'DesignApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '1257',
    },
    url: 'https://www.imggen.org/tools/ai-logo-maker',
    image: 'https://www.imggen.org/images/ai-logo-maker-schema.jpg',
  }
  // 统一配置跳转URL
  const AI_LOGO_MAKER_TOOL_URL = '/ai/logo-generator'

  return (
    <div className="relative h-full w-full bg-[#0f172a]">
      <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)]"></div>

      {/* Animated background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1),rgba(255,255,255,0)_50%)]"></div>
        <div
          className="absolute h-[200px] w-[400px] bg-purple-500 rounded-full blur-[100px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse"
          style={{ animationDuration: '6s' }}
        ></div>
        <div
          className="absolute h-[150px] w-[300px] bg-pink-500 rounded-full blur-[80px] top-1/4 left-1/4 animate-pulse"
          style={{ animationDuration: '8s', animationDelay: '2s' }}
        ></div>
        <div
          className="absolute h-[180px] w-[350px] bg-fuchsia-500 rounded-full blur-[90px] bottom-1/4 right-1/4 animate-pulse"
          style={{ animationDuration: '7s', animationDelay: '4s' }}
        ></div>
      </div>

      {/* Add JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <HeroSection toolUrl={AI_LOGO_MAKER_TOOL_URL}/>
      {/* Components Sections */}
      <FeatureSection toolUrl={AI_LOGO_MAKER_TOOL_URL} />
      <CaseStudySection toolUrl={AI_LOGO_MAKER_TOOL_URL} />
      <HowToGuideSection toolUrl={AI_LOGO_MAKER_TOOL_URL} />
      <TestimonialSection toolUrl={AI_LOGO_MAKER_TOOL_URL} />
      <FAQSection toolUrl={AI_LOGO_MAKER_TOOL_URL} />
    </div>
  )
}

export default AILogoMakerPage
