import { Metadata } from 'next'
import React from 'react'
import { getTranslations } from 'next-intl/server'
import Hero from './components/Hero'
import Features from './components/Features'
import HowToUse from './components/HowToUse'
import UseCases from './components/UseCases'
import Testimonials from './components/Testimonials'
import FAQ from './components/FAQ'
import CTA from './components/CTA'
import './styles.css'

const url = 'https://imggen.ai/ai-remove-watermark'
const ogImage = 'https://imggen.ai/images/og-watermark-remover.jpg'
const twitterImage = 'https://imggen.ai/images/twitter-watermark-remover.jpg'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('aiRemoveWatermark')

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    authors: [{ name: 'ImgGen Team' }],
    openGraph: {
      type: 'website',
      url,
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      siteName: 'ImgGen',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: t('openGraphImageAlt'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: [twitterImage],
      creator: '@imggen_ai',
      site: '@imggen_ai',
    },
  }
}

const link = '/ai/ai-remove-watermark'

export default function AIRemoveWatermarkPage() {
  return (
    <main className="w-full min-h-screen bg-slate-900">
      <Hero link={link} />
      <Features />
      <UseCases link={link} />
      <HowToUse link={link} />
      <Testimonials />
      <FAQ link={link} />
      <CTA link={link} />
    </main>
  )
}
