'use client'
import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'

interface AnimatedImageComparisonProps {
  beforeImage: string
  afterImage: string
  className?: string
  autoPlay?: boolean
  scanDuration?: number
  pauseDuration?: number
}

export default function AnimatedImageComparison({
  beforeImage,
  afterImage,
  className = '',
  autoPlay = true,
  scanDuration = 2.5,
  pauseDuration = 1.5,
}: AnimatedImageComparisonProps) {
  const t = useTranslations('aiRemoveWatermark')
  const [animationPhase, setAnimationPhase] = useState<
    'idle' | 'scanning' | 'returning'
  >('idle')
  const [animationKey, setAnimationKey] = useState(0)

  // 自动循环扫描动画
  useEffect(() => {
    if (!autoPlay) return

    const startScanCycle = () => {
      setAnimationKey((prev) => prev + 1) // 触发新的动画周期

      // 阶段1: 开始扫描
      setAnimationPhase('scanning')

      // 阶段2: 扫描完成，开始返回
      const returnTimer = setTimeout(() => {
        setAnimationPhase('returning')
      }, scanDuration * 1000)

      // 阶段3: 返回完成，进入空闲
      const idleTimer = setTimeout(() => {
        setAnimationPhase('idle')
      }, (scanDuration + scanDuration * 0.8) * 1000) // 返回动画稍快一些

      return () => {
        clearTimeout(returnTimer)
        clearTimeout(idleTimer)
      }
    }

    // 立即开始第一次扫描
    startScanCycle()

    // 设置循环扫描
    const totalCycleDuration =
      scanDuration + scanDuration * 0.8 + pauseDuration * 0.5
    const interval = setInterval(() => {
      startScanCycle()
    }, totalCycleDuration * 1000)

    return () => {
      clearInterval(interval)
    }
  }, [autoPlay, scanDuration, pauseDuration])

  return (
    <div className={`relative group ${className}`}>
      <div className="relative w-full h-64 rounded-2xl overflow-hidden shadow-2xl">
        {/* Before 图片 (底层) */}
        <div className="absolute inset-0">
          <img
            src={beforeImage}
            alt={t('beforeProcessingAlt')}
            className="object-cover w-full h-full"
          />
        </div>

        {/* After 图片 (顶层，通过clip-path控制显示区域) */}
        <motion.div
          key={`after-${animationKey}`}
          className="absolute inset-0 bg-black"
          initial={{
            clipPath: 'inset(0 100% 0 0)',
          }}
          animate={
            animationPhase === 'scanning'
              ? {
                  clipPath: 'inset(0 0% 0 0)',
                }
              : animationPhase === 'returning'
              ? {
                  clipPath: 'inset(0 100% 0 0)',
                }
              : {
                  clipPath: 'inset(0 100% 0 0)',
                }
          }
          transition={{
            duration:
              animationPhase === 'scanning'
                ? scanDuration
                : animationPhase === 'returning'
                ? scanDuration * 0.8
                : 0,
            ease: 'easeInOut',
          }}
        >
          <img
            src={afterImage}
            alt={t('afterProcessingAlt')}
            className="object-cover w-full h-full"
          />
        </motion.div>

        {/* 扫描线 */}
        <motion.div
          key={`scanline-${animationKey}`}
          className="absolute top-0 bottom-0 w-1 bg-gradient-to-b from-cyan-400 via-blue-500 to-purple-600 shadow-lg z-10"
          style={{
            boxShadow:
              '0 0 20px rgba(59, 130, 246, 0.8), 0 0 40px rgba(59, 130, 246, 0.4)',
            filter: 'drop-shadow(0 0 10px rgba(59, 130, 246, 0.6))',
          }}
          initial={{
            left: '0%',
          }}
          animate={
            animationPhase === 'scanning'
              ? {
                  left: '100%',
                }
              : animationPhase === 'returning'
              ? {
                  left: '0%',
                }
              : {
                  left: '0%',
                }
          }
          transition={{
            duration:
              animationPhase === 'scanning'
                ? scanDuration
                : animationPhase === 'returning'
                ? scanDuration * 0.8
                : 0,
            ease: 'easeInOut',
          }}
        >
          {/* 扫描线光效 */}
          <div className="absolute inset-0 bg-white/50 animate-pulse" />

          {/* 扫描线两端的装饰 */}
          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-cyan-400 rounded-full shadow-lg" />
          <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-purple-600 rounded-full shadow-lg" />
        </motion.div>

        {/* 扫描光效 */}
        {(animationPhase === 'scanning' || animationPhase === 'returning') && (
          <motion.div
            key={`glow-${animationKey}`}
            className="absolute top-0 bottom-0 w-8 bg-gradient-to-r from-transparent via-blue-400/30 to-transparent z-5"
            style={{
              transform: 'translateX(-50%)',
            }}
            initial={{
              left: '0%',
            }}
            animate={
              animationPhase === 'scanning'
                ? {
                    left: '100%',
                  }
                : animationPhase === 'returning'
                ? {
                    left: '0%',
                  }
                : {
                    left: '0%',
                  }
            }
            transition={{
              duration:
                animationPhase === 'scanning'
                  ? scanDuration
                  : animationPhase === 'returning'
                  ? scanDuration * 0.8
                  : 0,
              ease: 'easeInOut',
            }}
          >
            {/* 额外的光晕效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent blur-sm" />
          </motion.div>
        )}

        {/* 状态标签 */}
        {/* <div className="absolute top-4 left-4 z-20">
          <motion.div
            className="px-3 py-1 rounded-full text-xs font-semibold bg-red-500/90 text-white backdrop-blur-sm"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            {t('beforeLabel')}
          </motion.div>
        </div> */}

        {/* <div className="absolute top-4 right-4 z-20">
          <motion.div
            className="px-3 py-1 rounded-full text-xs font-semibold bg-green-500/90 text-white backdrop-blur-sm"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            {t('afterLabel')}
          </motion.div>
        </div> */}

        {/* 悬停遮罩 */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-colors duration-300" />
      </div>

      {/* 扫描进度指示器 */}
      {(animationPhase === 'scanning' || animationPhase === 'returning') && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200/30 rounded-b-2xl overflow-hidden">
          <motion.div
            key={`progress-${animationKey}`}
            className="h-full bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600"
            initial={{ width: '0%' }}
            animate={
              animationPhase === 'scanning'
                ? { width: '100%' }
                : animationPhase === 'returning'
                ? { width: '0%' }
                : { width: '0%' }
            }
            transition={{
              duration:
                animationPhase === 'scanning'
                  ? scanDuration
                  : animationPhase === 'returning'
                  ? scanDuration * 0.8
                  : 0,
              ease: 'easeInOut',
            }}
          />
        </div>
      )}
    </div>
  )
}
