'use client'
import { motion } from 'framer-motion'
import { Link } from '@i18n/routing'
import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import WatermarkRemovalDemo from './WatermarkRemovalDemo'

export default function Hero({ link = '' }) {
  const t = useTranslations('aiRemoveWatermark')
  const [particles, setParticles] = useState<
    Array<{ left: string; top: string; delay: number }>
  >([])

  useEffect(() => {
    // 只在客户端生成粒子位置
    const particleData = Array.from({ length: 50 }, (_, i) => ({
      left: `${Math.random() * 100}%`,
      top: `${Math.random() * 100}%`,
      delay: Math.random() * 2,
    }))
    setParticles(particleData)
  }, [])

  return (
    <section className="relative w-full min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
      {/* 动态背景效果 */}
      <div className="absolute inset-0">
        {/* 渐变背景 */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-pink-900/30 to-purple-900/20"></div>

        {/* 动态粒子效果 */}
        <div className="absolute inset-0">
          {particles.map((particle, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-purple-400/30 rounded-full"
              style={{
                left: particle.left,
                top: particle.top,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.3, 1, 0.3],
              }}
              transition={{
                duration: 3 + (i % 3),
                repeat: Infinity,
                delay: particle.delay,
              }}
            />
          ))}
        </div>

        {/* 网格背景 */}
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `linear-gradient(rgba(56, 189, 248, 0.1) 1px, transparent 1px),
                             linear-gradient(90deg, rgba(56, 189, 248, 0.1) 1px, transparent 1px)`,
            backgroundSize: '50px 50px',
          }}
        />
      </div>

      <div className="relative z-10 w-full px-4 py-16 pt-32 md:px-6 lg:px-8">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-6"
          >
            <span className="inline-block px-4 py-2 bg-pink-500/20 text-pink-300 text-sm font-semibold rounded-full border border-pink-500/30 mb-6">
              {t('aiPoweredTechnology')}
            </span>
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
              {t('heroTitle')}
              <span className="block text-pink-500/80">
                {t('heroTitleHighlight')}
              </span>
            </h1>
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mx-auto max-w-3xl text-xl text-gray-300 mb-12 leading-relaxed"
          >
            {t('heroDescription')}
          </motion.p>

          {/* 特性标签 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            {[
              t('aiPowered'),
              t('lightningFast'),
              t('preciseRemoval'),
              t('mobileFriendly'),
            ].map((feature, index) => (
              <span
                key={index}
                className="px-4 py-2 bg-white/10 backdrop-blur text-white text-sm font-medium rounded-full border border-white/20"
              >
                {feature}
              </span>
            ))}
          </motion.div>
        </div>

        {/* 演示区域 */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.6 }}
          className="mb-16"
        >
          <WatermarkRemovalDemo
            link={link}
            beforeImage="/images/ai-remove-watermark/sample-before1.png"
            afterImage="/images/ai-remove-watermark/sample-after1.png"
            className="max-w-4xl mx-auto"
          />
        </motion.div>
      </div>
    </section>
  )
}
