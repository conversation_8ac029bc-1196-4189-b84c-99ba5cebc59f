'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import AnimatedImageComparison from './AnimatedImageComparison'

export default function UseCases({ link = '' }) {
  const t = useTranslations('aiRemoveWatermark')
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  // 根据SEO文档的8个Use Cases，使用统一的扫描线效果
  const useCases = [
    {
      title: t('useCase1Title'),
      description: t('useCase1Description'),
      cta: t('useCase1Cta'),
      icon: '👥',
      beforeImage: '/images/ai-remove-watermark/travel-before-comp.png',
      afterImage: '/images/ai-remove-watermark/travel-after.png',
      gradient: 'from-blue-500 to-cyan-500',
    },
    {
      title: t('useCase2Title'),
      description: t('useCase2Description'),
      cta: t('useCase2Cta'),
      icon: '📱',
      beforeImage: '/images/ai-remove-watermark/tk-before-comp.png',
      afterImage: '/images/ai-remove-watermark/tk-after.png',
      gradient: 'from-pink-500 to-purple-500',
    },
    {
      title: t('useCase3Title'),
      description: t('useCase3Description'),
      cta: t('useCase3Cta'),
      icon: '📄',
      beforeImage: '/images/ai-remove-watermark/write-before-comp.png',
      afterImage: '/images/ai-remove-watermark/write-after.png',
      gradient: 'from-green-500 to-emerald-500',
    },
    {
      title: t('useCase4Title'),
      description: t('useCase4Description'),
      cta: t('useCase4Cta'),
      icon: '🛍️',
      beforeImage: '/images/ai-remove-watermark/model-before.png',
      afterImage: '/images/ai-remove-watermark/model-after.png',
      gradient: 'from-orange-500 to-red-500',
    },
    {
      title: t('useCase5Title'),
      description: t('useCase5Description'),
      cta: t('useCase5Cta'),
      icon: '📸',
      beforeImage: '/images/ai-remove-watermark/art-before-comp.png',
      afterImage: '/images/ai-remove-watermark/art-after.png',
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      title: t('useCase6Title'),
      description: t('useCase6Description'),
      cta: t('useCase6Cta'),
      icon: '✂️',
      beforeImage: '/images/ai-remove-watermark/human-before.png',
      afterImage: '/images/ai-remove-watermark/human-after.png',
      gradient: 'from-cyan-500 to-blue-500',
    },
    {
      title: t('useCase7Title'),
      description: t('useCase7Description'),
      cta: t('useCase7Cta'),
      icon: '👗',
      beforeImage: '/images/ai-remove-watermark/logo-before-comp.png',
      afterImage: '/images/ai-remove-watermark/logo-after.png',
      gradient: 'from-indigo-500 to-purple-500',
    },
    {
      title: t('useCase8Title'),
      description: t('useCase8Description'),
      cta: t('useCase8Cta'),
      icon: '🎨',
      beforeImage: '/images/ai-remove-watermark/color-before.png',
      afterImage: '/images/ai-remove-watermark/color-after.png',
      gradient: 'from-teal-500 to-cyan-500',
    },
  ]

  return (
    <section className="w-full py-24 bg-gradient-to-b from-slate-900 to-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 标题部分 */}
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            <span className="text-purple-400">{t('useCasesTitle')}</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            {t('useCasesDescription')}
          </p>
        </motion.div>

        {/* Use Cases列表 - 左图右内容布局 */}
        <div className="space-y-24">
          {useCases.map((useCase, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              className={`flex flex-col lg:flex-row items-center gap-12 ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* 左侧图片区域 */}
              <div className="flex-1 w-full">
                <div className="relative">
                  <AnimatedImageComparison
                    beforeImage={useCase.beforeImage}
                    afterImage={useCase.afterImage}
                    className="w-full"
                    autoPlay={true}
                    scanDuration={2.5}
                    pauseDuration={1.5 + index * 0.5}
                  />

                  {/* 图标装饰 */}
                  <div
                    className={`absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-r ${useCase.gradient} rounded-2xl flex items-center justify-center text-2xl shadow-2xl z-10`}
                  >
                    {useCase.icon}
                  </div>
                </div>
              </div>

              {/* 右侧内容区域 */}
              <div className="flex-1 w-full">
                <div className="max-w-xl">
                  <motion.h3
                    className="text-3xl md:text-4xl font-bold text-white mb-6 leading-tight"
                    initial={{ opacity: 0, x: index % 2 === 1 ? 50 : -50 }}
                    animate={inView ? { opacity: 1, x: 0 } : {}}
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.3 }}
                  >
                    {useCase.title}
                  </motion.h3>

                  <motion.p
                    className="text-lg text-gray-300 mb-8 leading-relaxed"
                    initial={{ opacity: 0, x: index % 2 === 1 ? 50 : -50 }}
                    animate={inView ? { opacity: 1, x: 0 } : {}}
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.4 }}
                  >
                    {useCase.description}
                  </motion.p>

                  {/* CTA按钮 */}
                  <motion.div
                    initial={{ opacity: 0, x: index % 2 === 1 ? 50 : -50 }}
                    animate={inView ? { opacity: 1, x: 0 } : {}}
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.5 }}
                  >
                    <Link href={link} prefetch={false}>
                      <motion.button
                        className={`px-8 py-4 bg-gradient-to-r ${useCase.gradient} text-white font-semibold rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 group`}
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <span className="flex items-center gap-2">
                          {useCase.cta}
                          <svg
                            className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17 8l4 4m0 0l-4 4m4-4H3"
                            />
                          </svg>
                        </span>
                      </motion.button>
                    </Link>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部 CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1.2 }}
          className="mt-32 text-center"
        >
          <div className="relative bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-500/20 rounded-3xl p-12 overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5"></div>
            <div className="absolute top-0 left-1/4 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 right-1/4 w-32 h-32 bg-pink-500/10 rounded-full blur-3xl"></div>

            <div className="relative z-10">
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                {t('readyToTransformTitle')}
              </h3>
              <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                {t('readyToTransformDescription')}
              </p>

              <Link href={link} prefetch={false}>
                <motion.button
                  className="px-12 py-5 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold text-xl rounded-2xl shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 group relative overflow-hidden"
                  whileHover={{ scale: 1.05, y: -3 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {/* 按钮背景动画 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  <span className="relative flex items-center gap-3">
                    {t('startRemovingButton')}
                    <svg
                      className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                      />
                    </svg>
                  </span>
                </motion.button>
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
