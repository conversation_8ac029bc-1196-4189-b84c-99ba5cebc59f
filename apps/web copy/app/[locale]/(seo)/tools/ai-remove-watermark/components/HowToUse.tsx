'use client'
import { motion, AnimatePresence } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Link } from '@i18n/routing'
import { useState, useRef, useEffect } from 'react'
import { useTranslations } from 'next-intl'

export default function HowToUse({ link = '' }: { link?: string }) {
  const t = useTranslations('aiRemoveWatermark')

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })
  const [dragActive, setDragActive] = useState(false)
  const dropRef = useRef(null)

  // 动画轮播状态
  const [currentScreen, setCurrentScreen] = useState(0) // 0: 拖拽区域, 1: 处理中, 2: 完成
  const [progress, setProgress] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const isPausedRef = useRef(false)

  // 同步isPaused状态到ref
  useEffect(() => {
    isPausedRef.current = isPaused
  }, [isPaused])

  // 动画循环逻辑
  useEffect(() => {
    if (!inView) return

    let isRunning = true

    const sleep = (ms: number) =>
      new Promise((resolve) => setTimeout(resolve, ms))

    const animationCycle = async () => {
      if (!isRunning) return

      // 第一屏：拖拽区域 (3秒)
      setCurrentScreen(0)
      setProgress(0)
      await sleep(3000)

      if (!isRunning) return

      // 第二屏：处理中 (4秒)
      setCurrentScreen(1)

      // 模拟进度条增长
      for (let i = 0; i <= 100 && isRunning; i += 2) {
        setProgress(i)
        await sleep(40)
      }

      if (!isRunning) return
      await sleep(500)

      // 第三屏：完成 (3秒，可暂停)
      setCurrentScreen(2)

      // 等待3秒，但如果暂停则等待恢复
      let elapsedTime = 0
      const totalTime = 3000

      while (elapsedTime < totalTime && isRunning) {
        await sleep(100)

        // 如果没有暂停，则增加已过时间
        if (!isPausedRef.current) {
          elapsedTime += 100
        }
        // 暂停时不增加时间，但继续循环检查状态
      }
    }

    const runCycle = async () => {
      while (isRunning) {
        await animationCycle()
      }
    }

    runCycle()

    // 清理函数
    return () => {
      isRunning = false
    }
  }, [inView])

  const steps = [
    {
      number: t('step1Number'),
      title: t('step1Title'),
      description: t('step1Description'),
      icon: t('step1Icon'),
      color: 'from-blue-500 to-cyan-500',
    },
    {
      number: t('step2Number'),
      title: t('step2Title'),
      description: t('step2Description'),
      icon: t('step2Icon'),
      color: 'from-purple-500 to-pink-500',
    },
  ].filter((step) => step.title) // 过滤掉空的步骤

  // 拖拽事件处理
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(true)
  }
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
  }
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(true)
  }
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    // 可在此处处理文件上传逻辑
    window.location.href = link
  }

  return (
    <section className="w-full py-24 bg-gradient-to-b from-gray-900 to-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-pink-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('howToTitle')}
            {t('howToSubtitle') && (
              <span className="block text-purple-400">
                {t('howToSubtitle')}
              </span>
            )}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            {t('howToDescription')}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* 步骤列表 */}
          <div className="space-y-8">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -30 }}
                animate={inView ? { opacity: 1, x: 0 } : {}}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="group flex items-start space-x-6"
              >
                {/* 步骤编号 */}
                <div
                  className={`flex-shrink-0 w-16 h-16 bg-gradient-to-r ${step.color} rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  {step.number}
                </div>

                {/* 步骤内容 */}
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <span className="text-2xl">{step.icon}</span>
                    <h3 className="text-xl font-bold text-white group-hover:text-cyan-300 transition-colors duration-300">
                      {step.title}
                    </h3>
                  </div>
                  <p className="text-gray-300 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* 动态可视化演示 */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative"
          >
            <div className="relative bg-white/5 backdrop-blur border border-white/10 rounded-3xl p-8 shadow-2xl overflow-hidden">
              <AnimatePresence mode="wait">
                {/* 第一屏：拖拽区域 */}
                {currentScreen === 0 && (
                  <motion.div
                    key="screen-0"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                    className="space-y-6"
                  >
                    <div
                      ref={dropRef}
                      className={`border-2 border-dashed rounded-2xl p-12 text-center bg-cyan-400/5 transition-all duration-300 ${
                        dragActive
                          ? 'border-purple-500 bg-purple-500/10 scale-105 shadow-2xl'
                          : 'border-cyan-400/50'
                      }`}
                      onDragEnter={handleDragEnter}
                      onDragLeave={handleDragLeave}
                      onDragOver={handleDragOver}
                      onDrop={handleDrop}
                    >
                      <motion.div
                        className="text-6xl mb-6"
                        animate={{
                          y: [0, -10, 0],
                          rotate: [0, 5, -5, 0],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: 'easeInOut',
                        }}
                      >
                        📤
                      </motion.div>
                      <p className="text-white font-bold text-xl mb-2">
                        {t('dropImageHere')}
                      </p>
                      <p className="text-gray-400">{t('orClickToBrowse')}</p>

                      {/* 动态虚线边框动画 */}
                      <motion.div
                        className="absolute inset-0 border-2 border-dashed border-purple-400/30 rounded-2xl"
                        animate={{
                          borderColor: [
                            'rgba(168, 85, 247, 0.3)',
                            'rgba(168, 85, 247, 0.6)',
                            'rgba(168, 85, 247, 0.3)',
                          ],
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: 'easeInOut',
                        }}
                      />
                    </div>
                  </motion.div>
                )}

                {/* 第二屏：处理中 */}
                {currentScreen === 1 && (
                  <motion.div
                    key="screen-1"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.5 }}
                    className="space-y-6"
                  >
                    {/* 图片移入动画 */}
                    <div className="relative h-48 bg-gray-800/50 rounded-2xl overflow-hidden">
                      <motion.img
                        src="/images/ai-remove-watermark/cat-before.png"
                        alt="Processing image"
                        className="w-full h-full object-cover"
                        initial={{ x: -300, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ duration: 1, ease: 'easeOut' }}
                      />

                      {/* 扫描线效果 */}
                      <motion.div
                        className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-cyan-400 to-transparent"
                        animate={{ y: [0, 192, 0] }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: 'linear',
                        }}
                      />
                    </div>

                    {/* 进度条 */}
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm text-gray-300">
                        <span>{t('processing')}</span>
                        <span>{progress}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-3 overflow-hidden">
                        <motion.div
                          className="bg-gradient-to-r from-purple-400 to-pink-500 h-full rounded-full relative"
                          style={{ width: `${progress}%` }}
                        >
                          {/* 进度条光效 */}
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                            animate={{ x: ['-100%', '100%'] }}
                            transition={{
                              duration: 1,
                              repeat: Infinity,
                              ease: 'linear',
                            }}
                          />
                        </motion.div>
                      </div>
                    </div>

                    {/* AI处理指示器 */}
                    <div className="flex items-center justify-center space-x-3 text-cyan-400">
                      <motion.div
                        className="w-2 h-2 bg-cyan-400 rounded-full"
                        animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
                        transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-cyan-400 rounded-full"
                        animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          delay: 0.2,
                        }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-cyan-400 rounded-full"
                        animate={{ scale: [1, 1.5, 1], opacity: [1, 0.5, 1] }}
                        transition={{
                          duration: 1,
                          repeat: Infinity,
                          delay: 0.4,
                        }}
                      />
                      <span className="text-sm font-medium">
                        AI {t('processing')}
                      </span>
                    </div>
                  </motion.div>
                )}

                {/* 第三屏：完成 */}
                {currentScreen === 2 && (
                  <motion.div
                    key="screen-2"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 1.2 }}
                    transition={{ duration: 0.5 }}
                    className="space-y-6"
                    onMouseEnter={() => {
                      setIsPaused(true)
                    }}
                    onMouseLeave={() => {
                      setIsPaused(false)
                    }}
                  >
                    {/* 闪烁特效 */}
                    <motion.div
                      className="absolute inset-0 pointer-events-none bg-gradient-to-r from-yellow-400/20 via-white/40 to-yellow-400/20 rounded-3xl"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: [0, 1, 0] }}
                      transition={{ duration: 0.5, repeat: 3 }}
                    />

                    {/* 处理后的图片 */}
                    <div className="relative h-48 bg-gray-800/50 rounded-2xl overflow-hidden">
                      <motion.img
                        src="/images/ai-remove-watermark/cat-after.png"
                        alt="Processed image"
                        className="w-full h-full object-cover"
                        initial={{ scale: 1.2, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 0.8, ease: 'easeOut' }}
                      />

                      {/* 成功光环 */}
                      <motion.div
                        className="absolute inset-0 border-4 border-green-400/50 rounded-2xl"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.3 }}
                      />
                    </div>

                    {/* 成功指示器 */}
                    <motion.div
                      className="flex flex-col items-center justify-center space-y-2 text-green-400"
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.5 }}
                    >
                      <div className="flex items-center space-x-3">
                        <motion.div
                          className="text-3xl"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 0.5, delay: 0.7 }}
                        >
                          ✨
                        </motion.div>
                        <span className="text-lg font-bold">
                          {/* 完成！水印已移除 */}
                        </span>
                      </div>
                    </motion.div>

                    {/* 立即体验按钮 */}
                    {link && (
                      <motion.div
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ duration: 0.5, delay: 0.8 }}
                        onMouseEnter={(e) => e.stopPropagation()}
                        onMouseLeave={(e) => e.stopPropagation()}
                      >
                        <Link
                          href={link}
                          prefetch={false}
                          className="block z-10"
                        >
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="w-full py-4 bg-gradient-to-r from-green-500 to-emerald-500 hover:opacity-90 transition-all text-white font-bold rounded-2xl shadow-lg hover:shadow-xl cursor-pointer text-center"
                            onClick={(e) => {
                              e.stopPropagation()
                            }}
                          >
                            {t('ctaPrimaryButton')}
                          </motion.div>
                        </Link>
                      </motion.div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>

              {/* 屏幕指示器 */}
              <div className="absolute top-4 right-4 flex flex-col items-end space-y-2">
                <div className="flex space-x-2">
                  {[0, 1, 2].map((screen) => (
                    <motion.div
                      key={screen}
                      className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                        currentScreen === screen
                          ? isPaused && screen === 2
                            ? 'bg-orange-400'
                            : 'bg-purple-400'
                          : 'bg-gray-600'
                      }`}
                      animate={{
                        scale: currentScreen === screen ? 1.2 : 1,
                        opacity: currentScreen === screen ? 1 : 0.5,
                      }}
                      transition={{ duration: 0.3 }}
                    />
                  ))}
                </div>
              </div>

              {/* 装饰性光效 */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-cyan-400 rounded-full blur-lg opacity-60 animate-pulse"></div>
              <div
                className="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-400 rounded-full blur-lg opacity-60 animate-pulse"
                style={{ animationDelay: '1s' }}
              ></div>
            </div>
          </motion.div>
        </div>

        {/* 底部提示 */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1 }}
          className="mt-16 text-center"
        >
          <div className="inline-flex items-center space-x-2 px-6 py-3 bg-green-500/20 border border-green-500/30 rounded-full text-green-300">
            <span className="text-lg">✅</span>
            <span className="font-semibold">{t('noSignupFreeInstant')}</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
