'use client'

import { useInView } from 'react-intersection-observer'
import { type ReactNode } from 'react'

interface AnimatedCardProps {
  children: ReactNode
  delay?: number
  className?: string
}

export default function AnimatedCard({ 
  children, 
  delay = 0, 
  className = '' 
}: AnimatedCardProps) {
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  })

  return (
    <div
      ref={ref}
      className={`transform transition-all duration-700 ${
        inView 
          ? 'translate-y-0 opacity-100' 
          : 'translate-y-10 opacity-0'
      } ${className}`}
      style={{
        transitionDelay: inView ? `${delay}ms` : '0ms',
      }}
    >
      <div className="relative group h-full">
        <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-50 transition duration-300" />
        <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 lg:p-8 h-full">
          {children}
        </div>
      </div>
    </div>
  )
}