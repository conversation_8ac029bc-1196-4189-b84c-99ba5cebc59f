'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimatedCard'

interface FAQSectionProps {
  toolUrl: string
}

export default function FAQSection({ toolUrl }: FAQSectionProps) {
  const [openFAQ, setOpenFAQ] = useState<number | null>(0)
  const t = useTranslations('aiArtGeneratorFree')

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-4xl mx-auto">
        {/* Section Header - H2 with exact SEO content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('faqTitle')}</span>
          </h2>
          <p className="text-lg text-white/80">{t('faqDescription')}</p>
        </div>

        {/* FAQ List */}
        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div className="border border-white/10 rounded-2xl overflow-hidden">
                <button
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200"
                >
                  <h3 className="text-lg md:text-xl font-semibold text-white pr-4">
                    {faq.question}
                  </h3>
                  <div
                    className={`transform transition-transform duration-200 ${
                      openFAQ === index ? 'rotate-180' : ''
                    }`}
                  >
                    <i className="fas fa-chevron-down text-purple-400" />
                  </div>
                </button>

                <div
                  className={`overflow-hidden transition-all duration-300 ${
                    openFAQ === index
                      ? 'max-h-96 opacity-100'
                      : 'max-h-0 opacity-0'
                  }`}
                >
                  <div className="px-6 pb-4">
                    <p className="text-white/80 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Additional Help Section */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-4 px-8 py-4 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-2xl">
            <i className="fas fa-question-circle text-purple-400 text-xl" />
            <span className="text-white font-medium">
              {t('faqSupportText')}
            </span>
            <button className="text-purple-400 hover:text-purple-300 font-semibold">
              {t('faqSupportButton')}
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}
