import { cn } from '@ui/lib'
import { type ReactNode } from 'react'
import { Link } from '@i18n/routing'

interface CTAButtonProps {
  children: ReactNode
  variant?: 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  href?: string
  onClick?: () => void
}

export default function CTAButton({
  children,
  variant = 'primary',
  size = 'md',
  className,
  href,
  onClick,
}: CTAButtonProps) {
  const baseClasses = cn(
    'inline-flex items-center justify-center font-semibold rounded-full transition-all duration-300 hover:scale-105 active:scale-95',
    {
      'px-4 py-2 text-sm': size === 'sm',
      'px-6 py-3 text-base': size === 'md',
      'px-8 py-4 text-lg': size === 'lg',
    },
    {
      'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl':
        variant === 'primary',
      'bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20':
        variant === 'secondary',
    },
    className
  )

  if (href) {
    return (
      <Link href={href} className={baseClasses}>
        {children}
      </Link>
    )
  }

  return (
    <button onClick={onClick} className={baseClasses}>
      {children}
    </button>
  )
}
