'use client'

import { UserContextProvider } from '@saas/auth/lib/user-context'
import type { PropsWithChildren } from 'react'
import { Toaster } from 'react-hot-toast'

export default function ToolsLayout({ children }: PropsWithChildren) {
  return (
    <UserContextProvider initialUser={null}>
      <Toaster
        position="top-center"
        reverseOrder={false}
        toastOptions={{
          duration: 2000,
          style: {
            background: '#333',
            color: '#fff',
          },
        }}
      />
      <main className="min-h-screen">{children}</main>
    </UserContextProvider>
  )
}
