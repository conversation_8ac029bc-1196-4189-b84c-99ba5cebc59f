import { Metadata } from 'next'
import React from 'react'
import Hero from './components/Hero'
import HowToUse from './components/HowToUse'
import UseCases from './components/UseCases'
import WhyChooseUs from './components/WhyChooseUs'
import Features from './components/Features'
import Testimonials from './components/Testimonials'
import FAQ from './components/FAQ'
import CTA from './components/CTA'
import BackGroundHighlight from './components/BackGroundHighlight'
import './index.css'

const title = 'Free AI Background Remover – Remove Image Backgrounds Online'
const description =
  "Use IMGGen's AI background remover to erase photo backgrounds online. Try free with credits, download transparent images, and clean up logos or signatures."
const url = 'https://imggen.ai/ai-image-background-remover'
const ogImage = 'https://imggen.ai/images/og-background-remover.jpg'
const twitterImage = 'https://imggen.ai/images/twitter-background-remover.jpg'

export const metadata: Metadata = {
  title,
  description,
  keywords: [
    'image background remover',
    'background remover free',
    'ai background remover',
    'transparent background maker',
    'remove white from image',
    'logo background remover',
    'signature background remover',
    'free background remover tool online',
    'background eraser',
  ],
  authors: [{ name: 'ImgGen Team' }],
  openGraph: {
    type: 'website',
    url,
    title,
    description,
    siteName: 'ImgGen',
    images: [
      {
        url: ogImage,
        width: 1200,
        height: 630,
        alt: 'AI Background Remover - Remove Image Backgrounds Online',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title,
    description,
    images: [twitterImage],
    creator: '@imggen_ai',
    site: '@imggen_ai',
  },
}
const link = 'ai/ai-image-background-remover'
export default function AIBackgroundRemoverPage() {
  return (
    <main className="flex min-h-screen flex-col bg-purple-950/60 items-center justify-between">
      <BackGroundHighlight/>
      <Hero link={link}/>
      <HowToUse />
      <UseCases />
      <Features link={link}/>
      <WhyChooseUs />
      <Testimonials />
      <FAQ link={link}/>
      <CTA link={link}/>
    </main>
  )
}
