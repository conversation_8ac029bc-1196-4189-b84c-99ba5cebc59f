'use client'
import { Link } from '@i18n/routing'
import BeforeAfterSlider from './BeforeAfterSlider'

export default function Hero({ link = '' }) {
  return (
    <section className="w-full relative h-screen bg-gradient-to-br from-purple-800/9 via-purple-700/30 to-pink-900 px-4 py-16 md:px-6 lg:px-8">
      {/* <div className="absolute inset-0 bg-gradient-to-t from-purple-950/20 to-transparent pointer-events-none"></div> */}
      <div className="mx-auto max-w-7xl text-center">
        <h1 className="mb-6 text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
          Free AI Background Remover Online
        </h1>
        <p className="mx-auto mb-8 max-w-2xl text-lg text-white/30">
          Erase image backgrounds in seconds. Clean, fast, and easy — perfect
          for any photo.
        </p>
        <BeforeAfterSlider
          className="mb-8 w-full max-w-3xl mx-auto"
          beforeImage="/images/girl-before.png"
          afterImage="/images/girl-after.png"
        />
        <div className="flex translate-x-0 mt-4  z-10 justify-center gap-4">
          <button className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-lg relative overflow-hidden">
            <Link href={link}>Start Background Removal Now</Link>
          </button>
        </div>
      </div>
    </section>
  )
}
