'use client'
import { useState } from 'react'
import BeforeAfterSlider from './BeforeAfterSlider'

export default function UseCases() {
  const [activeTab, setActiveTab] = useState('portraits')

  const cases = {
    portraits: {
      title: 'Portraits',
      before: '/images/portraits-before.png',
      after: '/images/portraits-after.png',
      beforeAlt: 'image of a little child-before the background is removed',
      afterAlt: 'image of a little child-after the background is removed',
    },
    products: {
      title: 'Products',
      before: '/images/product-before.png',
      after: '/images/product-after.png',
      beforeAlt: 'image of a bag-before the background is removed',
      afterAlt: 'image of a bag-after the background is removed',
    },
    animals: {
      title: 'Animals',
      before: '/images/animal-before.png',
      after: '/images/animal-after.png',
      beforeAlt: 'image of a little dog-before the background is removed',
      afterAlt: 'image of a little dog-after the background is removed',
    },
    cars: {
      title: 'Cars',
      before: '/images/car-before.png',
      after: '/images/car-after.png',
      beforeAlt: 'image of a car-before the background is removed',
      afterAlt: 'image of a car-after the background is removed',
    },
    graphics: {
      title: 'Graphics',
      before: '/images/graphics-before.png',
      after: '/images/graphics-after.png',
      beforeAlt: 'image of a graphic-before the background is removed',
      afterAlt: ' image of a graphic-after the background is removed',
    },
  }

  return (
    <section className="w-full bg-gradient-to-bl to-purple-700/30 via-purple-700/30 from-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <h2 className="mb-4 translate-x-0 text-center text-3xl font-bold text-white sm:text-4xl">
          See How Our AI Background Remover Transforms Your Images
        </h2>
        <p className="mx-auto translate-x-0 mb-12 max-w-3xl text-center text-lg text-white/30">
          Explore how IMGGen's background remover free tool transforms everyday
          photos into clean, professional visuals. From portraits to product
          shots and brand logos, our AI background remover delivers crisp
          cutouts and stunning transparent backgrounds — ready for use anywhere.
        </p>

        <div className="mb-8 flex flex-wrap justify-center gap-4">
          {Object.entries(cases).map(([key, value]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key)}
              className={`rounded-full translate-x-0 px-6 py-2 text-sm font-medium transition-all duration-75 ease-in ${
                activeTab === key
                  ? 'group px-8 py-4 bg-gradient-to-r border border-white/0 border-solid from-pink-500/80 to-purple-600/80 text-white font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20'
                  : 'group px-8 py-4 bg-transparent border border-white/15 border-solid text-white/70 font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20'
              }`}
            >
              {value.title}
            </button>
          ))}
        </div>
        <div className="mx-auto max-w-3xl">
          {Object.keys(cases).map((item) => {
            return (
              <div
                key={item}
                className={`${item === activeTab ? '' : '!hidden'}`}
              >
                <BeforeAfterSlider
                  beforeAlt={cases[activeTab as keyof typeof cases].beforeAlt}
                  afterAlt={cases[activeTab as keyof typeof cases].afterAlt}
                  beforeImage={cases[activeTab as keyof typeof cases].before}
                  afterImage={cases[activeTab as keyof typeof cases].after}
                />
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
