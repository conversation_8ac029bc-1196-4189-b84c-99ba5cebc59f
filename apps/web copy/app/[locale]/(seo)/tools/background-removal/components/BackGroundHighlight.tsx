'use client'

import { useEffect, useState } from 'react'
import { useWindowSize } from 'usehooks-ts'

export default function BackGroundHighlight({
  className,
}: {
  className?: string
}) {
  const colorOrder = [
    [0, 0],
    [0, 1],
    [1, 1],
    [1, 2],
    [3, 2],
    [4, 0],
  ]
  const colorArr = [
    'rgba(176, 135, 255, 0.7)',
    'rgba(33, 69, 255, 0.7)',
    'rgba(255, 208, 116, 0.7)',
    'rgba(23, 241, 209, 0.7)',
    '#02FF8A',
    'rgba(255, 255, 255)',
  ]
  // 获取用户窗口大小
  const { width, height } = useWindowSize()

  const [translate, setTranslate] = useState({ x: 0, y: 0 })
  const [isMounted, setIsMounted] = useState(false)
  const [color, setColor] = useState(0)

  const [zoom, setZoom] = useState(1)
  const [progress, setProgress] = useState(0)
  function handleScroll() {
    const scrollY = window.scrollY
    const windowHeight = window.innerHeight
    const documentHeight = document.documentElement.scrollHeight
    const percentage = (scrollY / (documentHeight - windowHeight)) * 100
    setProgress(percentage)
  }

  useEffect(() => {
    setIsMounted(true)
    window.addEventListener('scroll', handleScroll)
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  useEffect(() => {
    let isMounted = true
    const handleWindowMouseMove = (event: MouseEvent) => {
      setTranslate({
        x: event.clientX - 0.5 * width,
        y: event.clientY - 0.5 * height,
      })
    }
    const handleWindowScroll = () => {
      let scrollTop = document.documentElement.scrollTop //滚动条滚动高度
      let scrollHeight = document.documentElement.scrollHeight //滚动内容高度
      let scrollRatio = scrollTop / (scrollHeight - height)
      const index = Math.floor(scrollRatio * colorOrder.length)
      if (index >= colorOrder.length) {
        setColor(colorOrder.length)
      } else {
        setColor(index)
      }
    }

    let colorTimer = null
    if (isMounted) {
      colorTimer = setInterval(() => {
        setColor(color - 1 <= 0 ? colorOrder.length - 1 : color - 1)
      }, 5000)
    }

    let zoomTimer = null
    if (isMounted) {
      zoomTimer = setInterval(() => {
        setZoom(Math.floor(Math.random() * (13 - 10 + 1) + 10) * 0.1)
      }, 1000)
    }

    window.addEventListener('mousemove', handleWindowMouseMove)
    window.addEventListener('scroll', handleWindowScroll)
    return () => {
      isMounted = false
      window.removeEventListener('mousemove', handleWindowMouseMove)
      window.addEventListener('scroll', handleWindowScroll)
      clearInterval(colorTimer)
      clearInterval(zoomTimer)
    }
  }, [width, height])
  
  if (!isMounted) return null
  return (
    <div
      style={{
        // display: progress > 99 ? 'none' : '',
        opacity: progress > 70 ? '0.5' : '0.8',
      }}
      className={`fixed z-[0] pointer-events-none left-0 right-0 top-0 bottom-0 ${
        className || ''
      }`}
    >
      <div className="md:not-sr-only sr-only md:flex md:w-full md:h-full justify-center items-center absolute left-0 top-0">
        <div
          className="w-[400px] h-[400px] rounded-full overflow-hidden absolute"
          style={{
            transform: `translate(${translate.x}px, ${translate.y}px) scaleX(${zoom})`,
            transition: 'all 1s ease-out',
          }}
        >
          {colorOrder.map((order, index) => (
            <div
              key={index}
              className="absolute w-full h-full"
              style={{
                opacity: color == index ? '1' : '0',
                backgroundImage: `linear-gradient(135deg, ${
                  colorArr[order[0]]
                }, ${colorArr[order[1]]}`,
                transition: 'opacity 0.7s linear',
                mask: 'radial-gradient(closest-side circle, #000 0%, transparent 100%)',
              }}
            ></div>
          ))}
        </div>
      </div>
    </div>
  )
}
