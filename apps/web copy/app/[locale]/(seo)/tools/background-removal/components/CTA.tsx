'use client'
import { Link } from '@i18n/routing'
import BeforeAfterSlider from './BeforeAfterSlider'

export default function CTA({ link = '' }) {
  return (
    <section className="w-full bg-gradient-to-br from-purple-700/30 via-purple-700/30 to-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-purple-800/40 via-purple-700/40 to-pink-900/40 backdrop-blur-sm">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10"></div>
          <div className="absolute -top-40 -right-40 h-80 w-80 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-600/20 blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 h-80 w-80 rounded-full bg-gradient-to-br from-purple-500/20 to-pink-600/20 blur-3xl"></div>

          <div className="relative grid gap-12 p-8 md:p-12 lg:grid-cols-2 lg:gap-16 max-md:grid-cols-1">
            {/* Left side - Content */}
            <div className="flex flex-col justify-center space-y-6">
              <div className="space-y-4">
                <div className="inline-flex items-center gap-2 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-600/20 px-4 py-2 backdrop-blur-sm">
                  <div className="h-2 w-2 animate-pulse rounded-full bg-green-400"></div>
                  <span className="text-sm font-medium text-white">
                    Free Credits Available
                  </span>
                </div>

                <h2 className="text-3xl font-bold text-white sm:text-4xl lg:text-5xl max-md:text-xl max-md:flex max-md:flex-wrap max-md:break-words max-md:max-w-full">
                  Try the Best Free AI Background Remover Now
                </h2>

                <p className="text-lg text-white/80 leading-relaxed">
                  Start now with free credits to try our AI background remover —
                  cleanly erase photo backgrounds, download transparent images,
                  and even remove white from logos or signatures. It's the
                  easiest way to create polished visuals without any design
                  tools or editing experience.
                </p>
              </div>

              {/* Features list */}
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500/20">
                    <svg
                      className="h-4 w-4 text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <span className="text-white/80">
                    One-click background removal
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500/20">
                    <svg
                      className="h-4 w-4 text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <span className="text-white/80">
                    High-quality transparent PNGs
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex h-6 w-6 items-center justify-center rounded-full bg-green-500/20">
                    <svg
                      className="h-4 w-4 text-green-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <span className="text-white/80">
                    No watermarks or sign-up required
                  </span>
                </div>
              </div>

              {/* CTA Button */}
              <div className="pt-4">
                <Link href={link}>
                  <button className="group max-md:whitespace-nowrap max-md:mx-auto max-md:px-4 relative overflow-hidden rounded-full bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-4 font-semibold text-white transition-all duration-300 hover:opacity-90 hover:shadow-2xl hover:shadow-purple-500/25 hover:scale-105">
                    <span className="relative z-10 flex items-center gap-2">
                      <svg
                        className="h-5 w-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                      Removing Backgrounds for Free
                    </span>
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] transition-transform duration-700 group-hover:translate-x-[100%]"></div>
                  </button>
                </Link>
              </div>
            </div>

            {/* Right side - Image showcase */}
            <div className="relative flex items-center justify-center">
              <div className="relative">
                {/* Main showcase image */}
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-white/10 to-white/5 p-4 backdrop-blur-sm">
                  <div className="aspect-square w-full max-w-md overflow-hidden rounded-xl bg-gradient-to-br from-blue-100 to-purple-100">
                    <BeforeAfterSlider
                      hideBlur
                      className="!h-full !w-full object-cover !rounded-2xl !overflow-hidden"
                      containerClassName="!h-full !w-full min-w-[400px] max-md:min-w-[300px] object-cover !rounded-2xl"
                      beforeImage="/images/woman-cat-before.png"
                      afterImage="/images/woman-cat-after.png"
                      beforeAlt="image of a woman with a cat-before the background is removed"
                      afterAlt="image of a woman with a cat-after the background is removed"
                    />
                    {/* <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
                      alt="AI Background Removal Demo"
                      className="h-full w-full object-cover"
                    /> */}
                  </div>
                  {/* Processing indicator */}
                  <div className="absolute top-6 left-6 flex items-center gap-2 rounded-full bg-gradient-to-r from-green-500 to-blue-500 px-3 py-1 text-sm font-medium text-white">
                    <div className="h-2 w-2 animate-pulse rounded-full bg-white"></div>
                    Processing...
                  </div>
                </div>

                {/* Floating elements */}
                <div className="absolute -top-6 -right-6 animate-bounce">
                  <div className="rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 p-3">
                    <svg
                      className="h-6 w-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                  </div>
                </div>

                <div className="absolute -bottom-4 -left-4 animate-pulse">
                  <div className="rounded-full bg-gradient-to-r from-pink-500 to-purple-500 p-3">
                    <svg
                      className="h-6 w-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                </div>

                {/* Stats floating cards */}
                <div className="absolute -top-8 left-1/2 -translate-x-1/2 animate-float">
                  <div className="rounded-lg bg-gradient-to-r from-blue-500/20 to-purple-600/20 backdrop-blur-sm border border-white/20 px-4 py-2">
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">2.3s</div>
                      <div className="text-xs text-white/70">
                        Processing Time
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  className="absolute -bottom-8 right-1/4 animate-float"
                  style={{ animationDelay: '1s' }}
                >
                  <div className="rounded-lg bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm border border-white/20 px-4 py-2">
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">4K</div>
                      <div className="text-xs text-white/70">Resolution</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </section>
  )
}
