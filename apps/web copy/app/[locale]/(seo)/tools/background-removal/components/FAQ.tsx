'use client'
import { Link } from '@i18n/routing'
import { useState } from 'react'

export default function FAQ({ link = '' }) {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const faqs = [
    {
      question: 'How do I remove a background from an image?',
      answer:
        'Just upload your photo to IMGGen, click "Remove Background," and let our AI background remover do the work in seconds.',
      icon: '🖼️',
    },
    {
      question: 'Is this background remover really free to use?',
      answer:
        'Yes! When you start using IMGGen, we give you free credits to try our background remover free. Once your credits run out, you can recharge to unlock more uses and access premium features.',
      icon: '💰',
    },
    {
      question: 'Can I remove white or a solid color from an image?',
      answer:
        'Absolutely. IMGGen works as a color remover too, so you can remove white from image files or erase any solid background color.',
      icon: '🎨',
    },
    {
      question: 'Does it work for logos or handwritten signatures?',
      answer:
        'Yes. Use it as a logo background remover or signature background remover to get clean, transparent PNGs for your brand assets.',
      icon: '✍️',
    },
    {
      question: 'Can I replace the background with something new?',
      answer:
        'After using the image background remover, you can keep it transparent or use our AI backgrounds to add something fresh.',
      icon: '🔄',
    },
    {
      question: 'Can I remove specific colors instead of the full background?',
      answer:
        "Yes. Our color remover tool lets you erase solid colors like white, green, or blue with just one click. Whether you're using it as a color remover from image for product photos or trying to remove colour from image for cleaner presentations, IMGGen makes it easy. It's also great as a color eraser or to erase color in image files before adding new backgrounds for photos.",
      icon: '🌈',
    },
  ]

  const toggleFAQ = (index: number | null) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="w-full bg-gradient-to-tr from-purple-700/30 via-purple-700/30 to-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl translate-x-0">
        {/* Header with animated background */}
        <div className="relative mb-16 text-center">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="h-32 w-32 animate-pulse rounded-full bg-gradient-to-r from-blue-500/20 to-purple-600/20 blur-xl"></div>
          </div>
          <div className="relative">
            <h2 className="mb-4 text-3xl font-bold text-white sm:text-4xl lg:text-5xl">
              Frequently Asked Questions
            </h2>
            <p className="mx-auto max-w-3xl text-lg text-white/70">
              Everything you need to know about our AI background remover tool.
            </p>
          </div>
        </div>

        {/* FAQ Items */}
        <div className="mx-auto max-w-4xl space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-800/20 via-purple-700/30 to-pink-900/20 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl hover:shadow-purple-500/25"
            >
              {/* Animated border gradient */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>

              {/* FAQ Content */}
              <div className="relative">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left transition-all duration-300 hover:bg-white/5"
                >
                  <div className="flex items-center justify-between gap-4">
                    <div className="flex items-center gap-4">
                      {/* Icon */}
                      <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500/30 to-purple-600/30 text-xl backdrop-blur-sm">
                        {faq.icon}
                      </div>
                      {/* Question */}
                      <h3 className="text-lg font-semibold text-white group-hover:text-blue-200 transition-colors duration-300">
                        {faq.question}
                      </h3>
                    </div>
                    {/* Toggle Icon */}
                    <div className="relative h-6 w-6 shrink-0">
                      <svg
                        className={`absolute inset-0 h-6 w-6 text-white/70 transition-all duration-300 ${
                          openIndex === index
                            ? 'rotate-45 opacity-0'
                            : 'rotate-0 opacity-100'
                        }`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                      <svg
                        className={`absolute inset-0 h-6 w-6 text-blue-400 transition-all duration-300 ${
                          openIndex === index
                            ? 'rotate-0 opacity-100'
                            : 'rotate-45 opacity-0'
                        }`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth={2}
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M20 12H4"
                        />
                      </svg>
                    </div>
                  </div>
                </button>

                {/* Answer with smooth animation */}
                <div
                  className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    openIndex === index
                      ? 'max-h-96 opacity-100'
                      : 'max-h-0 opacity-0'
                  }`}
                >
                  <div className="border-t border-white/10 px-6 pb-6 pt-4">
                    <div className="pl-16">
                      <p className="text-white/80 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA section */}
        <div className="mt-16 text-center">
          <div className="rounded-2xl bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 p-8 backdrop-blur-sm">
            <h3 className="mb-4 text-2xl font-bold text-white">
              Still Have Questions?
            </h3>
            <p className="mb-6 text-white/70">
              Can't find what you're looking for? Try our AI background remover
              now and see how easy it is!
            </p>
            <Link href={link}>
              <button className="group relative overflow-hidden rounded-full bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-3 font-semibold text-white transition-all duration-300 hover:opacity-90 hover:shadow-lg hover:shadow-purple-500/25">
                <span className="relative z-10">Try It Free Now</span>
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] transition-transform duration-700 group-hover:translate-x-[100%]"></div>
              </button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
