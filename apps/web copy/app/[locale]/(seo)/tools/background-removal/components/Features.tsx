import { Link } from '@i18n/routing'
import BeforeAfterSlider from './BeforeAfterSlider'
import AnimatedCard from './AnimateCard'

export default function Features({ link = '' }) {
  return (
    <section className="w-full bg-gradient-to-br from-purple-700/30 via-purple-700/30 to-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <h2 className="mb-4 text-center text-3xl font-bold text-white sm:text-4xl">
        Why Choose IMGGen as Your Go-To AI Background Remover Tool
      </h2>
      <p className="mx-auto translate-x-0 mb-12 max-w-3xl text-center text-lg text-white/30">
        Remove image backgrounds in one click with speed, precision, and no
        extra cost. Trusted by creators, sellers, and professionals alike.
      </p>
      <div className="mx-auto max-w-7xl space-y-24 translate-x-0">
        <div className="grid gap-8 lg:grid-cols-2">
          <AnimatedCard>
            <BeforeAfterSlider
              beforeAlt="image of a woman-before the background is removed"
              afterAlt="image of a woman-after the background is removed"
              beforeImage="/images/woman-before.png"
              afterImage="/images/woman-after.png"
            />
          </AnimatedCard>

          <div className="flex flex-col justify-center">
            <h3 className="mb-4 text-2xl font-bold text-white">
              Remove Backgrounds Automatically in One Tap
            </h3>
            <p className="mb-6 text-white/30">
              Our AI background remover detects the subject instantly and
              removes the background in seconds — no manual clicks or selections
              needed. Whether it’s a person, product, or pet, the tool ensures
              smooth, precise cutouts. It’s your go-to background remover free —
              fast, clean, and made for everyone.
            </p>
            <Link
              href={link}
              className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-sm relative overflow-hidden w-fit"
            >
              Start Background Removal Now
            </Link>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          <div className="flex flex-col justify-center">
            <h3 className="mb-4 text-2xl font-bold text-white">
              Works Seamlessly for Logos, People & Handwriting
            </h3>
            <p className="mb-6 text-white/30">
              Need to clean up logos, social avatars, or even handwritten
              signatures? Our image background remover handles them all. It
              works just as well for high-contrast logos as it does for fine
              edges like hair or ink strokes. Many users love it as a signature
              background remover or logo background remover tool — especially
              when preparing brand assets.
            </p>
            <Link
              href={link}
              className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-sm relative overflow-hidden w-fit"
            >
              Remove Background Free
            </Link>
          </div>
          <AnimatedCard>
            <BeforeAfterSlider
              beforeAlt="image of a signature-before the background is removed"
              afterAlt="image of a signature-after the background is removed"
              beforeImage="/images/signature-before.png"
              afterImage="/images/signature-after.png"
            />
          </AnimatedCard>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          <AnimatedCard>
            <BeforeAfterSlider
              beforeAlt="image of a tomatoes-before the background is changed"
              afterAlt="image of a tomatoes-after the background is changed"
              beforeImage="/images/tomatoes-before.png"
              afterImage="/images/tomatoes-after.png"
            />
          </AnimatedCard>
          <div className="flex flex-col justify-center">
            <h3 className="mb-4 text-2xl font-bold text-white">
              Keep It Transparent or Add a Clean Background
            </h3>
            <p className="mb-6 text-white/30">
              After removing the original background, you can leave your image
              on a transparent background, or replace it with a solid color of
              your choice. Use it to remove white from image files or to enhance
              visual clarity for presentations and shops. It also acts as a free
              transparent background maker — no watermark, no paywall.
            </p>
            <Link
              href={link}
              className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-sm relative overflow-hidden w-fit"
            >
              Make Background Transparent
            </Link>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          <div className="flex flex-col justify-center">
            <h3 className="mb-4 text-2xl font-bold text-white">
            Want a New Look? Use AI Background Generator
            </h3>
            <p className="mb-6 text-white/30">
            Looking to go beyond just removing? Try our AI backgrounds to automatically generate new scenes, colors, or textures for your image. Perfect for ads, profile images, posters, or anything where you want your subject to pop. Our background remover free tool connects seamlessly with the AI Background Generator — saving time and sparking creativity.
            </p>
            <Link
              href={link}
              className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-sm relative overflow-hidden w-fit"
            >
              Try AI Backgrounds
            </Link>
          </div>
          <AnimatedCard>
            <BeforeAfterSlider
              beforeAlt="image of an owl-before the background is changed"
              afterAlt="image of a owl-after the background is changed"
              beforeImage="/images/owl-before.png"
              afterImage="/images/owl-after.png"
            />
          </AnimatedCard>
        </div>
      </div>
    </section>
  )
}
