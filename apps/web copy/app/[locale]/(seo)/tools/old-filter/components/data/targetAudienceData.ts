import { getTranslations } from 'next-intl/server';

export interface TargetAudienceItem {
  image: string
  alt: string
  title: string
  description: string
}

export const getTargetAudienceData = async () => {
  const t = await getTranslations('oldfilter');
  return [
    {
      image: '/samples/age-target1.png',
      alt: 'age progression mother and daughter',
      title: t('targetAudience1Title'),
      description: t('targetAudience1Description'),
    },
    {
      image: '/samples/age-target2.png',
      alt: 'age progression child',
      title: t('targetAudience2Title'),
      description: t('targetAudience2Description'),
    },
    {
      image: '/samples/age-target3.png',
      alt: 'age progression couple',
      title: t('targetAudience3Title'),
      description: t('targetAudience3Description'),
    },
  ]
}
