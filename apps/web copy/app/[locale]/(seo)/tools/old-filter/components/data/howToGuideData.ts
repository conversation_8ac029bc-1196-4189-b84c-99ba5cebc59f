import { getTranslations } from 'next-intl/server';
import React from 'react'
import { UploadCloud, Settings, Download } from 'lucide-react'

export interface Step {
  icon: React.ReactElement
  title: string
  description: string
  image: string
}

export const getHowToGuideData = async () => {
  const t = await getTranslations('oldfilter');
  return [
    {
      icon: React.createElement(UploadCloud, {
        className: 'w-10 h-10 text-[#339bfa]',
      }),
      title: t('howTo1Title'),
      description: t('howTo1Description'),
      image: '/samples/age-step1.png',
    },
    {
      icon: React.createElement(Settings, {
        className: 'w-10 h-10 text-pink-400',
      }),
      title: t('howTo2Title'),
      description: t('howTo2Description'),
      image: '/samples/age-step2.png',
    },
    {
      icon: React.createElement(Download, {
        className: 'w-10 h-10 text-fuchsia-500',
      }),
      title: t('howTo3Title'),
      description: t('howTo3Description'),
      image: '/samples/age-step3.png',
    },
  ]
}
