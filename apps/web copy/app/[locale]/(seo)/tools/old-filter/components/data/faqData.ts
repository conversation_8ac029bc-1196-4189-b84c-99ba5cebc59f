import { getTranslations } from 'next-intl/server';

export interface FAQ {
  question: string
  answer: string
}

export const getFaqData = async () => {
  const t = await getTranslations('oldfilter');
  return [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
    {
      question: t('faq5Question'),
      answer: t('faq5Answer'),
    },
  ]
}
