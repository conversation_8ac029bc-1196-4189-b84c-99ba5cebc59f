import { getTranslations } from 'next-intl/server';

export interface Testimonial {
  name: string
  role: string
  avatar: string
  text: string
}

export const getTestimonialData = async () => {
  const t = await getTranslations('oldfilter');
  return [
    {
      name: t('testimonial1Name'),
      role: t('testimonial1Role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Lisa',
      text: t('testimonial1Text'),
    },
    {
      name: t('testimonial2Name'),
      role: t('testimonial2Role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=David',
      text: t('testimonial2Text'),
    },
    {
      name: t('testimonial3Name'),
      role: t('testimonial3Role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Rosa',
      text: t('testimonial3Text'),
    },
    {
      name: t('testimonial4Name'),
      role: t('testimonial4Role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Emily',
      text: t('testimonial4Text'),
    },
    {
      name: t('testimonial5Name'),
      role: t('testimonial5Role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Jake',
      text: t('testimonial5Text'),
    },
    {
      name: t('testimonial6Name'),
      role: t('testimonial6Role'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=SophieAlex',
      text: t('testimonial6Text'),
    },
  ]
}
