import { getTranslations } from 'next-intl/server';

export interface WhyUsItem {
  img: string
  alt: string
  title: string
  desc: string
  btn: string
}

export const getWhyUsData = async () => {
  const t = await getTranslations('oldfilter');
  return [
    {
      img: '/samples/age-why1.png',
      alt: 'age a photo with AI age progression free online',
      title: t('whyUs1Title'),
      desc: t('whyUs1Description'),
      btn: t('whyUs1Button'),
    },
    {
      img: '/samples/age-why2.png',
      alt: 'AI age progression free online of a man',
      title: t('whyUs2Title'),
      desc: t('whyUs2Description'),
      btn: t('whyUs2Button'),
    },
    {
      img: '/samples/age-why3.png',
      alt: 'before and after photo aged transformation',
      title: t('whyUs3Title'),
      desc: t('whyUs3Description'),
      btn: t('whyUs3Button'),
    },
    {
      img: '/samples/age-why4.png',
      alt: 'old age filter used for profile privacy',
      title: t('whyUs4Title'),
      desc: t('whyUs4Description'),
      btn: t('whyUs4Button'),
    },
  ]
}
