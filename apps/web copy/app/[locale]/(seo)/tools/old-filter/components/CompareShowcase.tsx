import { getTranslations } from 'next-intl/server'
import React from 'react'
import Image from 'next/image'

export default async function CompareShowcase() {
  const t = await getTranslations('oldfilter')
  return (
    <div className="relative flex items-end gap-6 animate-fade-in-up">
      {/* Before 图 */}
      <div className="relative max-md:w-40 max-md:h-52 w-52 h-64 shadow-2xl rounded-2xl overflow-hidden transform transition-all duration-700 hover:scale-105 hover:shadow-3xl animate-slide-in-left">
        <Image
          src="/samples/age-before.jpg"
          alt="AI age progression free online result-Before"
          fill
          className="object-cover transition-transform duration-700 hover:scale-110"
          priority
        />
        <span className="absolute top-2 left-2 bg-black/60 text-white text-xs px-3 py-1 rounded-full font-semibold animate-pulse">
          {t('compareBefore')}
        </span>
        {/* 添加光晕效果 */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
      </div>
      {/* After 图 */}
      <div className="relative shadow-2xl max-md:w-40 max-md:h-52 w-52 h-64 rounded-2xl overflow-hidden transform transition-all duration-700 hover:scale-105 hover:shadow-3xl animate-slide-in-right">
        <Image
          src="/samples/age-after.jpg"
          alt="AI age progression free online result-After"
          fill
          className="object-cover transition-transform duration-700 hover:scale-110"
          priority
        />
        <span className="absolute top-2 left-2 bg-black/60 text-white text-xs px-3 py-1 rounded-full font-semibold animate-pulse">
          {t('compareAfter')}
        </span>
        {/* 添加光晕效果 */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
      </div>
      {/* 弯曲箭头 */}
      <svg
        className="absolute left-44 max-md:left-32 bottom-10 animate-bounce-slow"
        width="80"
        height="60"
        viewBox="0 0 80 60"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10 10C40 60 60 0 70 40"
          stroke="#fff"
          strokeWidth="4"
          strokeLinecap="round"
          className="animate-draw-path"
        />
        <path
          d="M70 40L62 36M70 40L68 32"
          stroke="#fff"
          strokeWidth="4"
          strokeLinecap="round"
          className="animate-draw-arrow"
        />
      </svg>

      {/* 添加全局样式 */}
      {/* <style jsx>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slide-in-left {
          from {
            opacity: 0;
            transform: translateX(-50px) scale(0.8);
          }
          to {
            opacity: 1;
            transform: translateX(0) scale(1);
          }
        }

        @keyframes slide-in-right {
          from {
            opacity: 0;
            transform: translateX(50px) scale(0.8);
          }
          to {
            opacity: 1;
            transform: translateX(0) scale(1);
          }
        }

        @keyframes bounce-slow {
          0%,
          100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @keyframes draw-path {
          from {
            stroke-dasharray: 100;
            stroke-dashoffset: 100;
          }
          to {
            stroke-dashoffset: 0;
          }
        }

        @keyframes draw-arrow {
          from {
            stroke-dasharray: 20;
            stroke-dashoffset: 20;
          }
          to {
            stroke-dashoffset: 0;
          }
        }

        .animate-fade-in-up {
          animation: fade-in-up 1s ease-out;
        }

        .animate-slide-in-left {
          animation: slide-in-left 1s ease-out 0.2s both;
        }

        .animate-slide-in-right {
          animation: slide-in-right 1s ease-out 0.4s both;
        }

        .animate-bounce-slow {
          animation: bounce-slow 2s ease-in-out infinite;
        }

        .animate-draw-path {
          animation: draw-path 1.5s ease-out 0.6s both;
        }

        .animate-draw-arrow {
          animation: draw-arrow 0.5s ease-out 2s both;
        }

        .hover\\:shadow-3xl:hover {
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
        }
      `}</style> */}
    </div>
  )
}
