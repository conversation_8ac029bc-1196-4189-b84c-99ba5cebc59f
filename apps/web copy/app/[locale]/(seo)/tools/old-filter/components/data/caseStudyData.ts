import { getTranslations } from 'next-intl/server';

export interface CaseStudy {
  image: string
  alt: string
}

export const getCaseStudyData = async () => {
  const t = await getTranslations('oldfilter');
  return [
    {
      image: '/samples/age-case1.png',
      alt: t('caseStudy1Alt'),
    },
    {
      image: '/samples/age-case2.png',
      alt: t('caseStudy2Alt'),
    },
    {
      image: '/samples/age-case3.png',
      alt: t('caseStudy3Alt'),
    },
    {
      image: '/samples/age-case4.png',
      alt: t('caseStudy4Alt'),
    },
    {
      image: '/samples/age-case5.png',
      alt: t('caseStudy5Alt'),
    },
    {
      image: '/samples/age-case6.png',
      alt: t('caseStudy6Alt'),
    },
  ]
}
