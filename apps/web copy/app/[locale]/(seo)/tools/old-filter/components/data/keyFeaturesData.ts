import { getTranslations } from 'next-intl/server';
import { Sparkles, Download, Globe, Zap } from 'lucide-react'

export interface KeyFeature {
  icon: React.ComponentType<{ className?: string }>
  title: string
  description: string
}

export const getKeyFeaturesData = async () => {
  const t = await getTranslations('oldfilter');
  return [
    {
      icon: Sparkles,
      title: t('keyFeature1Title'),
      description: t('keyFeature1Description'),
    },
    {
      icon: Download,
      title: t('keyFeature2Title'),
      description: t('keyFeature2Description'),
    },
    {
      icon: Globe,
      title: t('keyFeature3Title'),
      description: t('keyFeature3Description'),
    },
    {
      icon: Zap,
      title: t('keyFeature4Title'),
      description: t('keyFeature4Description'),
    },
  ]
}
