import { getTranslations } from 'next-intl/server'
import Image from 'next/image'
import React from 'react'
import { getTargetAudienceData } from './data/targetAudienceData'

const TargetAudienceSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('oldfilter')
  const targetAudienceData = await getTargetAudienceData()
  return (
    <section className="relative py-20">
      <div className="container mx-auto px-4 md:px-8">
        {/* 标题和副标题 */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            {t('targetAudienceTitle')}
          </h2>
          <p className="text-lg md:text-xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
            {t('targetAudienceDescription')}
          </p>
        </div>

        {/* 三个使用场景 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {targetAudienceData.map((item, index) => (
            <div
              key={index}
              className="bg-slate-800 rounded-2xl p-8 hover:bg-slate-750 transition-all duration-300 border border-slate-700 hover:border-purple-500"
            >
              <div className="aspect-square relative bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl mb-6 flex items-center justify-center">
                <Image
                  src={item.image}
                  alt={item.alt}
                  className="w-full h-full object-cover"
                  fill
                />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">
                {item.title}
              </h3>
              <p className="text-gray-400 leading-relaxed">
                {item.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TargetAudienceSection
