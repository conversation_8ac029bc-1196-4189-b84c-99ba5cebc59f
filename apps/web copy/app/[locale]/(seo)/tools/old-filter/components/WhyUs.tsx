import { getTranslations } from 'next-intl/server'
import React from 'react'
import { getWhyUsData } from './data/whyUsData'
import { Link } from '@i18n/routing'

const Whyus = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('oldfilter')
  const whyUsData = await getWhyUsData()
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-16">
          {t('whyUsTitle')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          {whyUsData.map((item, idx) => (
            <div
              key={idx}
              className="rounded-2xl p-6 flex flex-col items-center text-center shadow-md"
            >
              <img
                src={item.img}
                alt={item.alt}
                className="w-full aspect-video object-cover rounded-xl mb-6 shadow-md hover:scale-105 transition-all duration-300"
              />
              <h3 className="text-2xl font-semibold text-white mb-4">
                {item.title}
              </h3>
              <p className="text-gray-300 text-base mb-8">{item.desc}</p>
              <Link href={toolUrl}>
                <button className="px-7 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-full shadow-lg hover:opacity-90 transition-all">
                  {item.btn}
                </button>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Whyus
