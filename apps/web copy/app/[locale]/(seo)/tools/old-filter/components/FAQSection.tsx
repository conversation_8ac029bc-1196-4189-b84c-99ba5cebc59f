import { getTranslations } from 'next-intl/server'
import { getFaqData } from './data/faqData'

export default async function FAQSection({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('oldfilter')
  const faqData = await getFaqData()

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-center text-white mb-4">
            {t('faqTitle')}
          </h2>
          <p className="text-lg md:text-xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
            {t('faqDescription')}
          </p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          <div className="space-y-4">
            {faqData.slice(0, 3).map((faq, index) => (
              <details
                key={index}
                className="group bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 transition-all duration-300 hover:border-slate-700 open:border-purple-500"
              >
                <summary
                  className="p-6 flex justify-between items-center cursor-pointer list-none"
                  style={{
                    animation: `fade-in-up 0.5s ${index * 0.1}s ease-out both`,
                  }}
                >
                  <span className="font-semibold text-white text-lg">
                    {faq.question}
                  </span>
                  <svg
                    className="w-5 h-5 text-gray-400 transition-transform duration-300 group-open:rotate-180 group-open:text-purple-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </summary>
                <div className="px-6 pb-6 pt-2 text-gray-400">
                  {faq.answer.split('\n').map((paragraph, i) =>
                    paragraph.startsWith('-') ? (
                      <ul key={i} className="pl-5 space-y-2">
                        <li className="list-disc list-outside">
                          <span
                            dangerouslySetInnerHTML={{
                              __html: paragraph.substring(1).trim(),
                            }}
                          />
                        </li>
                      </ul>
                    ) : (
                      <p
                        key={i}
                        className="text-gray-300 mb-4"
                        dangerouslySetInnerHTML={{ __html: paragraph }}
                      />
                    )
                  )}
                </div>
              </details>
            ))}
          </div>

          <div className="space-y-4">
            {faqData.slice(3).map((faq, index) => (
              <details
                key={index + 3}
                className="group bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 transition-all duration-300 hover:border-slate-700 open:border-purple-500"
              >
                <summary
                  className="p-6 flex justify-between items-center cursor-pointer list-none"
                  style={{
                    animation: `fade-in-up 0.5s ${
                      (index + 3) * 0.1
                    }s ease-out both`,
                  }}
                >
                  <span className="font-semibold text-white text-lg">
                    {faq.question}
                  </span>
                  <svg
                    className="w-5 h-5 text-gray-400 transition-transform duration-300 group-open:rotate-180 group-open:text-purple-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </summary>
                <div className="px-6 pb-6 pt-2 text-gray-400">
                  {faq.answer.split('\n').map((paragraph, i) =>
                    paragraph.startsWith('-') ? (
                      <ul key={i} className="pl-5 space-y-2">
                        <li className="list-disc list-outside">
                          <span
                            dangerouslySetInnerHTML={{
                              __html: paragraph.substring(1).trim(),
                            }}
                          />
                        </li>
                      </ul>
                    ) : (
                      <p
                        key={i}
                        className="text-gray-300 mb-4"
                        dangerouslySetInnerHTML={{ __html: paragraph }}
                      />
                    )
                  )}
                </div>
              </details>
            ))}
          </div>
        </div>
      </div>

      {/* <style jsx global>{`
        @keyframes fade-in-up {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        details > summary::-webkit-details-marker {
          display: none;
        }
      `}</style> */}
    </section>
  )
}
