import { getTranslations } from 'next-intl/server'
import React from 'react'
import StaticImageSplit from '../../components/StaticImageSplit'
import { getCaseStudyData } from './data/caseStudyData'

const CaseStudySection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('oldfilter')
  const caseStudyData = await getCaseStudyData()
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-16">
          {t('caseStudyTitle')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 justify-items-center">
          {caseStudyData.map((caseStudy, idx) => (
            <div
              key={idx}
              title={caseStudy.alt}
              className="rounded-3xl overflow-hidden bg-slate-900/70 border border-slate-800 shadow-2xl transition-transform duration-300 hover:-translate-y-2 hover:shadow-purple-500/30 w-full max-w-xs flex flex-col items-center"
            >
              <img
                src={caseStudy.image}
                alt={caseStudy.alt}
                className="w-full h-64 object-cover rounded-3xl shadow-md transition-all duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CaseStudySection
