import { getTranslations } from 'next-intl/server';
import React from 'react'

import { getTestimonialData } from './data/testimonialData'

const TestimonialSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('oldfilter');
  const testimonialData = await getTestimonialData()
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {t('testimonialTitle')}
          </h2>
          <p className="text-lg md:text-xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
            {t('testimonialDescription')}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
          {testimonialData.map((testimonial, idx) => (
            <div
              key={idx}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 flex flex-col"
            >
              <div className="flex items-center gap-4 mb-2">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-16 h-16 rounded-full border-2 border-white"
                  loading="lazy"
                />
                <div>
                  <div className="font-bold text-white text-lg">{testimonial.name}</div>
                  <div className="text-sm text-gray-400 font-medium">
                    {testimonial.role}
                  </div>
                </div>
              </div>
              <p className="text-gray-400 text-base leading-relaxed text-left pl-1 pr-1">
                "{testimonial.text}"
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection

