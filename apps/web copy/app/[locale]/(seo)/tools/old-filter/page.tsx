import { getTranslations } from 'next-intl/server'
import React from 'react'
import { UploadCloud } from 'lucide-react'
import HowToGuideSection from './components/HowToGuideSection'
import CaseStudySection from './components/CaseStudySection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'
import WhyUs from './components/WhyUs'
import TargetAudienceSection from './components/TargetAudienceSection'
import KeyFeaturesSection from './components/KeyFeaturesSection'

import CompareShowcase from './components/CompareShowcase'
import { Link } from '@i18n/routing'

export async function generateMetadata() {
  const t = await getTranslations('oldfilter')
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      url: 'https://imggen.org/tools/old-filter',
      type: 'website',
      images: [
        {
          url: 'https://imggen.org/images/ai-age-progression-preview.png',
          width: 1200,
          height: 630,
          alt: t('openGraphTitle'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: ['https://imggen.org/images/ai-age-progression-preview.png'],
    },
  }
}

const OldFilterPage = async () => {
  const t = await getTranslations('oldfilter')

  // JSON-LD Schema
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('jsonLdName'),
    description: t('jsonLdDescription'),
    applicationCategory: 'PhotoApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    image: 'https://imggen.org/images/ai-age-progression-preview.png',
    keywords: [
      'AI age progression',
      'age filter',
      'aging filter',
      'photo aged',
      'old filter',
      'face ager',
      'aging ai',
      'photo aging',
    ],
  }
  // 统一配置跳转URL
  const OLD_FILTER_TOOL_URL = '/ai/old-filter'

  return (
    <div className="relative h-full w-full bg-[#0f172a]">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(jsonLd),
        }}
      />
      <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)]"></div>
      {/* Animated background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1),rgba(255,255,255,0)_50%)]"></div>
        <div
          className="absolute h-[200px] w-[400px] bg-purple-500 rounded-full blur-[100px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse"
          style={{ animationDuration: '6s' }}
        ></div>
        <div
          className="absolute h-[150px] w-[300px] bg-pink-500 rounded-full blur-[80px] top-1/4 left-1/4 animate-pulse"
          style={{ animationDuration: '8s', animationDelay: '2s' }}
        ></div>
        <div
          className="absolute h-[180px] w-[350px] bg-fuchsia-500 rounded-full blur-[90px] bottom-1/4 right-1/4 animate-pulse"
          style={{ animationDuration: '7s', animationDelay: '4s' }}
        ></div>
      </div>

      {/* Add JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <div className="relative z-10 min-h-screen flex items-center">
        <div className="relative container mx-auto px-4 md:px-8 py-12 md:py-24 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 左侧：文字+按钮 */}
            <div className="text-white animate-fade-in-up max-md:w-full">
              <h1 className="text-3xl md:text-5xl font-bold mb-6 text-fuchsia-400">
                {t('pageTitle')}
              </h1>
              <p className="text-lg md:text-xl text-gray-400 mb-10 font-normal">
                {t('pageDescription')}
              </p>
              <div
                className="hidden justify-center animate-fade-in-up max-md:flex mb-8"
                style={{ animationDelay: '0.3s' }}
              >
                <CompareShowcase />
              </div>
              <div className="max-md:flex max-md:justify-center">
                <Link href={OLD_FILTER_TOOL_URL}>
                  <button className="relative inline-block p-px font-semibold leading-6 text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all shadow-2xl cursor-pointer group rounded-xl shadow-zinc-900 transition-transform duration-300 ease-in-out hover:scale-105 active:scale-95">
                    <span className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>

                    <span className="relative z-10 block px-8 py-4 rounded-xl bg-slate-950 text-gray-200 hover:text-white font-semibold text-lg">
                      <div className="relative z-10 flex items-center space-x-2">
                        <UploadCloud className="w-5 h-5 transition-transform duration-500" />
                        <span className="transition-all duration-500">
                          {t('uploadButton')}
                        </span>
                      </div>
                    </span>
                  </button>
                </Link>
              </div>
            </div>

            <div
              className="hidden justify-center animate-fade-in-up md:flex"
              style={{ animationDelay: '0.3s' }}
            >
              <CompareShowcase />
            </div>
          </div>
        </div>
      </div>

      <HowToGuideSection toolUrl={OLD_FILTER_TOOL_URL} />
      <CaseStudySection toolUrl={OLD_FILTER_TOOL_URL} />
      <WhyUs toolUrl={OLD_FILTER_TOOL_URL} />
      <TargetAudienceSection toolUrl={OLD_FILTER_TOOL_URL} />
      <KeyFeaturesSection toolUrl={OLD_FILTER_TOOL_URL} />
      <TestimonialSection toolUrl={OLD_FILTER_TOOL_URL} />
      <FAQSection toolUrl={OLD_FILTER_TOOL_URL} />
    </div>
  )
}

export default OldFilterPage
