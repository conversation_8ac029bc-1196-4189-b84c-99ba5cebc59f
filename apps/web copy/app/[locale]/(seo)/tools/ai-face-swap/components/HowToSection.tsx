import { getTranslations } from 'next-intl/server'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

export default async function HowToSection({ toolUrl = '' }) {
  const t = await getTranslations('aiFaceSwap')
  const steps = [
    {
      stepNumber: 1,
      title: t('step1Title'),
      description: t('step1Description'),
      icon: 'fas fa-upload',
      image: '/images/ai-face-swap/how-to-1.jpg',
      imageAlt:
        'Step 1: Upload your two photos to start the AI face swap process',
    },
    {
      stepNumber: 2,
      title: t('step2Title'),
      description: t('step2Description'),
      icon: 'fas fa-wand-magic-sparkles',
      image: '/images/ai-face-swap/how-to-2.jpg',
      imageAlt: 'Step 2: Generate and download your AI face swap result',
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-purple-400">{t('howToTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('howToDescription')}
          </p>
        </div>

        {/* Steps - Enhanced Layout */}
        <div className="space-y-20">
          {steps.map((step, index) => (
            <AnimatedCard key={index} delay={index * 200}>
              <div
                className={`flex flex-col ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } items-center gap-8 lg:gap-12`}
              >
                {/* Image Container - Enhanced */}
                <div className="w-full lg:w-1/2 relative">
                  <div className="relative group">
                    {/* Enhanced glow effect */}
                    <div className="absolute -inset-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur-xl opacity-20 group-hover:opacity-40 transition duration-500" />
                    <div className="relative">
                      <img
                        src={step.image}
                        alt={step.imageAlt}
                        className="w-full h-full object-cover rounded-3xl shadow-2xl"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent rounded-3xl" />

                      {/* Progress indicator for steps */}
                      <div className="absolute bottom-6 left-6 right-6">
                        <div className="flex items-center gap-2">
                          {steps.map((_, i) => (
                            <div
                              key={i}
                              className={`h-1 rounded-full transition-all duration-300 ${
                                i <= index
                                  ? 'bg-gradient-to-r from-purple-400 to-pink-400 flex-1'
                                  : 'bg-white/20 w-8'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Container - Enhanced */}
                <div className="w-full lg:w-1/2 space-y-6">
                  {/* Step Header */}
                  <div className="flex items-center gap-6">
                    <div className="relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-50" />
                      <div className="relative flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl shadow-lg">
                        <i className={`${step.icon} text-white text-xl`} />
                      </div>
                    </div>
                    <div>
                      <div className="text-purple-400 text-lg font-semibold mb-1">
                        Step {step.stepNumber}
                      </div>
                      <div className="text-white/60 text-sm">
                        {index === 0
                          ? t('startHereLabel')
                          : t('finalStepLabel')}
                      </div>
                    </div>
                  </div>

                  {/* H3 with exact README content */}
                  <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white leading-tight">
                    {step.title}
                  </h3>

                  {/* P with exact README content */}
                  <p className="text-white/80 leading-relaxed text-lg">
                    {step.description}
                  </p>

                  {/* Features for this step - Enhanced Design */}
                  <div className="grid grid-cols-1 gap-3">
                    {step.stepNumber === 1 ? (
                      <>
                        <div className="flex items-center gap-3 p-4 bg-white/5 rounded-xl border border-white/10 group hover:bg-white/10 transition-colors">
                          <div className="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center group-hover:bg-purple-500/30 transition-colors">
                            <i className="fas fa-mouse-pointer text-purple-400 text-sm" />
                          </div>
                          <span className="text-white/90 font-medium">
                            {t('dragDropInterface')}
                          </span>
                        </div>
                        <div className="flex items-center gap-3 p-4 bg-white/5 rounded-xl border border-white/10 group hover:bg-white/10 transition-colors">
                          <div className="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center group-hover:bg-purple-500/30 transition-colors">
                            <i className="fas fa-file-image text-purple-400 text-sm" />
                          </div>
                          <span className="text-white/90 font-medium">
                            {t('multipleFormatsSupported')}
                          </span>
                        </div>
                        <div className="flex items-center gap-3 p-4 bg-white/5 rounded-xl border border-white/10 group hover:bg-white/10 transition-colors">
                          <div className="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center group-hover:bg-purple-500/30 transition-colors">
                            <i className="fas fa-eye text-purple-400 text-sm" />
                          </div>
                          <span className="text-white/90 font-medium">
                            {t('instantPreview')}
                          </span>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex items-center gap-3 p-4 bg-white/5 rounded-xl border border-white/10 group hover:bg-white/10 transition-colors">
                          <div className="w-8 h-8 bg-pink-500/20 rounded-full flex items-center justify-center group-hover:bg-pink-500/30 transition-colors">
                            <i className="fas fa-image text-pink-400 text-sm" />
                          </div>
                          <span className="text-white/90 font-medium">
                            {t('highResolutionOutput')}
                          </span>
                        </div>
                        <div className="flex items-center gap-3 p-4 bg-white/5 rounded-xl border border-white/10 group hover:bg-white/10 transition-colors">
                          <div className="w-8 h-8 bg-pink-500/20 rounded-full flex items-center justify-center group-hover:bg-pink-500/30 transition-colors">
                            <i className="fas fa-download text-pink-400 text-sm" />
                          </div>
                          <span className="text-white/90 font-medium">
                            {t('instantDownload')}
                          </span>
                        </div>
                        <div className="flex items-center gap-3 p-4 bg-white/5 rounded-xl border border-white/10 group hover:bg-white/10 transition-colors">
                          <div className="w-8 h-8 bg-pink-500/20 rounded-full flex items-center justify-center group-hover:bg-pink-500/30 transition-colors">
                            <i className="fas fa-check text-pink-400 text-sm" />
                          </div>
                          <span className="text-white/90 font-medium">
                            {t('noWatermarks')}
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Bottom CTA - Enhanced Design */}
        <div className="text-center mt-20">
          <div className="max-w-md mx-auto">
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-50 group-hover:opacity-75 transition duration-300" />
              <div className="relative">
                <CTAButton
                  href={toolUrl}
                  size="lg"
                  className="w-full text-xl py-8 px-12 rounded-2xl shadow-2xl"
                >
                  <i className="fas fa-rocket mr-3" />
                  {t('startFreeSwapButton')}
                </CTAButton>
              </div>
            </div>
            <div className="mt-6 flex items-center justify-center gap-8 text-white/60 text-sm">
              <div className="flex items-center gap-2">
                <i className="fas fa-check text-green-400" />
                <span>{t('noRegistrationRequired')}</span>
              </div>
              <div className="flex items-center gap-2">
                <i className="fas fa-infinity text-purple-400" />
                <span>{t('freeForever')}</span>
              </div>
              <div className="flex items-center gap-2">
                <i className="fas fa-bolt text-yellow-400" />
                <span>{t('instantResults')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
