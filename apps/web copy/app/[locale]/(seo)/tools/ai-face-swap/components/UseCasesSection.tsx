import { getTranslations } from 'next-intl/server'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

export default async function UseCasesSection({ toolUrl = '' }) {
  const t = await getTranslations('aiFaceSwap')
  const useCases = [
    {
      title: t('useCase1Title'),
      description: t('useCase1Description'),
      image: '/images/ai-face-swap/use-case-1.jpg',
      alt: 'A funny meme showing a cat with a human face, created with our free face swap tool.',
      ctaText: t('useCase1Cta'),
      stats: [
        {
          icon: 'fas fa-laugh',
          value: t('useCase1Stat1'),
          label: t('useCase1Stat1Label'),
        },
        {
          icon: 'fas fa-chart-line',
          value: t('useCase1Stat2'),
          label: t('useCase1Stat2Label'),
        },
        {
          icon: 'fas fa-heart',
          value: t('useCase1Stat3'),
          label: t('useCase1Stat3Label'),
        },
      ],
    },
    {
      title: t('useCase2Title'),
      description: t('useCase2Description'),
      image: '/images/ai-face-swap/use-case-2.jpg',
      alt: "A user's face swapped onto the Mona Lisa painting using the AI face swap tool.",
      ctaText: t('useCase2Cta'),
      stats: [
        {
          icon: 'fas fa-palette',
          value: t('useCase2Stat1'),
          label: t('useCase2Stat1Label'),
        },
        {
          icon: 'fas fa-clock',
          value: t('useCase2Stat2'),
          label: t('useCase2Stat2Label'),
        },
        {
          icon: 'fas fa-images',
          value: t('useCase2Stat3'),
          label: t('useCase2Stat3Label'),
        },
      ],
    },
    {
      title: t('useCase3Title'),
      description: t('useCase3Description'),
      image: '/images/ai-face-swap/use-case-3.jpg',
      alt: 'A grid of professional headshots for different careers, all generated from one face using the free ai headshot generator.',
      ctaText: t('useCase3Cta'),
      stats: [
        {
          icon: 'fas fa-users',
          value: t('useCase3Stat1'),
          label: t('useCase3Stat1Label'),
        },
        {
          icon: 'fas fa-dollar-sign',
          value: t('useCase3Stat2'),
          label: t('useCase3Stat2Label'),
        },
        {
          icon: 'fas fa-star',
          value: t('useCase3Stat3'),
          label: t('useCase3Stat3Label'),
        },
      ],
    },
    {
      title: t('useCase4Title'),
      description: t('useCase4Description'),
      image: '/images/ai-face-swap/use-case-4.jpg',
      alt: 'A group photo corrected with the face swap online free tool to fix a person who blinked.',
      ctaText: t('useCase4Cta'),
      stats: [
        {
          icon: 'fas fa-users',
          value: t('useCase4Stat1'),
          label: t('useCase4Stat1Label'),
        },
        {
          icon: 'fas fa-clock',
          value: t('useCase4Stat2'),
          label: t('useCase4Stat2Label'),
        },
        {
          icon: 'fas fa-thumbs-up',
          value: t('useCase4Stat3'),
          label: t('useCase4Stat3Label'),
        },
      ],
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('useCasesTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('useCasesDescription')}
          </p>
        </div>

        {/* Use Cases Grid - Enhanced Layout */}
        <div className="space-y-20">
          {useCases.map((useCase, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div
                className={`flex flex-col ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } items-center gap-8 lg:gap-12`}
              >
                {/* Image Container - Enhanced */}
                <div className="w-full lg:w-1/2 relative">
                  <div className="relative group">
                    <div className="absolute -inset-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur-xl opacity-20 group-hover:opacity-40 transition duration-500" />
                    <div className="relative overflow-hidden rounded-3xl">
                      <img
                        src={useCase.image}
                        alt={useCase.alt}
                        className="w-full h-full object-cover transform transition duration-500 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20" />
                    </div>
                  </div>
                </div>

                {/* Content Container - Enhanced */}
                <div className="w-full lg:w-1/2 space-y-6">
                  {/* H3 with exact README content */}
                  <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white leading-tight">
                    {useCase.title}
                  </h3>

                  {/* P with exact README content */}
                  <p className="text-white/80 leading-relaxed text-lg">
                    {useCase.description}
                  </p>

                  {/* Stats - Enhanced Design */}
                  <div className="grid grid-cols-3 gap-4">
                    {useCase.stats.map((stat, statIndex) => (
                      <div key={statIndex} className="group relative">
                        <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-20 group-hover:opacity-40 transition duration-300" />
                        <div className="relative text-center p-4 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 group-hover:bg-white/10 transition-all duration-300">
                          <div className="w-10 h-10 mx-auto mb-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center">
                            <i className={`${stat.icon} text-white text-lg`} />
                          </div>
                          <div className="text-white font-bold text-xl mb-1">
                            {stat.value}
                          </div>
                          <div className="text-white/60 text-sm font-medium">
                            {stat.label}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* CTA - Enhanced Design */}
                  <div className="pt-4">
                    <div className="relative group inline-block">
                      <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl blur opacity-30 group-hover:opacity-60 transition duration-300" />
                      <CTAButton className="relative" href={toolUrl}>
                        <i className="fas fa-magic mr-2" />
                        {useCase.ctaText}
                      </CTAButton>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Bottom Summary Section - New Addition */}
        <div className="mt-20">
          <AnimatedCard delay={400}>
            <div className="text-center space-y-8">
              <div className="max-w-3xl mx-auto">
                <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                  {t('readyToExploreTitle')}
                </h3>
                <p className="text-white/80 text-lg leading-relaxed">
                  {t('readyToExploreDescription')}
                </p>
              </div>

              {/* Quick stats overview */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-purple-400">6M+</div>
                  <div className="text-white/60 text-sm">
                    {t('totalCreationsLabel')}
                  </div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-purple-400">100+</div>
                  <div className="text-white/60 text-sm">
                    {t('creativeStylesLabel')}
                  </div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-purple-400">4.9★</div>
                  <div className="text-white/60 text-sm">
                    {t('averageRatingLabel')}
                  </div>
                </div>
                <div className="text-center space-y-2">
                  <div className="text-3xl font-bold text-pink-400">24/7</div>
                  <div className="text-white/60 text-sm">
                    {t('alwaysAvailableLabel')}
                  </div>
                </div>
              </div>
            </div>
          </AnimatedCard>
        </div>
      </div>
    </section>
  )
}
