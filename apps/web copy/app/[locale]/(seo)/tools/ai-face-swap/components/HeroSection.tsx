import { getTranslations } from 'next-intl/server'
import CT<PERSON>utton from './CTAButton'

export default async function HeroSection({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('aiFaceSwap')
  return (
    <section className="relative min-h-screen flex flex-col items-center justify-center px-4 pt-20 pb-16 overflow-hidden bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Main Content Container - Now with flexible layout */}
        <div className="flex flex-col xl:flex-row items-center gap-8 xl:gap-16">
          {/* Left Column - Header Content */}
          <div className="flex-1 text-center xl:text-left">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-6">
              <i className="fas fa-magic text-purple-400" />
              <span>{t('aiPoweredBadge')}</span>
            </div>

            {/* Main Title - H1 from README */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 xl:mb-6 leading-tight">
              <span className="text-white">{t('heroTitle1')}</span>
              <br />
              <span className="text-purple-400">{t('heroTitle2')}</span>
            </h1>

            {/* Description - P from README */}
            <p className="text-base md:text-lg lg:text-xl text-white/80 max-w-2xl mx-auto xl:mx-0 mb-6 xl:mb-8 leading-relaxed">
              {t('heroDescription')}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row items-center xl:items-start xl:justify-start justify-center gap-4 mb-8 xl:mb-0">
              <CTAButton size="lg" className="w-full sm:w-auto" href={toolUrl}>
                <i className="fas fa-upload mr-2" />
                {t('tryAiSwapButton')}
              </CTAButton>
              <CTAButton
                href={toolUrl}
                variant="secondary"
                size="lg"
                className="w-full sm:w-auto"
              >
                <i className="fas fa-play mr-2" />
                {t('seeExamplesButton')}
              </CTAButton>
            </div>
          </div>

          {/* Right Column - Before/After Preview */}
          <div className="flex-1 w-full max-w-2xl">
            <div className="relative">
              {/* Desktop and Tablet Layout */}
              <div className="hidden md:block">
                <div className="flex items-center justify-center gap-4 lg:gap-8">
                  {/* Before Image */}
                  <div className="relative group flex-1 max-w-xs">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300" />
                    <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="text-center mb-3">
                        <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                          <i className="fas fa-user" />
                          {t('originalPhotoLabel')}
                        </span>
                      </div>
                      <img
                        src="/images/ai-face-swap/before.jpg"
                        alt="Original photo before AI face swap transformation"
                        className="w-full aspect-square object-cover rounded-xl"
                      />
                    </div>
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center justify-center flex-shrink-0">
                    <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full shadow-lg">
                      <i className="fas fa-arrow-right text-white text-xl" />
                    </div>
                  </div>

                  {/* After Image */}
                  <div className="relative group flex-1 max-w-xs">
                    <div className="absolute -inset-1 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300" />
                    <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="text-center mb-3">
                        <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                          <i className="fas fa-sparkles" />
                          {t('aiFaceSwapResultLabel')}
                        </span>
                      </div>
                      <img
                        src="/images/ai-face-swap/after.jpg"
                        alt="AI-generated face swap result using our advanced face swap technology"
                        className="w-full aspect-square object-cover rounded-xl"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Mobile Layout */}
              <div className="block md:hidden">
                <div className="space-y-6">
                  {/* Before Image */}
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-75" />
                    <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="text-center mb-3">
                        <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                          <i className="fas fa-user" />
                          {t('originalPhotoLabel')}
                        </span>
                      </div>
                      <img
                        src="/images/ai-face-swap/before.jpg"
                        alt="Original photo before AI face swap transformation"
                        className="w-full max-w-xs mx-auto rounded-xl"
                      />
                    </div>
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center justify-center">
                    <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full">
                      <i className="fas fa-arrow-down text-white text-xl" />
                    </div>
                  </div>

                  {/* After Image */}
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl blur opacity-75" />
                    <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="text-center mb-3">
                        <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                          <i className="fas fa-sparkles" />
                          {t('aiFaceSwapResultLabel')}
                        </span>
                      </div>
                      <img
                        src="/images/ai-face-swap/after.jpg"
                        alt="AI-generated face swap result using our advanced face swap technology"
                        className="w-full max-w-xs mx-auto rounded-xl"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}
