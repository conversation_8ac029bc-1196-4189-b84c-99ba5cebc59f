// 'use client' and useState have been removed. This is now a Server Component.
// The custom AnimatedCard component has also been removed.

import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

export default function FAQSection({ toolUrl = '' }) {
  const t = useTranslations('aiFaceSwap')
  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
  ]

  return (
    <section className="py-24 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Section Header - Enhanced */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-purple-400">{t('faqTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('faqDescription')}
          </p>
        </div>

        {/* FAQ Items - Enhanced Design */}
        <div className="space-y-6">
          {faqs.map((faq, index) => (
            <div key={index} className="group relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-3xl blur opacity-0 group-hover:opacity-100 transition duration-300" />
              <div className="relative rounded-3xl bg-white/5 border border-white/10 backdrop-blur-sm hover:bg-white/10 hover:border-white/20 transition-all duration-300">
                {/* This hidden checkbox drives the accordion state */}
                <input
                  type="checkbox"
                  id={`faq-${index}`}
                  className="absolute opacity-0 peer"
                  // The first item is open by default for demonstration
                  defaultChecked={index === 0}
                />

                {/* The <button> is replaced with a <label> for accessibility and functionality */}
                <label
                  htmlFor={`faq-${index}`}
                  className="w-full text-left p-8 flex items-center justify-between cursor-pointer group-hover:bg-white/5 transition-colors duration-200"
                >
                  <div className="flex items-start gap-6 flex-1">
                    {/* Question Number Badge */}
                    <div className="flex-shrink-0">
                      <div className="relative">
                        <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-50" />
                        <div className="relative w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                          {index + 1}
                        </div>
                      </div>
                    </div>

                    {/* Question Text */}
                    <h3 className="text-lg md:text-xl font-semibold text-white pr-4 group-hover:text-purple-400 transition-colors duration-200 leading-relaxed">
                      {faq.question}
                    </h3>
                  </div>

                  {/* Expand/Collapse Icon */}
                  <div
                    className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full bg-gradient-to-r from-purple-600 to-pink-600 transition-transform duration-500 ease-in-out
                               peer-checked:rotate-180 shadow-lg"
                  >
                    <i className="fas fa-chevron-down text-white text-sm" />
                  </div>
                </label>

                {/* Answer animates using grid-template-rows for smooth height transition */}
                <div
                  className="grid transition-all duration-500 ease-in-out
                             grid-rows-[0fr] peer-checked:grid-rows-[1fr]"
                >
                  <div className="overflow-hidden">
                    <div className="px-8 pb-8">
                      <div className="pl-16">
                        {' '}
                        {/* Align with question text */}
                        <div className="relative">
                          <div className="absolute -inset-2 bg-gradient-to-r from-purple-600/10 to-pink-600/10 rounded-2xl blur" />
                          <div className="relative bg-white/5 rounded-2xl p-6 border-l-4 border-purple-500">
                            <div className="flex items-start gap-4">
                              <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mt-1">
                                <i className="fas fa-lightbulb text-white text-sm" />
                              </div>
                              <p className="text-white/80 leading-relaxed text-lg">
                                {faq.answer}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Help Section - Enhanced Design */}
        <div className="mt-16">
          <div className="relative group">
            <div className="absolute -inset-2 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition duration-300" />
            <div className="relative text-center p-8 rounded-3xl bg-white/5 border border-white/10 backdrop-blur-sm">
              <div className="flex flex-col md:flex-row items-center justify-center gap-8">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-50" />
                    <div className="relative w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                      <i className="fas fa-headset text-white text-2xl" />
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="text-white font-semibold text-xl">
                      {t('stillHaveQuestionsTitle')}
                    </div>
                    <div className="text-white/60 text-sm">
                      {t('supportTeamDescription')}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="relative group/button">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl blur opacity-30 group-hover/button:opacity-60 transition duration-300" />
                    <Link href={toolUrl}>
                      <button className="relative flex items-center gap-3 px-6 py-3 bg-white/10 hover:bg-white/20 border border-white/20 rounded-xl text-white transition-all duration-200 hover:scale-105 backdrop-blur-sm">
                        <i className="fas fa-envelope text-purple-400" />
                        <span className="font-medium">
                          {t('contactSupportButton')}
                        </span>
                      </button>
                    </Link>
                  </div>

                  <div className="relative group/button">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl blur opacity-50 group-hover/button:opacity-75 transition duration-300" />
                    <Link href={toolUrl}>
                      <button className="relative flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 rounded-xl text-white transition-all duration-200 hover:scale-105 shadow-lg">
                        <i className="fas fa-comments text-white" />
                        <span className="font-medium">
                          {t('liveChatButton')}
                        </span>
                      </button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
