import { getTranslations } from 'next-intl/server'
import AnimatedCard from './AnimatedCard'

export default async function TestimonialsSection() {
  const t = await getTranslations('aiFaceSwap')
  const testimonials = [
    {
      name: t('testimonial1Name'),
      role: t('testimonial1Role'),
      avatar: '/images/user/user-1.png',
      rating: 5,
      quote: t('testimonial1Quote'),
      verified: true,
    },
    {
      name: t('testimonial2Name'),
      role: t('testimonial2Role'),
      avatar: '/images/user/user-2.png',
      rating: 5,
      quote: t('testimonial2Quote'),
      verified: true,
    },
    {
      name: t('testimonial3Name'),
      role: t('testimonial3Role'),
      avatar: '/images/user/user-3.png',
      rating: 5,
      quote: t('testimonial3Quote'),
      verified: true,
    },
    {
      name: t('testimonial4Name'),
      role: t('testimonial4Role'),
      avatar: '/images/user/user-4.png',
      rating: 5,
      quote: t('testimonial4Quote'),
      verified: true,
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('testimonialsTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('testimonialsDescription')}
          </p>
        </div>

        {/* Testimonials Grid - Enhanced Layout */}
        <div className="grid md:grid-cols-2 gap-8 lg:gap-10 mb-16">
          {testimonials.map((testimonial, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div className="relative h-full flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    {/* Quote Icon - Enhanced */}
                    <div className="relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-50" />
                      <div className="relative w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                        <i className="fas fa-quote-left text-white text-sm" />
                      </div>
                    </div>

                    {/* Stars - Enhanced */}
                    <div className="flex items-center gap-1">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <div key={i} className="relative">
                          <i className="fas fa-star text-yellow-400 text-lg drop-shadow-lg" />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Verified Badge - Enhanced */}
                  <div className="relative">
                    <div className="absolute -inset-1 bg-green-500/30 rounded-full blur opacity-50" />
                    <div className="relative flex items-center gap-2 px-3 py-1 bg-green-500/20 border border-green-500/30 rounded-full backdrop-blur-sm">
                      <i className="fas fa-check-circle text-green-400 text-sm" />
                      <span className="text-green-400 text-sm font-medium">
                        Verified
                      </span>
                    </div>
                  </div>
                </div>

                {/* Quote - Enhanced */}
                <div className="flex-1 mb-6">
                  <blockquote className="text-white/90 text-lg leading-relaxed italic relative">
                    <div className="absolute -top-2 -left-2 text-purple-400/30 text-4xl">
                      "
                    </div>
                    {testimonial.quote}
                    <div className="absolute -bottom-4 -right-2 text-purple-400/30 text-4xl">
                      "
                    </div>
                  </blockquote>
                </div>

                {/* Author - Enhanced */}
                <div className="flex items-center gap-4 pt-4 border-t border-white/10">
                  <div className="relative">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-50" />
                    <img
                      src={testimonial.avatar}
                      alt={`${testimonial.name} profile picture`}
                      className="relative w-14 h-14 rounded-full object-cover border-2 border-white/20"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="text-white font-semibold text-lg">
                      {testimonial.name}
                    </div>
                    <div className="text-purple-400 text-sm font-medium">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Bottom Stats - Enhanced Design */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10 rounded-3xl blur-xl" />
          <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">
                {t('trustedWorldwideTitle')}
              </h3>
              <p className="text-white/60">
                {t('trustedWorldwideDescription')}
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <AnimatedCard
                delay={400}
                className="text-center bg-transparent border-0 p-6"
              >
                <div className="relative mb-4">
                  <div className="absolute -inset-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-20" />
                  <div className="relative w-16 h-16 mx-auto bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                    <i className="fas fa-users text-white text-2xl" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  {t('useCase2Stat3')}
                </div>
                <div className="text-white/60 text-sm font-medium">
                  {t('happyUsersLabel')}
                </div>
              </AnimatedCard>

              <AnimatedCard
                delay={500}
                className="text-center bg-transparent border-0 p-6"
              >
                <div className="relative mb-4">
                  <div className="absolute -inset-2 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl blur opacity-20" />
                  <div className="relative w-16 h-16 mx-auto bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl flex items-center justify-center">
                    <i className="fas fa-star text-white text-2xl" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-pink-400 mb-2">
                  {t('useCase1Stat3')}
                </div>
                <div className="text-white/60 text-sm font-medium">
                  {t('averageRatingLabel')}
                </div>
              </AnimatedCard>

              <AnimatedCard
                delay={600}
                className="text-center bg-transparent border-0 p-6"
              >
                <div className="relative mb-4">
                  <div className="absolute -inset-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-20" />
                  <div className="relative w-16 h-16 mx-auto bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                    <i className="fas fa-heart text-white text-2xl" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  {t('useCase4Stat3')}
                </div>
                <div className="text-white/60 text-sm font-medium">
                  {t('satisfactionRateLabel')}
                </div>
              </AnimatedCard>

              <AnimatedCard
                delay={700}
                className="text-center bg-transparent border-0 p-6"
              >
                <div className="relative mb-4">
                  <div className="absolute -inset-2 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl blur opacity-20" />
                  <div className="relative w-16 h-16 mx-auto bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl flex items-center justify-center">
                    <i className="fas fa-clock text-white text-2xl" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-pink-400 mb-2">
                  24/7
                </div>
                <div className="text-white/60 text-sm font-medium">
                  {t('alwaysAvailableLabel')}
                </div>
              </AnimatedCard>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
