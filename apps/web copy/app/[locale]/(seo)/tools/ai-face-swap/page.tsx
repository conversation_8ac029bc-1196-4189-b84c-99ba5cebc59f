import { getTranslations } from 'next-intl/server'
import HeroSection from './components/HeroSection'
import AdvantagesSection from './components/AdvantagesSection'
import UseCasesSection from './components/UseCasesSection'
import HowToSection from './components/HowToSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'

export async function generateMetadata() {
  const t = await getTranslations('aiFaceSwap')
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      type: 'website',
      url: 'https://imggen.ai/ai-face-swap',
      images: [
        {
          url: 'https://imggen.ai/images/og-ai-face-swap.jpg',
          width: 1200,
          height: 630,
          alt: t('openGraphTitle'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: ['https://imggen.ai/images/twitter-ai-face-swap.jpg'],
    },
  }
}

export default function AIFaceSwapPage() {
  // 统一配置跳转URL
  const AI_FACE_SWAP_TOOL_URL = '/ai/face-swap'

  return (
    <div className="min-h-screen bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      {/* Font Awesome CDN */}
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        crossOrigin="anonymous"
      />

      {/* Structured Data - Matching README Schema.org JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'SoftwareApplication',
            name: 'Free AI Face Swap Online Tool | ImgGen',
            description:
              'Try our powerful free face swap to instantly put face on picture with stunningly realistic AI. Create hilarious memes and professional headshots in seconds.',
            applicationCategory: 'MultimediaApplication',
            operatingSystem: 'Web',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD',
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: '4.9',
              reviewCount: '1250',
            },
            image: 'https://imggen.ai/assets/schema-image-face-swap.jpg',
            url: 'https://imggen.ai/ai-face-swap',
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': 'https://imggen.ai/ai-face-swap',
            },
          }),
        }}
      />

      <main className="relative">
        <HeroSection toolUrl={AI_FACE_SWAP_TOOL_URL} />
        <AdvantagesSection toolUrl={AI_FACE_SWAP_TOOL_URL} />
        <UseCasesSection toolUrl={AI_FACE_SWAP_TOOL_URL} />
        <HowToSection toolUrl={AI_FACE_SWAP_TOOL_URL} />
        <TestimonialsSection />
        <FAQSection toolUrl={AI_FACE_SWAP_TOOL_URL} />
      </main>
    </div>
  )
}
