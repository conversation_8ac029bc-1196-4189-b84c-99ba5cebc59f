import { Metadata } from 'next'
import React from 'react'
import { getTranslations } from 'next-intl/server'
import Hero from './components/Hero'
import HowToUse from './components/HowToUse'
import ShowcaseExamples from './components/ShowcaseExamples'
import WhyChooseUs from './components/WhyChooseUs'
import ProductAdvantages from './components/ProductAdvantages'
import Testimonials from './components/Testimonials'
import FAQ from './components/FAQ'
import CTA from './components/CTA'
import './index.css'
import BackGroundHighlight from './components/BackGroundHighlight'

const url = 'https://imggen.ai/ai-background-replace'
const ogImage = 'https://imggen.ai/images/og-background-changer.jpg'
const twitterImage = 'https://imggen.ai/images/twitter-background-changer.jpg'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('aiBackgroundReplace')

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords').split(', '),
    authors: [{ name: 'ImgGen Team' }],
    openGraph: {
      type: 'website',
      url,
      title: t('title'),
      description: t('description'),
      siteName: 'ImgGen',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: t('openGraphAlt'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
      images: [twitterImage],
      creator: '@imggen_ai',
      site: '@imggen_ai',
    },
  }
}

const link = 'ai/ai-background-replace'

export default function AIBackgroundRemovePage() {
  return (
    <main className="flex min-h-screen flex-col bg-gradient-to-br from-blue-950/70 via-indigo-900/60 to-cyan-800/50 items-center justify-between">
      <BackGroundHighlight />
      <Hero link={link} />
      <HowToUse />
      <ShowcaseExamples />
      <WhyChooseUs link={link} />
      <ProductAdvantages />
      <Testimonials />
      <FAQ link={link} />
      <CTA link={link} />
    </main>
  )
}
