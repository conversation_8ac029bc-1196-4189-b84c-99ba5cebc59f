import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

export default function ProductAdvantages() {
  const t = useTranslations('aiBackgroundReplace')
  const advantages = [
    {
      title: t('advantage1Title'),
      description: t('advantage1Description'),
      icon: (
        <svg
          className="h-8 w-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
          />
        </svg>
      ),
      gradient: 'from-yellow-400 to-orange-500',
    },
    {
      title: t('advantage2Title'),
      description: t('advantage2Description'),
      icon: (
        <svg
          className="h-8 w-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
      ),
      gradient: 'from-green-400 to-teal-500',
    },
    {
      title: t('advantage3Title'),
      description: t('advantage3Description'),
      icon: (
        <svg
          className="h-8 w-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          />
        </svg>
      ),
      gradient: 'from-blue-400 to-purple-500',
    },
  ]

  return (
    <section className="w-full translate-x-0 bg-gradient-to-br from-slate-900/90 via-gray-900/70 to-slate-800/80 px-4 py-24 md:px-6 lg:px-8">
      <div className="mx-auto max-w-6xl translate-x-0">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white sm:text-5xl mb-4 md:mb-6 glow-text">
            {t('advantagesTitle')}
          </h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            {t('advantagesDescription')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {advantages.map((advantage, index) => (
            <AnimatedCard key={index}>
              <div key={index} className="relative group">
                <div className="glass-effect rounded-2xl md:rounded-3xl p-6 md:p-8 h-full hover-glow transition-all duration-300 group-hover:scale-105">
                  {/* Icon with gradient background */}
                  <div
                    className={`bg-gradient-to-r ${advantage.gradient} p-3 md:p-4 rounded-xl md:rounded-2xl w-fit mb-4 md:mb-6`}
                  >
                    <div className="text-white">{advantage.icon}</div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl md:text-2xl font-bold text-white mb-3 md:mb-4">
                    {advantage.title}
                  </h3>

                  <p className="text-white/70 leading-relaxed text-sm md:text-base">
                    {advantage.description}
                  </p>

                  {/* Decorative element */}
                  <div
                    className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${advantage.gradient} rounded-b-2xl md:rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}
                  ></div>
                </div>

                {/* Floating background effect */}
                <div
                  className={`absolute inset-0 bg-gradient-to-r ${advantage.gradient} opacity-5 rounded-2xl md:rounded-3xl blur-xl group-hover:opacity-10 transition-opacity duration-300 -z-10`}
                ></div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Bottom decoration */}
        <div className="flex justify-center mt-12 md:mt-16">
          <div className="flex items-center space-x-3 md:space-x-4">
            <div className="w-2.5 h-2.5 md:w-3 md:h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse"></div>
            <div
              className="w-2.5 h-2.5 md:w-3 md:h-3 bg-gradient-to-r from-green-400 to-teal-500 rounded-full animate-pulse"
              style={{ animationDelay: '0.5s' }}
            ></div>
            <div
              className="w-2.5 h-2.5 md:w-3 md:h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse"
              style={{ animationDelay: '1s' }}
            ></div>
          </div>
        </div>
      </div>
    </section>
  )
}
