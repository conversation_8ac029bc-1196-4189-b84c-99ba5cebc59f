'use client'
import { useState } from 'react'
import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

export default function ShowcaseExamples() {
  const t = useTranslations('aiBackgroundReplace')
  const categories = [
    {
      name: t('categoryPortraits'),
      examples: [
        {
          type: t('typeOriginal'),
          image: '/images/ai-background-replace/woman-before.png',
          alt: 'image of a woman-before the background is changed',
        },
        {
          type: t('typeTransparentBackground'),
          image: '/images/ai-background-replace/woman-after.png',
          alt: 'image of a woman-after the background is removed',
        },
        {
          type: t('typeNewBackground'),
          image: '/images/ai-background-replace/woman-change.png',
          alt: 'image of a woman-after the background is changed',
        },
      ],
    },
    {
      name: t('categoryProducts'),
      examples: [
        {
          type: t('typeOriginal'),
          image: '/images/ai-background-replace/lipsticks-before.png',
          alt: 'image of lipsticks-before the background is changed',
        },
        {
          type: t('typeTransparentBackground'),
          image: '/images/ai-background-replace/lipsticks-after.png',
          alt: 'image of lipsticks-after the background is removed',
        },
        {
          type: t('typeNewBackground'),
          image: '/images/ai-background-replace/lipsticks-change.png',
          alt: 'image of lipsticks-after the background is changed',
        },
      ],
    },
    {
      name: t('categoryAnimals'),
      examples: [
        {
          type: t('typeOriginal'),
          image: '/images/ai-background-replace/cat-before.png',
          alt: 'image of a cat - before the background is changed',
        },
        {
          type: t('typeTransparentBackground'),
          image: '/images/ai-background-replace/cat-after.png',
          alt: 'image of a cat - after the background is removed',
        },
        {
          type: t('typeNewBackground'),
          image: '/images/ai-background-replace/cat-change.png',
          alt: 'image of a cat - after the background is changed',
        },
      ],
    },
    {
      name: t('categoryCars'),
      examples: [
        {
          type: t('typeOriginal'),
          image: '/images/ai-background-replace/car-before.png',
          alt: 'image of a car-before the background is changed',
        },
        {
          type: t('typeTransparentBackground'),
          image: '/images/ai-background-replace/car-after.png',
          alt: 'image of a car-after the background is removed',
        },
        {
          type: t('typeNewBackground'),
          image: '/images/ai-background-replace/car-change.png',
          alt: 'image of a car-after the background is changed',
        },
      ],
    },
    {
      name: t('categoryGraphics'),
      examples: [
        {
          type: t('typeOriginal'),
          image: '/images/ai-background-replace/graphic-before.png',
          alt: 'image of a graphic-before the background is changed',
        },
        {
          type: t('typeTransparentBackground'),
          image: '/images/ai-background-replace/graphic-after.png',
          alt: 'image of a graphic-after the background is removed',
        },
        {
          type: t('typeNewBackground'),
          image: '/images/ai-background-replace/graphic-change.png',
          alt: 'image of a graphic-after the background is changed',
        },
      ],
    },
  ]
  const [activeCategory, setActiveCategory] = useState(categories[0].name)

  

  const activeExamples =
    categories.find((cat) => cat.name === activeCategory)?.examples || []

  return (
    <section className="w-full translate-x-0 bg-gradient-to-br from-indigo-950/70 via-blue-900/50 to-cyan-800/60 px-4 py-24 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white sm:text-5xl mb-4 md:mb-6 glow-text">
            {t('showcaseTitle')}
          </h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            {t('showcaseDescription')}
          </p>
        </div>

        {/* Category tabs */}
        <div className="flex flex-wrap justify-center gap-2 md:gap-4 mb-12 md:mb-16 px-2">
          {categories.map((category) => (
            <button
              key={category.name}
              onClick={() => setActiveCategory(category.name)}
              className={`px-4 md:px-6 py-2 md:py-3 rounded-full font-medium transition-all duration-300 text-sm md:text-base ${
                activeCategory === category.name
                  ? 'bg-flow-cyan text-white shadow-lg'
                  : 'glass-effect text-white/70 hover:text-white hover:bg-white/10'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>

        {/* Examples showcase */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-5xl mx-auto">
          {activeExamples.map((example, index) => (
            <AnimatedCard key={index}>
              <div key={index} className="space-y-3 md:space-y-4">
                <div className="relative group">
                  <div className="glass-effect rounded-xl md:rounded-2xl p-3 md:p-4 hover-glow transition-all duration-300">
                    <div className="aspect-square overflow-hidden rounded-lg md:rounded-xl bg-gradient-to-br from-white/10 to-white/5">
                      <img
                        src={example.image}
                        alt={example.alt}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                    </div>
                  </div>

                  {/* Type indicator */}
                  <div className="absolute top-4 md:top-6 left-4 md:left-6">
                    <div className="glass-effect rounded-full px-2 md:px-3 py-1 text-xs md:text-sm font-medium text-white">
                      {example.type}
                    </div>
                  </div>

                  {/* Progress indicator for demonstration */}
                  {index === 1 && (
                    <div className="absolute bottom-4 md:bottom-6 left-4 md:left-6 right-4 md:right-6">
                      <div className="glass-effect rounded-full p-1.5 md:p-2">
                        <div className="flex items-center gap-1.5 md:gap-2">
                          <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-green-400 rounded-full animate-pulse"></div>
                          <div className="text-xs text-white/80">
                            {t('transparent')}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {index === 2 && (
                    <div className="absolute bottom-4 md:bottom-6 left-4 md:left-6 right-4 md:right-6">
                      <div className="glass-effect rounded-full p-1.5 md:p-2">
                        <div className="flex items-center gap-1.5 md:gap-2">
                          <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                          <div className="text-xs text-white/80">
                            {t('newBackground')}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="text-center">
                  <h3 className="text-base md:text-lg font-semibold text-white mb-1">
                    {example.type}
                  </h3>
                  <p className="text-white/60 text-xs md:text-sm">
                    {index === 0 && t('originalPhoto')}
                    {index === 1 && t('backgroundRemoved')}
                    {index === 2 && t('newBackgroundApplied')}
                  </p>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Process flow visualization */}
        <div className="flex justify-center items-center mt-8 md:mt-12 space-x-4 md:space-x-8">
          <div className="hidden sm:flex items-center space-x-2 md:space-x-4">
            <div className="w-6 md:w-8 h-1 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full"></div>
            <div className="text-white/60 text-xs md:text-sm">{t('upload')}</div>
            <div className="w-6 md:w-8 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"></div>
            <div className="text-white/60 text-xs md:text-sm">{t('process')}</div>
            <div className="w-6 md:w-8 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
            <div className="text-white/60 text-xs md:text-sm">{t('download')}</div>
          </div>
        </div>
      </div>
    </section>
  )
}
