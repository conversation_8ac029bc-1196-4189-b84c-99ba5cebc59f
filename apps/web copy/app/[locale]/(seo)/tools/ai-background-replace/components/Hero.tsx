'use client'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import BeforeAfterSlider from './BeforeAfterSlider'

export default function Hero({ link = '' }) {
  const t = useTranslations('aiBackgroundReplace')
  return (
    <section className="w-full relative min-h-screen bg-aurora overflow-hidden flex items-center px-4 py-12 md:py-16 md:px-6 lg:px-8">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/50 via-indigo-900/30 to-cyan-800/40"></div>

      <div className="mx-auto max-w-7xl relative z-10">
        <div className="grid gap-12 lg:gap-16 lg:grid-cols-2 items-center">
          {/* Left side - Content */}
          <div className="space-y-5 md:space-y-6">
            <div className="space-y-4 md:space-y-5">
              <div className="inline-flex items-center gap-2 rounded-full bg-cyan-500/10 px-3 py-1.5 md:px-4 md:py-2 backdrop-blur-sm border border-cyan-500/20">
                <div className="h-2 w-2 animate-pulse rounded-full bg-cyan-400"></div>
                <span className="text-xs md:text-sm font-medium text-cyan-100">
                  {t('aiPoweredTool')}
                </span>
              </div>

              <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-[55px] lg:leading-[1] glow-text max-w-full lg:max-w-3xl">
                {t('heroTitle')}
              </h1>

              <p className="text-lg text-white/80 leading-relaxed max-w-xl">
                {t('heroDescription')}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row flex-wrap gap-3 md:gap-4 pt-2">
              <Link href={link}>
                <button className="bg-flow-cyan w-full sm:w-auto px-6 md:px-8 py-3 md:py-4 text-white font-semibold rounded-full text-base md:text-lg hover-glow transition-all duration-300">
                  {t('changeBackgroundNow')}
                </button>
              </Link>

              <Link href={link}>
                <button className="glass-effect w-full sm:w-auto px-6 md:px-8 py-3 md:py-4 text-white font-semibold rounded-full text-base md:text-lg hover:bg-white/20 transition-all duration-300">
                  {t('watchDemo')}
                </button>
              </Link>
            </div>

            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-8 pt-4">
              <div className="flex items-center gap-2">
                <svg
                  className="h-5 w-5 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-white/70 text-sm">
                  {t('noSignupRequired')}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <svg
                  className="h-5 w-5 text-green-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-white/70 text-sm">
                  {t('freeCreditsAvailable')}
                </span>
              </div>
            </div>
          </div>

          {/* Right side - Image demo */}
          <div className="relative max-lg:w-[60vw] max-lg:mx-auto max-md:w-full">
            <div className="relative animate-float">
              <BeforeAfterSlider
                className="w-full max-w-lg mx-auto shadow-2xl hover-glow"
                beforeImage="/images/product-before.png"
                afterImage="/images/product-after.png"
                beforeAlt="image of a perfume-before the background is changed"
                afterAlt="image of a perfume-after the background is changed"
                containerClassName="h-[300px] md:h-[400px] rounded-2xl"
              />
            </div>

            {/* Floating decoration elements */}
            <div className="absolute -top-4 md:-top-8 -left-4 md:-left-8 animate-pulse-slow">
              <div className="glass-effect rounded-xl md:rounded-2xl p-3 md:p-4">
                <svg
                  className="h-6 w-6 md:h-8 md:w-8 text-cyan-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
            </div>

            <div
              className="absolute -bottom-4 md:-bottom-8 -right-4 md:-right-8 animate-pulse-slow"
              style={{ animationDelay: '1s' }}
            >
              <div className="glass-effect rounded-xl md:rounded-2xl p-3 md:p-4">
                <svg
                  className="h-6 w-6 md:h-8 md:w-8 text-indigo-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
              </div>
            </div>

            <div
              className="absolute top-1/2 -right-8 md:-right-16 glass-effect rounded-lg p-2 md:p-3 animate-float hidden sm:block"
              style={{ animationDelay: '2s' }}
            >
              <div className="text-center">
                <div className="text-lg md:text-2xl font-bold text-white">2.1s</div>
                <div className="text-xs text-white/70">{t('processing')}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
