'use client'
import { Link } from '@i18n/routing'
import { useState } from 'react'
import { useTranslations } from 'next-intl'

export default function FAQ({ link = '' }: { link: string }) {
  const t = useTranslations('aiBackgroundReplace')
  const [openIndex, setOpenIndex] = useState<number | null>(0)

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer')
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer')
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer')
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer')
    },
    {
      question: t('faq5Question'),
      answer: t('faq5Answer')
    },
    {
      question: t('faq6Question'),
      answer: t('faq6Answer')
    }
  ]

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="w-full translate-x-0 bg-gradient-to-br from-gray-900/90 via-slate-800/80 to-blue-900/70 px-4 py-24 md:px-6 lg:px-8">
      <div className="mx-auto max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white sm:text-5xl mb-6 glow-text">
            {t('faqTitle')}
          </h2>
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            {t('faqDescription')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {faqs.map((faq, index) => (
            <div key={index} className="glass-effect h-fit rounded-2xl hover-glow transition-all duration-300 border border-white/10">
              <button
                onClick={() => toggleFAQ(index)}
                className="w-full text-left p-8 hover:bg-white/5 transition-colors duration-200 focus:outline-none focus:bg-white/5 rounded-2xl"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white rounded-full min-w-8 h-8 flex items-center justify-center text-sm font-bold mt-1">
                      {index + 1}
                    </div>
                    <h3 className="text-lg font-semibold text-white pr-4 leading-tight my-auto">
                      {faq.question}
                    </h3>
                  </div>
                  <div className={`text-cyan-400 transition-transform duration-200 flex-shrink-0 ${openIndex === index ? 'rotate-45' : ''}`}>
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                </div>
              </button>
              
              <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                openIndex === index ? 'max-h-96 opacity-100 pb-8' : 'max-h-0 opacity-0'
              }`}>
                <div className="px-8">
                  <div className="pl-12 border-l-2 border-cyan-400/30">
                    <p className="text-white/80 leading-relaxed mt-3">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Contact section with different design */}
        <div className="mt-20">
          <div className="glass-effect rounded-3xl p-12 text-center border border-white/10">
            <div className="flex justify-center mb-6">
              <div className="bg-gradient-to-r from-cyan-500 to-blue-600 rounded-full p-4">
                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('needMoreHelp')}
            </h3>
            <p className="text-white/70 mb-8 max-w-2xl mx-auto">
              {t('needMoreHelpDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href={link}>
                <button className="bg-flow-cyan px-8 py-3 text-white font-semibold rounded-full hover-glow transition-all duration-300">
                  {t('contactSupport')}
                </button>
              </Link>
              <Link href={link}>
                <button className="glass-effect border border-cyan-400/30 text-white px-8 py-3 rounded-full font-semibold hover:bg-white/10 transition-all duration-300">
                  {t('viewTutorials')}
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}