import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

export default function Testimonials() {
  const t = useTranslations('aiBackgroundReplace')
  const testimonials = [
    {
      name: t('testimonial1Name'),
      role: t('testimonial1Role'),
      content: t('testimonial1Content'),
      avatar: 'ET',
      rating: 5,
    },
    {
      name: t('testimonial2Name'),
      role: t('testimonial2Role'),
      content: t('testimonial2Content'),
      avatar: 'JR',
      rating: 5,
    },
    {
      name: t('testimonial3Name'),
      role: t('testimonial3Role'),
      content: t('testimonial3Content'),
      avatar: 'SM',
      rating: 5,
    },
    {
      name: t('testimonial4Name'),
      role: t('testimonial4Role'),
      content: t('testimonial4Content'),
      avatar: 'KL',
      rating: 5,
    },
    {
      name: t('testimonial5Name'),
      role: t('testimonial5Role'),
      content: t('testimonial5Content'),
      avatar: 'RB',
      rating: 5,
    },
    {
      name: t('testimonial6Name'),
      role: t('testimonial6Role'),
      content: t('testimonial6Content'),
      avatar: 'DK',
      rating: 5,
    },
  ]

  const StarIcon = () => (
    <svg className="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
    </svg>
  )

  return (
    <section className="w-full translate-x-0 bg-gradient-to-br from-indigo-950/80 via-purple-900/60 to-pink-800/70 px-4 py-24 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white sm:text-5xl mb-4 md:mb-6 glow-text">
            {t('testimonialsTitle')}
          </h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            {t('testimonialsDescription')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {testimonials.map((testimonial, index) => (
            <AnimatedCard key={index}>
              <div key={index} className="group">
                <div className="glass-effect rounded-2xl md:rounded-3xl p-6 md:p-8 h-full hover-glow transition-all duration-300 group-hover:scale-105">
                  {/* Rating */}
                  <div className="flex mb-3 md:mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <StarIcon key={i} />
                    ))}
                  </div>

                  {/* Content */}
                  <blockquote className="text-white/80 text-base md:text-lg leading-relaxed mb-4 md:mb-6">
                    "{testimonial.content}"
                  </blockquote>

                  {/* Author */}
                  <div className="flex items-center gap-3 md:gap-4">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xs md:text-sm">
                        {testimonial.avatar}
                      </span>
                    </div>
                    <div>
                      <div className="text-white font-semibold text-sm md:text-base">
                        {testimonial.name}
                      </div>
                      <div className="text-white/60 text-xs md:text-sm">
                        {testimonial.role}
                      </div>
                    </div>
                  </div>

                  {/* Decorative quote mark */}
                  <div className="absolute top-4 md:top-6 right-4 md:right-6 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                    <svg
                      className="h-6 w-6 md:h-8 md:w-8 text-white"
                      fill="currentColor"
                      viewBox="0 0 32 32"
                    >
                      <path d="M10 8v8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4V8c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8V8h-4zm16 0v8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4V8c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8V8h-4z" />
                    </svg>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Bottom stats */}
        <div className="flex justify-center mt-12 md:mt-16">
          <div className="glass-effect rounded-xl md:rounded-2xl p-6 md:p-8 max-md:w-full">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 text-center max-md:flex">
              <div>
                <div className="text-2xl md:text-3xl font-bold text-white mb-1 md:mb-2">
                  50K+
                </div>
                <div className="text-white/60 text-sm md:text-base">
                  {t('happyUsers')}
                </div>
              </div>
              <div>
                <div className="text-2xl md:text-3xl font-bold text-white mb-1 md:mb-2">
                  4.9
                </div>
                <div className="text-white/60 text-sm md:text-base">
                  {t('averageRating')}
                </div>
              </div>
              <div>
                <div className="text-2xl md:text-3xl font-bold text-white mb-1 md:mb-2">
                  1M+
                </div>
                <div className="text-white/60 text-sm md:text-base">
                  {t('photosProcessed')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
