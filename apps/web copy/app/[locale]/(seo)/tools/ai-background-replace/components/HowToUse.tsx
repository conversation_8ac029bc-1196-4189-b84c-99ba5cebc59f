import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

export default function HowToUse() {
  const t = useTranslations('aiBackgroundReplace')
  const steps = [
    {
      number: '01',
      title: t('step1Title'),
      description: t('step1Description'),
      icon: (
        <svg
          className="h-8 w-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
          />
        </svg>
      ),
      url: '/images/ai-background-replace/stunning-quality-car-before.png',
    },
    {
      number: '02',
      title: t('step2Title'),
      description: t('step2Description'),
      icon: (
        <svg
          className="h-8 w-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      ),
      url: '/images/ai-background-replace/stunning-quality-car-after.png',
    },
    {
      number: '03',
      title: t('step3Title'),
      description: t('step3Description'),
      icon: (
        <svg
          className="h-8 w-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
      ),
      url: '/images/ai-background-replace/stunning-quality-car-replace.png',
      // video: true,
    },
  ]

  return (
    <section className="w-full translate-x-0 bg-gradient-to-br from-slate-900/80 via-blue-900/60 to-indigo-900/70 px-4 py-24 md:px-6 lg:px-8">
      <div className="mx-auto max-w-6xl">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white sm:text-5xl mb-4 md:mb-6 glow-text">
            {t('howToTitle')}
          </h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            {t('howToDescription')}
          </p>
        </div>

        <div className="space-y-12 md:space-y-16">
          {steps.map((step, index) => (
            <div key={index} className="relative">
              <div className="flex flex-col lg:flex-row items-center gap-8 md:gap-12">
                {/* Step content */}
                <div
                  className={`lg:w-1/2 space-y-4 md:space-y-6 ${
                    index % 2 === 1 ? 'lg:order-2' : ''
                  }`}
                >
                  <div className="flex items-center gap-3 md:gap-4">
                    <div className="glass-effect rounded-xl md:rounded-2xl p-3 md:p-4 text-cyan-400">
                      {step.icon}
                    </div>
                    <div className="step-counter text-4xl md:text-6xl font-bold">
                      {step.number}
                    </div>
                  </div>

                  <h3 className="text-2xl md:text-3xl font-bold text-white">
                    {step.title}
                  </h3>

                  <p className="text-white/70 text-base md:text-lg leading-relaxed">
                    {step.description}
                  </p>
                </div>

                {/* Step visualization */}
                <div
                  className={`lg:w-1/2 max-lg:w-full max-md:!w-full ${
                    index % 2 === 1 ? 'lg:order-1' : ''
                  }`}
                >
                  <AnimatedCard>
                    <div className="relative">
                      <div className="glass-effect rounded-2xl md:rounded-3xl p-6 md:p-8 hover-glow">
                        <div className="bg-gradient-to-br relative from-cyan-400/20 to-blue-500/20 rounded-xl md:rounded-2xl h-60 md:h-80 flex items-center justify-center">
                          <div className="text-center z-10 absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] space-y-3 md:space-y-4">
                            <div className="mx-auto w-16 h-16 md:w-20 md:h-20 bg-white/10 rounded-full flex items-center justify-center">
                              <div className="text-white/60">{step.icon}</div>
                            </div>
                            <div className="text-white/60 font-medium text-sm md:text-base">
                              {/* Step {step.number} */}
                            </div>
                          </div>
                          {step.video ? (
                            <video
                              muted
                              autoPlay
                              preload="metadata"
                              loop
                              playsInline
                              className="w-full h-full object-cover opacity-90 rounded-2xl"
                              src="/videos/ai-background-replace/change-background.mp4"
                            ></video>
                          ) : (
                            <>
                            <img
                              className="object-contain backdrop-blur-sm z-[9] rounded-2xl absolute top-0 left-0 w-full h-full"
                              src={step.url}
                              alt=""
                            />
                            <img
                              className="object-cover blur-sm z-0 rounded-2xl absolute top-0 left-0 w-full h-full"
                              src={step.url}
                              alt=""
                            /></>
                          )}
                        </div>
                      </div>

                      {/* Connection line to next step */}
                      {index < steps.length - 1 && (
                        <div className="hidden lg:block absolute top-full left-1/2 transform -translate-x-1/2 mt-6 md:mt-8">
                          <div className="w-1 h-12 md:h-16 bg-gradient-to-b from-cyan-400 to-transparent"></div>
                        </div>
                      )}
                    </div>
                  </AnimatedCard>
                </div>
              </div>

              {/* Mobile connection line */}
              {index < steps.length - 1 && (
                <div className="lg:hidden flex justify-center mt-6 md:mt-8">
                  <div className="h-1 w-12 md:w-16 bg-gradient-to-r from-cyan-400 to-transparent"></div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
