import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

export default function WhyChooseUs({ link = '' }) {
  const t = useTranslations('aiBackgroundReplace')
  const features = [
    {
      title: t('feature1Title'),
      description: t('feature1Description'),
      buttonText: t('feature1Button'),
      icon: (
        <svg
          className="h-12 w-12"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
      ),
      gradient: 'from-cyan-400 to-blue-500',
      image: '/images/ai-background-replace/change-process.png',
      alt: 'the process of changing background',
    },
    {
      title: t('feature2Title'),
      description: t('feature2Description'),
      buttonText: t('feature2Button'),
      icon: (
        <svg
          className="h-12 w-12"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
      ),
      gradient: 'from-blue-400 to-indigo-500',
      image: '/images/ai-background-replace/refresh-exp.png',
      alt: 'refresh your image with a new look',
    },
    {
      title: t('feature3Title'),
      description: t('feature3Description'),
      buttonText: t('feature3Button'),
      icon: (
        <svg
          className="h-12 w-12"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2M9 3h10a2 2 0 012 2v12a4 4 0 01-4 4H9"
          />
        </svg>
      ),
      gradient: 'from-purple-500 to-pink-500',
      image: '/images/ai-background-replace/adjust-exp.png',
      alt: 'adjust background colors',
    },
    {
      title: t('feature4Title'),
      description: t('feature4Description'),
      buttonText: t('feature4Button'),
      icon: (
        <svg
          className="h-12 w-12"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
          />
        </svg>
      ),
      gradient: 'from-purple-400 to-pink-500',
      image: '/images/ai-background-replace/edit-exp.png',
      alt: 'edit photo background',
    },
    {
      title: t('feature5Title'),
      description: t('feature5Description'),
      buttonText: t('feature5Button'),
      icon: (
        <svg
          className="h-12 w-12"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
          />
        </svg>
      ),
      gradient: 'from-pink-400 to-red-500',
      image: '/images/ai-background-replace/different-exp.png',
      alt: 'different background styles',
    },
  ]

  return (
    <section className="w-full translate-x-0 bg-gradient-to-br from-cyan-950/70 via-indigo-900/60 to-blue-800/50 px-4 py-24 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="text-center mb-12 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white sm:text-5xl mb-4 md:mb-6 glow-text">
            {t('whyChooseTitle')}
          </h2>
          <p className="text-lg md:text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            {t('whyChooseDescription')}
          </p>
        </div>

        <div className="space-y-16 md:space-y-24">
          {features.map((feature, index) => (
            <div key={index} className="relative">
              <div
                className={`grid gap-8 md:gap-12 lg:grid-cols-2 items-center ${
                  index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
                }`}
              >
                {/* Content */}
                <div
                  className={`space-y-4 md:space-y-6 ${
                    index % 2 === 1 ? 'lg:col-start-2' : ''
                  }`}
                >
                  <div className="flex max-lg:hidden items-center gap-3 md:gap-4">
                    <div
                      className={`bg-gradient-to-r ${feature.gradient} p-3 md:p-4 rounded-xl md:rounded-2xl text-white`}
                    >
                      {feature.icon}
                    </div>
                  </div>

                  <h3 className="text-2xl md:text-3xl font-bold text-white leading-tight">
                    {feature.title}
                  </h3>

                  <p className="text-white/70 text-base md:text-lg leading-relaxed">
                    {feature.description}
                  </p>

                  <Link href={link}>
                    <button
                      className={`bg-gradient-to-r ${feature.gradient} mt-3 md:mt-4 px-5 md:px-6 py-2.5 md:py-3 text-white font-semibold rounded-full hover-glow transition-all duration-300 text-sm md:text-base`}
                    >
                      {feature.buttonText}
                    </button>
                  </Link>
                </div>

                {/* Placeholder for image */}
                <div className={`${index % 2 === 1 ? 'lg:col-start-1' : ''}`}>
                  <AnimatedCard>
                    <div className="relative">
                      <div className="glass-effect rounded-2xl md:rounded-3xl p-6 md:p-8 hover-glow">
                        <div
                          className={`bg-gradient-to-br ${feature.gradient} opacity-90 rounded-xl md:rounded-2xl h-60 md:h-80 flex items-center justify-center overflow-hidden`}
                        >
                          <img
                            src={feature.image}
                            alt={feature.alt}
                            className="w-full h-full rounded-xl md:rounded-2xl object-cover"
                          />
                        </div>
                      </div>

                      {/* Floating decoration */}
                      <div className="absolute -top-2 md:-top-4 -right-2 md:-right-4 animate-pulse-slow">
                        <div
                          className={`bg-gradient-to-r ${feature.gradient} p-2 md:p-3 rounded-full`}
                        >
                          <div className="w-3 h-3 md:w-4 md:h-4 bg-white rounded-full"></div>
                        </div>
                      </div>
                    </div>
                  </AnimatedCard>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
