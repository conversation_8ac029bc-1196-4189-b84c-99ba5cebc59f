'use client'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import BeforeAfterSlider from './BeforeAfterSlider'

export default function CTA({ link = '' }) {
  const t = useTranslations('aiBackgroundReplace')
  return (
    <section className="w-full translate-x-0 bg-gradient-to-br from-indigo-950/90 via-purple-900/70 to-pink-800/80 px-4 py-24 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Main CTA Container */}
        <div className="glass-effect rounded-2xl md:rounded-[3rem] p-8 md:p-12 lg:p-16 relative overflow-hidden border border-white/10">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-aurora opacity-30"></div>

          <div className="relative z-10">
            {/* Header Section */}
            <div className="text-center mb-12 md:mb-16">
              <div className="inline-flex items-center gap-2 bg-cyan-500/10 border border-cyan-500/20 rounded-full px-4 md:px-6 py-1.5 md:py-2 mb-6 md:mb-8">
                <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
                <span className="text-cyan-100 text-xs md:text-sm font-medium">
                  {t('transformPhotosToday')}
                </span>
              </div>

              <h2 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-6 md:mb-8 leading-tight glow-text">
                {t('ctaTitle')}
              </h2>

              <p className="text-base md:text-lg lg:text-xl text-white/80 max-w-4xl mx-auto leading-relaxed mb-8 md:mb-12">
                {t('ctaDescription')}
              </p>
            </div>

            {/* Demo Section */}
            <div className="grid lg:grid-cols-3 gap-8 md:gap-12 items-center mb-12 md:mb-16">
              {/* Before/After Demo */}
              <div className="lg:col-span-2">
                <div className="glass-effect rounded-2xl md:rounded-3xl p-4 md:p-6 lg:p-8 border border-white/20 hover-glow">
                  <div className="text-center mb-4 md:mb-6">
                    <h3 className="text-xl md:text-2xl font-bold text-white mb-1 md:mb-2">
                      {t('seeTheMagic')}
                    </h3>
                    <p className="text-white/70 text-sm md:text-base">
                      {t('dragSlider')}
                    </p>
                  </div>

                  <BeforeAfterSlider
                    hideBlur
                    className="!rounded-xl md:!rounded-2xl !border-2 !border-white/20"
                    containerClassName="!h-[250px] md:!h-[300px] !rounded-xl md:!rounded-2xl"
                    beforeImage="/images/food-before.png"
                    afterImage="/images/food-after.png"
                    beforeAlt="image of food-before the background is changed"
                    afterAlt="image of food-after the background is changed"
                  />
                </div>
              </div>

              {/* Stats & Features */}
              <div className="space-y-4 md:space-y-6">
                <div className="glass-effect rounded-xl md:rounded-2xl p-4 md:p-6 border border-white/10 hover-glow">
                  <div className="flex items-center gap-3 md:gap-4 mb-4">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg md:rounded-xl flex items-center justify-center">
                      <svg
                        className="w-5 h-5 md:w-6 md:h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13 10V3L4 14h7v7l9-11h-7z"
                        />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xl md:text-2xl font-bold text-white">
                        2.3 sec
                      </div>
                      <div className="text-white/70 text-xs md:text-sm">
                        {t('averageProcessingTime')}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="glass-effect rounded-xl md:rounded-2xl p-4 md:p-6 border border-white/10 hover-glow">
                  <div className="flex items-center gap-3 md:gap-4 mb-4">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-lg md:rounded-xl flex items-center justify-center">
                      <svg
                        className="w-5 h-5 md:w-6 md:h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xl md:text-2xl font-bold text-white">
                        99.9%
                      </div>
                      <div className="text-white/70 text-xs md:text-sm">
                        {t('accuracyRate')}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="glass-effect rounded-xl md:rounded-2xl p-4 md:p-6 border border-white/10 hover-glow">
                  <div className="flex items-center gap-3 md:gap-4 mb-4">
                    <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg md:rounded-xl flex items-center justify-center">
                      <svg
                        className="w-5 h-5 md:w-6 md:h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                        />
                      </svg>
                    </div>
                    <div>
                      <div className="text-xl md:text-2xl font-bold text-white">
                        1M+
                      </div>
                      <div className="text-white/70 text-xs md:text-sm">
                        {t('happyUsers')}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="text-center space-y-4 md:space-y-6">
              <Link href={link}>
                <button className="group bg-flow-cyan text-white text-base md:text-lg font-bold w-full sm:w-auto px-8 md:px-12 py-4 md:py-5 rounded-xl md:rounded-2xl transition-all duration-300 transform hover:scale-105 hover-glow inline-flex items-center justify-center gap-2 md:gap-3">
                  <svg
                    className="w-5 h-5 md:w-6 md:h-6 group-hover:rotate-12 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  <span>{t('startChangingBackgrounds')}</span>
                  <svg
                    className="w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 8l4 4m0 0l-4 4m4-4H3"
                    />
                  </svg>
                </button>
              </Link>

              <div className="flex flex-wrap justify-center gap-4 md:gap-6 text-xs md:text-sm text-white/70">
                <div className="flex items-center gap-2">
                  <svg
                    className="w-3 h-3 md:w-4 md:h-4 text-cyan-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {t('noSignupRequired')}
                </div>
                <div className="flex items-center gap-2">
                  <svg
                    className="w-3 h-3 md:w-4 md:h-4 text-cyan-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {t('freeCreditsAvailable')}
                </div>
                <div className="flex items-center gap-2">
                  <svg
                    className="w-3 h-3 md:w-4 md:h-4 text-cyan-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {t('hdQualityDownloads')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
