import { NavBar } from '@marketing/shared/components/NavBar'
import type { PropsWithChildren } from 'react'
import { Toaster } from 'react-hot-toast'

export default function AIBackgroundRemoveLayout({ children }: PropsWithChildren) {
  return (
    <div>
      <Toaster
        position="top-center"
        reverseOrder={false}
        toastOptions={{
          duration: 2000,
          style: {
            background: '#1e293b',
            color: '#f8fafc',
            border: '1px solid #334155',
          },
        }}
      />

      <NavBar />
      <main className="min-h-screen">{children}</main>
    </div>
  )
}