import { FC } from 'react'
import { useTranslations } from 'next-intl'

const TestimonialSection: FC<{ toolUrl: string }> = ({ toolUrl }) => {
  // 使用根级别翻译对象，与PricingSection保持一致
  const t = useTranslations('Tattoo.TestimonialSection')

  const testimonials = [
    {
      name: '<PERSON>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Jessica',
      role: t('testimonials.0.role'),
      content: t('testimonials.0.content'),
    },
    {
      name: '<PERSON>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Brian',
      role: t('testimonials.1.role'),
      content: t('testimonials.1.content'),
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Latoya',
      role: t('testimonials.2.role'),
      content: t('testimonials.2.content'),
    },
    {
      name: '<PERSON>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Marco',
      role: t('testimonials.3.role'),
      content: t('testimonials.3.content'),
    },
    {
      name: 'Emily H.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Emily',
      role: t('testimonials.4.role'),
      content: t('testimonials.4.content'),
    },
    {
      name: 'Tom W.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Tom',
      role: t('testimonials.5.role'),
      content: t('testimonials.5.content'),
    },
    {
      name: 'Rachel K.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Rachel',
      role: t('testimonials.6.role'),
      content: t('testimonials.6.content'),
    },
    {
      name: 'Andre L.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Andre',
      role: t('testimonials.7.role'),
      content: t('testimonials.7.content'),
    },
    {
      name: 'Sophia G.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sophia',
      role: t('testimonials.8.role'),
      content: t('testimonials.8.content'),
    },
  ]

  return (
    <section className="relative py-20 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="mb-4 text-4xl md:text-5xl font-bold text-center text-blue-500">
          {t('title')}
        </h2>
        <p className="text-gray-600 max-w-3xl mx-auto mb-16 text-center text-lg md:text-xl">
          {t('description')}
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.name}
              className="p-6 bg-white/50 backdrop-blur-sm rounded-xl border border-gray-100 hover:border-gray-200 transition-all hover:shadow-lg hover:shadow-purple-500/10"
            >
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  {/* 头像区域 */}
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-r from-purple-500 to-blue-500 p-[2px]">
                      <div className="w-full h-full rounded-full overflow-hidden bg-white">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    {/* 在线状态指示器 */}
                    <div className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-400 border-2 border-white"></div>
                  </div>
                  {/* 用户信息 */}
                  <div>
                    <div className="font-semibold text-gray-800">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {testimonial.role}
                    </div>
                  </div>
                </div>

                {/* 评价内容 */}
                <p className="text-gray-700">{testimonial.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
