'use client'

import React from 'react'
import { useSetAtom } from 'jotai'
import { setTattooTabAtom, TattooTabValue } from '@marketing/stores'

type ScrollButtonProps = {
  buttonText: string
  tabValue?: TattooTabValue
}

export default function ScrollButton({
  buttonText,
  tabValue = 'idea',
}: ScrollButtonProps) {
  const setTattooTab = useSetAtom(setTattooTabAtom)

  const handleClick = () => {
    const tattooGeneratorElement = document.getElementById('tattoo-generator')
    if (tattooGeneratorElement) {
      tattooGeneratorElement.scrollIntoView({ behavior: 'smooth' })
      setTattooTab(tabValue)
    }
  }

  return (
    <button
      onClick={handleClick}
      className="inline-block px-6 py-2.5 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium hover:opacity-90 transition-all hover:shadow-lg transform hover:-translate-y-0.5"
    >
      {buttonText}
    </button>
  )
}
