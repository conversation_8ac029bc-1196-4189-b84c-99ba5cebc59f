import React from 'react'
import { getTranslations } from 'next-intl/server'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './ScrollButton'

export default async function GenerationStep({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('Tattoo.GenerationStep')

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-16">
        <h2 className="mb-4 text-4xl md:text-5xl py-2 font-bold text-blue-600">
          {t('title')}
        </h2>
        <p className="text-gray-600 max-w-4xl mx-auto text-lg md:text-xl">
          {t('description')}
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-12 items-stretch">
        {/* Left side - Options */}
        <div className="space-y-8 flex flex-col justify-between">
          {/* Option 1 */}
          <div className="bg-white/80 rounded-xl p-6 backdrop-blur border border-gray-200 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white flex items-center justify-center font-bold">
                1
              </div>
              <h3 className="text-xl font-semibold text-blue-600">
                {t('option1.title')}
              </h3>
            </div>
            <p className="text-gray-700 mb-3 font-medium">
              {t('option1.subtitle')}
            </p>
            <p className="text-gray-600 mb-4">{t('option1.description')}</p>
            <ul className="text-gray-600 space-y-2 list-disc pl-4 mb-5">
              <li>{t('option1.features.0')}</li>
              <li>{t('option1.features.1')}</li>
              <li>{t('option1.features.2')}</li>
            </ul>
            <div className="flex justify-center md:justify-start">
              <ScrollButton buttonText={t('option1.button')} tabValue="idea" />
            </div>
          </div>

          {/* Option 2 */}
          <div className="bg-white/80 rounded-xl p-6 backdrop-blur border border-gray-200 hover:border-blue-500/50 transition-colors shadow-sm">
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white flex items-center justify-center font-bold">
                2
              </div>
              <h3 className="text-xl font-semibold text-blue-600">
                {t('option2.title')}
              </h3>
            </div>
            <p className="text-gray-700 mb-3 font-medium">
              {t('option2.subtitle')}
            </p>
            <p className="text-gray-600 mb-4">{t('option2.description')}</p>
            <ul className="text-gray-600 space-y-2 list-disc pl-4 mb-5">
              <li>{t('option2.features.0')}</li>
              <li>{t('option2.features.1')}</li>
              <li>{t('option2.features.2')}</li>
            </ul>
            <div className="flex justify-center md:justify-start">
              <ScrollButton
                buttonText={t('option2.button')}
                tabValue="custom"
              />
            </div>
          </div>
        </div>

        {/* Right side - Image */}
        <div className="hidden md:block">
          <div className="w-full h-full rounded-2xl overflow-hidden">
            <div
              className="w-full h-full bg-center bg-cover"
              style={{
                backgroundImage: 'url("/tattoo/phoenix.jpg")',
              }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  )
}
