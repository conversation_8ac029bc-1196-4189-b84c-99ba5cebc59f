'use client'

import React from 'react'
import { useTranslations } from 'next-intl'

export default function ScrollToTopButton() {
  const t = useTranslations('Tattoo.TattooPrompts')

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <button
      onClick={scrollToTop}
      className="inline-flex items-center px-6 py-3 text-base font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all rounded-full shadow-lg hover:shadow-xl transform duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
    >
      {t('cta.button')}
    </button>
  )
}
