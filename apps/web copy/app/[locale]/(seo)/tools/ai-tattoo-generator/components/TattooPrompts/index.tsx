import React from 'react'
import ScrollToTopButton from './ScrollToTopButton'
import { getTranslations } from 'next-intl/server'

const categories = [
  'minimalist',
  'realistic',
  'spiritual',
  'geometric',
  'nature',
] as const

const promptNumbers = [1, 2, 3, 4, 5] as const

export default async function TattooPrompts({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('Tattoo.TattooPrompts')

  return (
    <div className="w-full bg-white/50 backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 py-24">
        <div className="max-w-4xl mx-auto space-y-16">
          {/* Header Section */}
          <div className="text-center space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold text-blue-600">
              {t('header.title')}
            </h2>
            <p className="text-gray-600 text-lg leading-relaxed">
              {t('header.description')}
            </p>
          </div>

          {/* Prompt Categories */}
          <div className="space-y-12">
            {categories.map((category) => (
              <div key={category} className="space-y-4">
                <h3 className="text-2xl font-semibold text-gray-800">
                  {t(`categories.${category}.title` as const)}
                </h3>
                <p className="text-gray-600 italic">
                  {t(`categories.${category}.description` as const)}
                </p>
                <ul className="space-y-2 text-gray-700">
                  {promptNumbers.map((num) => (
                    <li
                      key={num}
                      className={`pl-4 border-l-2 ${
                        category === 'minimalist' || category === 'geometric'
                          ? 'border-blue-500'
                          : category === 'realistic' || category === 'nature'
                          ? 'border-indigo-500'
                          : 'border-purple-500'
                      }`}
                    >
                      {t(`categories.${category}.prompt${num}` as const)}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center space-y-6">
            <p className="text-lg text-gray-600">{t('cta.description')}</p>
            <ScrollToTopButton />
          </div>
        </div>
      </div>
    </div>
  )
}
