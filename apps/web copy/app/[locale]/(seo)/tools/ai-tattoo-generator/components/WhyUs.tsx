import { FC } from 'react'
import { getTranslations } from 'next-intl/server'

interface FeatureCardProps {
  icon: JSX.Element
  title: string
  subtitle: string
  description: string
  index: number
}

const IconWrapper: FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="relative w-16 h-16 mb-6 group">
    <div className="absolute inset-0 bg-gradient-to-br from-indigo-100 to-blue-50 rounded-2xl transform rotate-6 transition-all duration-300 ease-in-out group-hover:rotate-12"></div>
    <div className="absolute inset-0 bg-gradient-to-tr from-blue-50 to-white rounded-2xl transform -rotate-3 transition-all duration-300 ease-in-out group-hover:-rotate-6"></div>
    <div className="relative bg-gradient-to-br from-indigo-500 to-blue-500 text-white rounded-2xl w-full h-full flex items-center justify-center shadow-lg transition-transform duration-300 ease-in-out group-hover:scale-105">
      {children}
    </div>
    <div className="absolute -top-1 -right-1 w-3 h-3 bg-white/80 backdrop-blur-sm rounded-full shadow-sm animate-pulse"></div>
    <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-200/80 backdrop-blur-sm rounded-full animate-pulse delay-300"></div>
  </div>
)

const FeatureCard: FC<FeatureCardProps> = ({
  icon,
  title,
  subtitle,
  description,
  index,
}) => (
  <div
    className={`group bg-gradient-to-br from-white/95 to-white/90 backdrop-blur-xl rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 ease-in-out hover:-translate-y-2 animate-[fade-in_0.5s_ease-out] delay-[${
      index * 100
    }ms] border border-white/20`}
  >
    <IconWrapper>{icon}</IconWrapper>
    <h3 className="text-xl font-bold text-gray-600 mb-2">{title}</h3>
    <p className="text-lg font-semibold text-blue-600 mb-3">{subtitle}</p>
    <p className="text-gray-600 leading-relaxed">{description}</p>
  </div>
)

const features = [
  {
    icon: (
      <svg
        className="w-8 h-8 transform transition-transform duration-300 ease-in-out group-hover:scale-110"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
        />
      </svg>
    ),
    key: 'easyToUse',
  },
  {
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M13 10V3L4 14h7v7l9-11h-7z"
        />
      </svg>
    ),
    key: 'instantGeneration',
  },
  {
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
        />
      </svg>
    ),
    key: 'promptLibrary',
  },
  {
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
        />
      </svg>
    ),
    key: 'smartCustomization',
  },
  {
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
        />
      </svg>
    ),
    key: 'allDevices',
  },
  {
    icon: (
      <svg
        className="w-8 h-8"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"
        />
      </svg>
    ),
    key: 'trusted',
  },
]

async function WhyUs({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('Tattoo.WhyUs')

  return (
    <section className="relative py-20 px-4 md:px-8 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 to-blue-100/50 backdrop-blur-[2px]"></div>

      {/* Decorative elements */}
      <div className="absolute top-20 left-0 w-72 h-72 bg-blue-200/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-0 w-96 h-96 bg-indigo-200/20 rounded-full blur-3xl"></div>

      <div className="relative max-w-7xl mx-auto">
        <div className="text-center mb-16 animate-[fade-in_0.6s_ease-out] delay-200">
          <div className="inline-block p-2 px-4 mb-4 rounded-full bg-gradient-to-r from-indigo-500/10 to-blue-500/10 backdrop-blur-sm">
            <span className="text-sm font-medium text-blue-600">
              {t('badge')}
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-blue-600 mb-4 pb-1">
            {t('title')}
          </h2>
          <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={feature.key}
              icon={feature.icon}
              title={t(`features.${feature.key}.title` as any)}
              subtitle={t(`features.${feature.key}.subtitle` as any)}
              description={t(`features.${feature.key}.description` as any)}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  )
}

export default WhyUs
