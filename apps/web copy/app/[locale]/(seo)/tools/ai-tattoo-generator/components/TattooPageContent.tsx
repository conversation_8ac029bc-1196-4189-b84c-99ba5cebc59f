import Image from 'next/image'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@ui/components/accordion'
import { getTranslations } from 'next-intl/server'

const SAMPLE_TATTOOS = [
  {
    src: '/tattoo/2.jpg',
    alt: 'wolf-tattoo',
  },
  {
    src: '/tattoo/3.jpg',
    alt: 'watercolor-tattoo',
  },
  {
    src: '/tattoo/4.jpg',
    alt: 'unicorns-tattoo',
  },
  {
    src: '/tattoo/5.jpeg',
    alt: 'rose-tattoo',
  },
  {
    src: '/tattoo/6.jpeg',
    alt: 'cat-tattoo',
  },
  {
    src: '/tattoo/7.jpeg',
    alt: 'tiger-tattoo',
  },
]

const SAMPLE_PROMPTS = {
  wolf: [
    'Generate a traditional black and grey tattoo design with a wolf and a full moon, placed on the upper arm.',
    'Elegant gender-neutral wolf with defined facial features, sleek black fur, intense blue eyes, minimalistic geometric patterns, standing on rocky terrain with a crescent moon in the background, in a black and white style.',
    'Majestic male wolf with sharp facial features, rugged black fur, deep amber eyes, standing on a snowy mountaintop with distant pine trees, in a black and white style.',
    'Fierce and majestic male wolf with intense facial features, grey fur, piercing yellow eyes, tribal-inspired black tattoo markings, standing on a rocky cliff overlooking a mystical forest, under a full moon, with a thick and wild mane-like fur around the neck.',
    'Graceful gender-neutral wolf with soft facial features, subtle blue and gray fur, expressive turquoise eyes, sitting beside a tranquil forest stream with watercolor reflections, in a watercolor style.',
  ],
  butterfly: [
    'Elegant gender-neutral butterfly with defined wing patterns in black, perched on a monochrome flower with delicate petals, in a black and white style.',
    'Delicate female butterfly with soft facial features, colorful wings in shades of blue, purple, and pink, sitting on a blooming cherry blossom branch with a soft breeze.',
    'Striking gender-neutral butterfly with bold wing patterns in black, perched on a stylized black and white flower, in a black and white style.',
    'Graceful female butterfly with soft facial features, colorful wings in shades of blue, pink, and purple, perched on a blooming cherry blossom with a gentle breeze.',
    'Elegant gender-neutral butterfly with refined wing patterns in subtle shades of black, gray, and white, perched on a delicate and minimalist flower.',
  ],
  rose: [
    'Timeless gender-neutral rose tattoo with delicate facial features, a single rose with detailed petals on one side of the face, vines with thorns wrapping around the neck and a climbing rose extending onto the shoulder, with an hourglass nestled among the vines, in a tattoo style.',
    'Elegant female rose tattoo with refined facial features, a single rose with intricately detailed petals on the side of the face, vines with thorns wrapping around the neck and a climbing rose extending onto the collarbone, with a softly lit candle nestled among the vines, in a tattoo style.',
    'Classic female rose tattoo with soft facial features, a single rose with intricately detailed petals on the side of the face, vines with thorns wrapping around the neck and a climbing rose extending onto the collarbone, with a vintage pocket watch nestled among the vines, in a tattoo style.',
    'Minimalist gender-neutral rose tattoo with subtle facial features, a small rose with simple yet elegant petals on the side of the face, a delicate vine wrapping around the neck, and small leaves extending onto the collarbone, in a tattoo style.',
  ],
}

const FAQ_ITEMS = [
  {
    question: 'What is an AI Tattoo Generator?',
    answer:
      'An AI Tattoo Generator is a computer program that uses artificial intelligence to create tattoo designs based on user input, preferences, or inspirations.',
  },
  {
    question: 'How does an AI Tattoo Generator work?',
    answer:
      'AI Tattoo Generators use deep learning algorithms to analyze and generate tattoo designs. They learn from a vast database of existing tattoo art and use this knowledge to create new and unique designs.',
  },
  {
    question: 'Can I use an AI Tattoo Generator to design a custom tattoo?',
    answer:
      'Yes, you can use an AI Tattoo Generator to design a custom tattoo. Simply provide the generator with your ideas, preferences, or inspirations, and it will generate a design that matches your vision.',
  },
  {
    question: 'Can I use an AI Tattoo Generator to design a temporary tattoo?',
    answer:
      'Yes, AI-generated tattoo designs can be used for temporary tattoos. You can print the design on temporary tattoo paper and apply it to your skin.',
  },
]

export default async function TattooPageContent() {
  const t = await getTranslations()

  // Split the images into two rows
  const firstRowImages = SAMPLE_TATTOOS.slice(0, 4)
  const secondRowImages = SAMPLE_TATTOOS.slice(4)

  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[410px] w-full">
        <Image
          src="/tattoo/1.jpg"
          alt="AI Tattoo Generator"
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 to-black/60" />
        <div className="absolute inset-0 container mx-auto px-4 flex items-center justify-center">
          <div className="max-w-4xl text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-8">
              AI Tattoo Generator Online Free
            </h1>
            <p className="text-lg md:text-xl text-gray-200 leading-relaxed">
              Transform your flash tattoo ideas into reality using our AI tattoo
              generator. Break free from the norm of replicating popular tattoo
              designs and create custom tattoo designs effortlessly with the
              assistance of AI!
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 space-y-32 py-24">
        {/* Gallery Section */}
        <section className="space-y-12">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              AI-Generated Tattoo Designs
            </h2>
            <p className="text-gray-600 text-lg leading-relaxed">
              Unlock unique tattoo designs with our AI tattoo generator. Explore
              the freedom to craft black tattoos, black ink designs, and various
              text tattoo creations.
            </p>
          </div>

          <div className="grid grid-cols-4 gap-6">
            {/* First row - 4 images */}
            {firstRowImages.map((tattoo, index) => (
              <div
                key={index}
                className="aspect-square relative rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                <Image
                  src={tattoo.src}
                  alt={tattoo.alt}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            ))}

            {/* Second row - 2 images */}
            {secondRowImages.map((tattoo, index) => (
              <div
                key={index}
                className="col-span-2 aspect-square relative rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group"
              >
                <Image
                  src={tattoo.src}
                  alt={tattoo.alt}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            ))}
          </div>
        </section>

        {/* Sample Prompts Section */}
        <section className="space-y-12">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Sample Tattoo Prompts
            </h2>
            <p className="text-gray-600 text-lg leading-relaxed">
              Need some ideas to get started? Here are a few sample prompts that
              you can use with our Free Online AI Tattoo Generator:
            </p>
          </div>

          <div className="space-y-4">
            {/* Wolf Prompts */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:bg-gray-50 hover:scale-[1.02]">
              <h3 className="text-2xl font-semibold mb-6">
                Traditional Wolf Tattoo
              </h3>
              <ul className="list-decimal pl-5 space-y-4">
                {SAMPLE_PROMPTS.wolf.map((prompt, index) => (
                  <li key={index} className="text-gray-600 leading-relaxed">
                    {prompt}
                  </li>
                ))}
              </ul>
            </div>

            {/* Butterfly Prompts */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:bg-gray-50 hover:scale-[1.02]">
              <h3 className="text-2xl font-semibold mb-6">Butterfly Tattoo</h3>
              <ul className="list-decimal pl-5 space-y-4">
                {SAMPLE_PROMPTS.butterfly.map((prompt, index) => (
                  <li key={index} className="text-gray-600 leading-relaxed">
                    {prompt}
                  </li>
                ))}
              </ul>
            </div>

            {/* Rose Prompts */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:bg-gray-50 hover:scale-[1.02]">
              <h3 className="text-2xl font-semibold mb-6">Rose Tattoo</h3>
              <ul className="list-decimal pl-5 space-y-4">
                {SAMPLE_PROMPTS.rose.map((prompt, index) => (
                  <li key={index} className="text-gray-600 leading-relaxed">
                    {prompt}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="space-y-12">
          <div className="text-center max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">FAQ</h2>
            <p className="text-gray-600 text-lg leading-relaxed">
              Do you have any questions about the AI Tattoo Generator? We have
              all the answers you need.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <Accordion type="single" collapsible className="space-y-6">
              {FAQ_ITEMS.map((item, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 data-[state=open]:bg-gray-50 data-[state=open]:shadow-xl overflow-hidden"
                >
                  <AccordionTrigger className="px-8 py-6 text-xl font-semibold hover:no-underline hover:bg-gray-50/50 transition-colors group">
                    <div className="flex items-center text-left">
                      <span className="text-[#4B6BFB] font-bold mr-4 text-2xl">
                        Q{index + 1}
                      </span>
                      <span className="group-hover:text-[#4B6BFB] transition-colors">
                        {item.question}
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-8 pb-8 pt-2 text-gray-600 leading-relaxed">
                    <div className="flex">
                      <span className="text-emerald-500 font-bold mr-4 text-2xl">
                        A
                      </span>
                      <p className="pt-1">{item.answer}</p>
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </section>
      </div>
    </div>
  )
}
