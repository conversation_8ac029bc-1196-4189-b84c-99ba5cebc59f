'use client'

import { useState } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import Masonry from 'react-masonry-css'
import { useAtom } from 'jotai'
import { tattooPromptAtom, tattooTabAtom } from '@marketing/stores'
import { useTranslations } from 'next-intl'

interface TattooItem {
  id: string
  titleKey: string
  descriptionKey: string
  imageUrl: string
  prompt: string
  aspectRatio?: string
}

const tattooSamples: TattooItem[] = [
  {
    id: 'dragonSleeve',
    titleKey: 'dragonSleeve',
    descriptionKey: 'dragonSleeve',
    imageUrl: '/tattoo/dragon.jpg',
    prompt:
      'Japanese dragon sleeve tattoo, bold lines, traditional style, vibrant colors, mythical creature',
    aspectRatio: '1/1',
  },
  {
    id: 'floralLine',
    titleKey: 'floralLine',
    descriptionKey: 'floralLine',
    imageUrl: '/tattoo/rose.jpg',
    prompt:
      'Delicate floral tattoo, fine lines, minimalist style, botanical illustration',
    aspectRatio: '1/1',
  },
  {
    id: 'blackGrayRealism',
    titleKey: 'blackGrayRealism',
    descriptionKey: 'blackGrayRealism',
    imageUrl: '/tattoo/skull.jpg',
    prompt:
      'Realistic skull tattoo, black and gray shading, high detail, dramatic lighting',
    aspectRatio: '1/1',
  },
  {
    id: 'mandalaGeometric',
    titleKey: 'mandalaGeometric',
    descriptionKey: 'mandalaGeometric',
    imageUrl: '/tattoo/geometric.jpg',
    prompt:
      'Geometric mandala tattoo, sacred geometry, symmetrical design, intricate patterns',
    aspectRatio: '1/1',
  },
  {
    id: 'smallBeginners',
    titleKey: 'smallBeginners',
    descriptionKey: 'smallBeginners',
    imageUrl: '/tattoo/small.jpg',
    prompt:
      'Small butterfly tattoo, minimalist design, delicate lines, simple shading',
    aspectRatio: '1/1',
  },
  {
    id: 'realisticAnimal',
    titleKey: 'realisticAnimal',
    descriptionKey: 'realisticAnimal',
    imageUrl: '/tattoo/2.jpg',
    prompt:
      'Realistic wolf tattoo, detailed fur texture, natural pose, wilderness theme',
    aspectRatio: '1/1',
  },
]

export default function TattooSample({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations('Tattoo.TattooSample')
  const [hoveredId, setHoveredId] = useState<string | null>(null)
  const [, setPrompt] = useAtom(tattooPromptAtom)
  const [, setActiveTab] = useAtom(tattooTabAtom)

  const handlePromptClick = (item: TattooItem) => {
    setPrompt(item.prompt)
    setActiveTab('custom')
    // 平滑滚动到生成器位置
    const generator = document.getElementById('tattoo-generator')
    if (generator) {
      generator.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }

  // 瀑布流的断点设置
  const breakpointColumnsObj = {
    default: 3,
    1100: 3,
    768: 2,
    500: 1,
  }

  return (
    <section className="max-w-7xl mx-auto px-4 py-16">
      <div className="text-center mb-16">
        <h2 className="text-4xl font-bold mb-4 text-blue-600">{t('title')}</h2>
        <p className="text-gray-600 max-w-3xl mx-auto text-lg">
          {t('description')}
        </p>
      </div>

      <Masonry
        breakpointCols={breakpointColumnsObj}
        className="flex w-auto -ml-6"
        columnClassName="pl-6"
      >
        {tattooSamples.map((item) => (
          <motion.div
            key={item.id}
            className="mb-6 group"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div
                className="relative bg-gray-100 w-full"
                style={{
                  paddingBottom: item.aspectRatio
                    ? `calc(100% * (${item.aspectRatio.split('/')[1]} / ${
                        item.aspectRatio.split('/')[0]
                      }))`
                    : '100%',
                }}
              >
                <div className="absolute inset-0">
                  <div className="absolute inset-0 animate-pulse bg-gradient-to-br from-gray-200 to-gray-300" />
                  <Image
                    src={item.imageUrl}
                    alt={t(`samples.${item.titleKey}.title` as any)}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  <div
                    className={`absolute inset-0 bg-black/60 flex flex-col justify-end p-6 transition-opacity duration-300 ${
                      hoveredId === item.id ? 'opacity-100' : 'opacity-0'
                    }`}
                    onMouseEnter={() => setHoveredId(item.id)}
                    onMouseLeave={() => setHoveredId(null)}
                  >
                    <h3 className="text-white text-xl font-semibold mb-2">
                      {t(`samples.${item.titleKey}.title` as any)}
                    </h3>
                    <p className="text-gray-200 mb-4">
                      {t(`samples.${item.descriptionKey}.description` as any)}
                    </p>
                    <button
                      onClick={() => handlePromptClick(item)}
                      className="bg-white text-black px-4 py-2.5 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center gap-2 font-medium"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
                        <polyline points="10 17 15 12 10 7" />
                        <line x1="15" y1="12" x2="3" y2="12" />
                      </svg>
                      {t('useThisStyle')}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </Masonry>
    </section>
  )
}
