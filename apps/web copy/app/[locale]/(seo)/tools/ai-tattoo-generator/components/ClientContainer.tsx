// apps/web/app/[locale]/(marketing)/ai-tattoo-generator/components/ClientContainer.tsx
'use client'

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { useTranslations } from 'next-intl'
import TattooPageContent from './TattooPageContent'
//
// 动态导入ImageGenerator组件
const TattooGenerator = dynamic(() => import('./TattooGenerator'), {
  ssr: false,
  loading: () => {
    const t = useTranslations()
    return (
      <div className="w-full h-[800px] flex items-center justify-center">
        loading...
      </div>
    )
  },
})

export default function ClientContainer() {
  return (
    <div className="space-y-20">
      <TattooGenerator />
    </div>
  )
}
