// components/FaqSection.tsx
import { FC } from 'react'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@ui/components/accordion'
import { useTranslations } from 'next-intl'

const FaqSection: FC<{ toolUrl: string }> = ({ toolUrl }) => {
  const t = useTranslations()

  const faqData = [
    {
      question: t('Tattoo.FAQSection.faqData.0.question'),
      answer: t('Tattoo.FAQSection.faqData.0.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.1.question'),
      answer: t('Tattoo.FAQSection.faqData.1.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.2.question'),
      answer: t('Tattoo.FAQSection.faqData.2.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.3.question'),
      answer: t('Tattoo.FAQSection.faqData.3.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.4.question'),
      answer: t('Tattoo.FAQSection.faqData.4.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.5.question'),
      answer: t('Tattoo.FAQSection.faqData.5.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.6.question'),
      answer: t('Tattoo.FAQSection.faqData.6.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.7.question'),
      answer: t('Tattoo.FAQSection.faqData.7.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.8.question'),
      answer: t('Tattoo.FAQSection.faqData.8.answer'),
    },
    {
      question: t('Tattoo.FAQSection.faqData.9.question'),
      answer: t('Tattoo.FAQSection.faqData.9.answer'),
    },
  ]

  return (
    <section className="py-24 relative overflow-hidden">
      <div className="container max-w-4xl">
        <div className="mb-12 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-[#4B6BFB] !leading-tight">
            {t('Tattoo.FAQSection.title')}
          </h2>
          <p className="text-lg text-gray-600">
            {t('Tattoo.FAQSection.description')}
          </p>
        </div>

        <Accordion type="single" collapsible className="flex flex-col gap-3">
          {faqData.map((item, i) => (
            <AccordionItem
              key={i}
              value={`faq-item-${i}`}
              className="rounded-xl border border-[#4B6BFB]/10 bg-white/80 backdrop-blur-sm px-6 py-4 shadow-sm hover:shadow-md transition-all duration-300 hover:border-[#4B6BFB]/30"
            >
              <AccordionTrigger className="py-2 text-lg text-left flex items-center">
                <div className="flex items-center gap-3 flex-1">
                  <span className="text-gray-800">{item.question}</span>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-3 leading-6 text-gray-600">
                  <p className="whitespace-pre-line">{item.answer}</p>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
        <p className="text-center mt-8 text-gray-600">
          {t('Tattoo.FAQSection.supportText')}
        </p>
      </div>
    </section>
  )
}

export default FaqSection
