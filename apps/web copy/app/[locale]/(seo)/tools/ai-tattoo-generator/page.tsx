// apps/web/app/[locale]/(marketing)/ai-tattoo-generator/page.tsx
import { getTranslations } from 'next-intl/server'
import TattooGenerator from './components/TattooGenerator'
import GenerationStep from './components/GenerationStep'
import TattooSample from './components/TattooSample'
import TattooBrandIntro from './components/TattooBrandIntro'
import TattooPrompts from './components/TattooPrompts'
import WhyUs from './components/WhyUs'
import TestimonialSection from './components/TestimonialSection'
import CtaSection from './components/CtaSection'
import FAQSection from './components/FAQSection'
export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('Tattoo.title'),
    description: t('Tattoo.description'),
    keywords: t('Tattoo.keywords'),
  }
}

export default function CreatePage() {
  // 统一配置跳转URL
  const AI_TATTOO_GENERATOR_TOOL_URL = '/ai/tattoo'

  return (
    <main className="min-h-screen bg-gradient-to-br from-indigo-50 to-blue-100 pt-40">
      <TattooGenerator toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
      <GenerationStep toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
      <TattooSample toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
      <TattooBrandIntro toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
      <TattooPrompts toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
      <WhyUs toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
      <TestimonialSection toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
      <FAQSection toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
      <CtaSection toolUrl={AI_TATTOO_GENERATOR_TOOL_URL} />
    </main>
  )
}
