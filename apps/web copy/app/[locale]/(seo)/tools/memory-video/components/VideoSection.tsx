'use client'
import CTAButton from './CTAButton'
import MemorialVideoPlayer from './MemorialVideoPlayer'
import { useState } from 'react'
import { useTranslations } from 'next-intl'

export default function VideoSection() {
  const t = useTranslations('memoryVideo')
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  const handleWatchDemo = () => {
    // 触发视频播放
    const videoElement = document.querySelector(
      '.aspect-video video'
    ) as HTMLVideoElement
    if (videoElement) {
      if (isVideoPlaying) {
        videoElement.pause()
      } else {
        videoElement.play()
      }
    }
  }

  return (
    <div className="relative group">
      <div className="relative bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl p-4 border border-white/20 overflow-hidden">
        {/* Video Container */}
        <div className="relative aspect-video rounded-xl overflow-hidden bg-black/40">
          <MemorialVideoPlayer
            onPlay={() => setIsVideoPlaying(true)}
            onPause={() => setIsVideoPlaying(false)}
            onEnded={() => setIsVideoPlaying(false)}
          />
        </div>

        {/* Video Description and Watch Demo Button */}
        <div className="mt-4 text-center">
          <h3 className="text-white font-semibold text-lg mb-2">
            {t('seeItInAction')}
          </h3>
          <p className="text-white/70 text-sm mb-4">
            {t('watchHowAI')}
          </p>
          <CTAButton
            variant="secondary"
            size="md"
            className="w-full sm:w-auto"
            onClick={handleWatchDemo}
          >
            <i
              className={`fas ${isVideoPlaying ? 'fa-pause' : 'fa-play'} mr-2`}
            />
            {isVideoPlaying ? t('pause') : t('watchDemo')}
          </CTAButton>
        </div>

        {/* Floating Elements */}
        <div className="absolute max-md:hidden -top-2 -right-2 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
        <div className="absolute max-md:hidden -bottom-2 -left-2 w-6 h-6 bg-purple-400 rounded-full animate-pulse delay-1000"></div>
      </div>

      {/* Before Image with Curved Arrow */}
      <div className="absolute max-md:hidden -top-16 -left-10 bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-xl">
        <div className="text-center mb-3">
          <span className="text-white/90 text-sm font-semibold">
            {t('originalPhoto')}
          </span>
        </div>
        <div className="w-28 h-28 rounded-xl overflow-hidden border-2 border-white/30 shadow-lg">
          <img
            src="/samples/memorial-video-maker/memorial-video-maker-before.png"
            alt={t('originalPhoto')}
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Curved Arrow connecting Before to Video */}
      <svg
        className="absolute max-md:hidden -top-20 left-12 w-40 h-40 pointer-events-none"
        viewBox="0 0 160 160"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>
        </defs>
        <path
          d="M30 130C50 110 70 90 90 70C110 50 130 60 150 90"
          stroke="rgba(255, 255, 255, 0.9)"
          strokeWidth="2.5"
          strokeLinecap="round"
          filter="url(#glow)"
          style={{
            animation: 'breathe 3s ease-in-out infinite',
          }}
        />
        <path
          d="M150 90L142 86M150 90L148 82"
          stroke="rgba(255, 255, 255, 0.9)"
          strokeWidth="2.5"
          strokeLinecap="round"
          filter="url(#glow)"
          style={{
            animation: 'breathe 3s ease-in-out infinite',
          }}
        />
        <style>
          {`
            @keyframes breathe {
              0%, 100% { 
                opacity: 0.5;
                stroke-width: 2.5;
              }
              50% { 
                opacity: 1;
                stroke-width: 4;
              }
            }
          `}
        </style>
      </svg>
    </div>
  )
}
