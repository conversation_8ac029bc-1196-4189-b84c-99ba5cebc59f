import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

interface HowToSectionProps {
  toolUrl: string
}

export default function HowToSection({ toolUrl }: HowToSectionProps) {
  const t = useTranslations('memoryVideo')
  return (
    <section className="py-24 px-4 relative">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-purple-400">
            {t('howToSectionTitle')}
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('howToSectionDescription')}
          </p>
        </div>

        {/* Steps Grid */}
        <div className="grid md:grid-cols-2 gap-8 lg:gap-12 mb-16">
          {/* Step 1 */}
          <AnimatedCard delay={100}>
            <div className="h-full flex flex-col">
              <div className="flex items-start gap-6 mb-6">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl shadow-lg">
                    <span className="text-white text-2xl font-bold">1</span>
                  </div>
                </div>

                <div className="flex-1">
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4 leading-tight">
                    {t('howToStep1Title')}
                  </h3>
                </div>
              </div>

              <div className="flex-1">
                <p className="text-white/80 mb-8 leading-relaxed text-lg">
                  {t('howToStep1Description')}
                </p>

                {/* Feature Icons */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-upload text-blue-400 text-sm" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('howToStep1Feature1')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-user text-blue-400 text-sm" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('howToStep1Feature2')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-check text-blue-400 text-sm" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('howToStep1Feature3')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </AnimatedCard>

          {/* Step 2 */}
          <AnimatedCard delay={200}>
            <div className="h-full flex flex-col">
              <div className="flex items-start gap-6 mb-6">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl shadow-lg">
                    <span className="text-white text-2xl font-bold">2</span>
                  </div>
                </div>

                <div className="flex-1">
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4 leading-tight">
                    {t('howToStep2Title')}
                  </h3>
                </div>
              </div>

              <div className="flex-1">
                <p className="text-white/80 mb-8 leading-relaxed text-lg">
                  {t('howToStep2Description')}
                </p>

                {/* Feature Icons */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-palette text-green-400 text-sm" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('howToStep2Feature1')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-music text-green-400 text-sm" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('howToStep2Feature2')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-download text-green-400 text-sm" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('howToStep2Feature3')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Visual Process Flow */}
        <div className="mb-16">
          <div className="relative">
            <div className="grid md:grid-cols-3 gap-8 relative">
              {/* Step 1 Visual */}
              <div className="text-center">
                <div className="relative mb-6">
                  <div className="w-24 h-24 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mx-auto shadow-lg">
                    <i className="fas fa-upload text-white text-2xl" />
                  </div>
                </div>
                <h4 className="text-white font-semibold text-lg mb-2">
                  {t('howToVisual1')}
                </h4>
                <p className="text-white/60 text-sm">
                  {t('howToVisual1Description')}
                </p>
              </div>

              {/* Step 2 Visual */}
              <div className="text-center">
                <div className="relative mb-6">
                  <div className="w-24 h-24 bg-gradient-to-r from-pink-600 to-purple-600 rounded-full flex items-center justify-center mx-auto shadow-lg">
                    <i className="fas fa-magic text-white text-2xl" />
                  </div>
                </div>
                <h4 className="text-white font-semibold text-lg mb-2">
                  {t('howToVisual2')}
                </h4>
                <p className="text-white/60 text-sm">{t('howToVisual2Description')}</p>
              </div>

              {/* Step 3 Visual */}
              <div className="text-center">
                <div className="relative mb-6">
                  <div className="w-24 h-24 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center mx-auto shadow-lg">
                    <i className="fas fa-heart text-white text-2xl" />
                  </div>
                </div>
                <h4 className="text-white font-semibold text-lg mb-2">
                  {t('howToVisual3')}
                </h4>
                <p className="text-white/60 text-sm">{t('howToVisual3Description')}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <div className="max-w-2xl mx-auto">
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur opacity-25 group-hover:opacity-50 transition duration-300" />
              <div className="relative flex flex-col md:flex-row items-center justify-between gap-6 px-8 py-8 bg-gradient-to-r from-purple-600/10 to-pink-600/10 backdrop-blur-sm border border-white/20 rounded-3xl">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                    <i className="fas fa-play text-white text-xl" />
                  </div>
                  <div className="text-left">
                    <div className="text-white font-semibold text-lg">
                      {t('howToCTA')}
                    </div>
                    <div className="text-white/60 text-sm">
                      {t('howToCTADescription')}
                    </div>
                  </div>
                </div>
                <CTAButton className="flex-shrink-0" href={toolUrl}>
                  {t('howToCTAButton')}
                </CTAButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
