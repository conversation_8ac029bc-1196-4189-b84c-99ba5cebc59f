import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'
import PreviewWithVideoTooltip from './PreviewWithVideoTooltip'

interface UseCasesSectionProps {
  toolUrl: string
}

export default function UseCasesSection({ toolUrl }: UseCasesSectionProps) {
  const t = useTranslations('memoryVideo')
  return (
    <section className="py-24 px-4 relative">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-purple-400">
            {t('useCasesSectionTitle')}
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('useCasesSectionDescription')}
          </p>
        </div>

        {/* Use Cases Grid */}
        <div className="space-y-16">
          {/* Case 1: Funeral Slideshow */}
          <AnimatedCard delay={100} className="p-0 bg-transparent border-none">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* 使用PreviewWithVideoTooltip组件 */}
              <PreviewWithVideoTooltip
                imageSrc="/samples/memorial-video-maker/A-1-before.jpeg"
                videoSrc="/samples/memorial-video-maker/A-1-after.mp4"
              />

              {/* Content */}
              <div className="space-y-6">
                <h3 className="text-2xl md:text-3xl font-bold text-white leading-tight">
                  {t('useCase1Title')}
                </h3>
                <p className="text-white/80 leading-relaxed text-lg">
                  {t('useCase1Description')}
                </p>
                <CTAButton href={`${toolUrl}?sample=1`}>
                  <i className="fas fa-play mr-2" />
                  {t('useCase1Button')}
                </CTAButton>
              </div>
            </div>
          </AnimatedCard>

          {/* Case 2: Digital Tributes */}
          <AnimatedCard delay={200} className="p-0 bg-transparent border-none">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* Content */}
              <div className="space-y-6 lg:order-1">
                <h3 className="text-2xl md:text-3xl font-bold text-white leading-tight">
                  {t('useCase2Title')}
                </h3>
                <p className="text-white/80 leading-relaxed text-lg">
                  {t('useCase2Description')}
                </p>
                <CTAButton href={`${toolUrl}?sample=2`}>
                  <i className="fas fa-palette mr-2" />
                  {t('useCase2Button')}
                </CTAButton>
              </div>
              {/* 使用PreviewWithVideoTooltip组件 */}
              <div className="lg:order-2">
                <PreviewWithVideoTooltip
                  imageSrc="/samples/memorial-video-maker/A-2-before.jpeg"
                  videoSrc="/samples/memorial-video-maker/A-2-after.mp4"
                />
              </div>
            </div>
          </AnimatedCard>

          {/* Case 3: Memorial Gathering */}
          <AnimatedCard delay={300} className="p-0 bg-transparent border-none">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* Image */}
              <PreviewWithVideoTooltip
                imageSrc="/samples/memorial-video-maker/A-3-before.jpeg"
                videoSrc="/samples/memorial-video-maker/A-3-after.mp4"
              />

              {/* Content */}
              <div className="space-y-6">
                <h3 className="text-2xl md:text-3xl font-bold text-white leading-tight">
                  {t('useCase3Title')}
                </h3>
                <p className="text-white/80 leading-relaxed text-lg">
                  {t('useCase3Description')}
                </p>
                <CTAButton href={`${toolUrl}?sample=3`}>
                  <i className="fas fa-rocket mr-2" />
                  {t('useCase3Button')}
                </CTAButton>
              </div>
            </div>
          </AnimatedCard>

          {/* Case 4: Living Portrait */}
          <AnimatedCard delay={400} className="p-0 bg-transparent border-none">
            <div className="grid lg:grid-cols-2 gap-8 items-center">
              {/* Content */}
              <div className="space-y-6 lg:order-1">
                <h3 className="text-2xl md:text-3xl font-bold text-white leading-tight">
                  {t('useCase4Title')}
                </h3>
                <p className="text-white/80 leading-relaxed text-lg">
                  {t('useCase4Description')}
                </p>
                <CTAButton href={`${toolUrl}?sample=4`}>
                  <i className="fas fa-heart mr-2" />
                  {t('useCase4Button')}
                </CTAButton>
              </div>

              <div className="lg:order-2">
                <PreviewWithVideoTooltip
                  imageSrc="/samples/memorial-video-maker/A-4-before.jpeg"
                  videoSrc="/samples/memorial-video-maker/A-4-after.mp4"
                />
              </div>
            </div>
          </AnimatedCard>
        </div>
      </div>
    </section>
  )
}
