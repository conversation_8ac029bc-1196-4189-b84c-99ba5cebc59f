import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimatedCard'

interface TestimonialsSectionProps {
  toolUrl: string
}

export default function TestimonialsSection({
  toolUrl,
}: TestimonialsSectionProps) {
  const t = useTranslations('memoryVideo')

  const testimonials = [
    {
      content: t('testimonial1'),
      author: t('testimonial1Author'),
      location: t('testimonial1Location'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah',
    },
    {
      content: t('testimonial2'),
      author: t('testimonial2Author'),
      location: t('testimonial2Location'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Michael',
    },
    {
      content: t('testimonial3'),
      author: t('testimonial3Author'),
      location: t('testimonial3Location'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Jennifer',
    },
    {
      content: t('testimonial4'),
      author: t('testimonial4Author'),
      location: t('testimonial4Location'),
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=David',
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-purple-400">
            {t('testimonialsSectionTitle')}
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('testimonialsSectionDescription')}
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
          {testimonials.map((testimonial, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div className="h-full flex flex-col">
                {/* Quote Icon */}
                <div className="mb-6">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl shadow-lg">
                    <i className="fas fa-quote-left text-white text-lg" />
                  </div>
                </div>

                {/* Testimonial Content */}
                <div className="flex-1">
                  <p className="text-white/90 leading-relaxed text-lg mb-6">
                    "{testimonial.content}"
                  </p>

                  {/* Author Info */}
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-white/20">
                      <img
                        src={testimonial.avatar}
                        alt={`Avatar for ${testimonial.author}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <div className="text-white font-semibold">
                        {testimonial.author}
                      </div>
                      <div className="text-white/60 text-sm">
                        {testimonial.location}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                {t('testimonialsStat1')}
              </div>
              <div className="text-white/60">{t('testimonialsStat1Label')}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                {t('testimonialsStat2')}
              </div>
              <div className="text-white/60">{t('testimonialsStat2Label')}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                {t('testimonialsStat3')}
              </div>
              <div className="text-white/60">{t('testimonialsStat3Label')}</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                {t('testimonialsStat4')}
              </div>
              <div className="text-white/60">{t('testimonialsStat4Label')}</div>
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 text-center">
          <div className="max-w-4xl mx-auto">
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur opacity-25 group-hover:opacity-50 transition duration-300" />
              <div className="relative px-8 py-8 bg-gradient-to-r from-purple-600/10 to-pink-600/10 backdrop-blur-sm border border-white/20 rounded-3xl">
                <div className="grid md:grid-cols-3 gap-8 items-center">
                  <div className="flex items-center justify-center gap-3">
                    <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-shield-alt text-green-400 text-xl" />
                    </div>
                    <div className="text-left">
                      <div className="text-white font-semibold">
                        {t('testimonialsTrust1')}
                      </div>
                      <div className="text-white/60 text-sm">
                        {t('testimonialsTrust1Description')}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-center gap-3">
                    <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-clock text-blue-400 text-xl" />
                    </div>
                    <div className="text-left">
                      <div className="text-white font-semibold">
                        {t('testimonialsTrust2')}
                      </div>
                      <div className="text-white/60 text-sm">
                        {t('testimonialsTrust2Description')}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-center gap-3">
                    <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-heart text-purple-400 text-xl" />
                    </div>
                    <div className="text-left">
                      <div className="text-white font-semibold">
                        {t('testimonialsTrust3')}
                      </div>
                      <div className="text-white/60 text-sm">
                        {t('testimonialsTrust3Description')}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
