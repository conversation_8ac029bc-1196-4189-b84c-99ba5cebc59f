import FAQItem from './FAQItem'
import CTAButton from './CTAButton'
import { useTranslations } from 'next-intl'

interface FAQSectionProps {
  toolUrl: string
}

export default function FAQSection({ toolUrl }: FAQSectionProps) {
  const t = useTranslations('memoryVideo')
  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="container mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-purple-400">
            {t('faqSectionTitle')}
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('faqSectionDescription')}
          </p>
        </div>

        {/* FAQ Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {faqs.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              index={index}
            />
          ))}
        </div>

        {/* Additional Help Section */}
        <div className="text-center">
          <div className="max-w-4xl mx-auto">
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur opacity-25 group-hover:opacity-50 transition duration-300" />
              <div className="relative px-8 py-12 bg-gradient-to-r from-purple-600/10 to-pink-600/10 backdrop-blur-sm border border-white/20 rounded-3xl">
                <div className="max-w-2xl mx-auto">
                  <div className="mb-8">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                      <i className="fas fa-question text-white text-2xl" />
                    </div>
                    <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
                      {t('faqHelpTitle')}
                    </h3>
                    <p className="text-white/80 text-lg">
                      {t('faqHelpDescription')}
                    </p>
                  </div>

                  <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                    <CTAButton
                      size="lg"
                      href="mailto:<EMAIL>"
                      target="_blank"
                    >
                      <i className="fas fa-envelope mr-2" />
                      {t('faqHelpButton1')}
                    </CTAButton>
                    <CTAButton variant="secondary" size="lg" href={toolUrl}>
                      <i className="fas fa-play mr-2" />
                      {t('faqHelpButton2')}
                    </CTAButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Final CTA */}
        <div className="text-center mt-16">
          <div className="max-w-2xl mx-auto">
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur opacity-25 group-hover:opacity-50 transition duration-300" />
              <div className="relative flex flex-col md:flex-row items-center justify-between gap-6 px-8 py-8 bg-gradient-to-r from-purple-600/10 to-pink-600/10 backdrop-blur-sm border border-white/20 rounded-3xl">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 flex-shrink-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                    <i className="fas fa-heart text-white text-xl" />
                  </div>
                  <div className="text-left">
                    <div className="text-white font-semibold text-lg">
                      {t('faqCTA')}
                    </div>
                    <div className="text-white/60 text-sm">
                      {t('faqCTADescription')}
                    </div>
                  </div>
                </div>
                <CTAButton className="flex-shrink-0" href={toolUrl}>
                  {t('faqCTAButton')}
                </CTAButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
