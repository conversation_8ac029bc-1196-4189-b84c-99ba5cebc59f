'use client'

import { useState } from 'react'
import AnimatedCard from './AnimatedCard'

interface FAQItemProps {
  question: string
  answer: string
  index: number
}

export default function FAQItem({ question, answer, index }: FAQItemProps) {
  const [isOpen, setIsOpen] = useState(false)

  const toggleFAQ = () => {
    setIsOpen(!isOpen)
  }

  return (
    <AnimatedCard delay={index * 100}>
      <div className="h-full">
        <button
          onClick={toggleFAQ}
          className="w-full text-left focus:outline-none"
        >
          <div className="flex items-start justify-between gap-4">
            <h3 className="text-xl md:text-2xl font-bold text-white leading-tight">
              {question}
            </h3>
            <div className="flex-shrink-0">
              <div
                className={`w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center transition-transform duration-300 ${
                  isOpen ? 'rotate-45' : ''
                }`}
              >
                <i className="fas fa-plus text-white text-sm" />
              </div>
            </div>
          </div>
        </button>

        <div
          className={`mt-6 transition-all duration-300 overflow-hidden ${
            isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}
        >
          <p className="text-white/80 leading-relaxed text-lg">{answer}</p>
        </div>
      </div>
    </AnimatedCard>
  )
}
