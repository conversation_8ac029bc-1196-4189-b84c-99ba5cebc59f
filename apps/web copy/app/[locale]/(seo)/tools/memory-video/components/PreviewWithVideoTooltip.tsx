'use client'
import React, { useState, useRef, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { useTranslations } from 'next-intl'

interface PreviewWithVideoTooltipProps {
  imageSrc: string
  videoSrc: string
}

const PreviewWithVideoTooltip: React.FC<PreviewWithVideoTooltipProps> = ({
  imageSrc,
  videoSrc,
}) => {
  const t = useTranslations('memoryVideo')
  const [showPreview, setShowPreview] = useState(false)
  const [showMobileModal, setShowMobileModal] = useState(false)
  const hideTimer = useRef<NodeJS.Timeout | null>(null)

  const handleMouseEnter = () => {
    if (hideTimer.current) clearTimeout(hideTimer.current)
    setShowPreview(true)
  }
  const handleMouseLeave = () => {
    hideTimer.current = setTimeout(() => setShowPreview(false), 200)
  }

  // 移动端弹窗（Portal实现）
  const MobileVideoModal = ({
    open,
    onClose,
    videoSrc,
  }: {
    open: boolean
    onClose: () => void
    videoSrc: string
  }) => {
    if (!open) return null
    return createPortal(
      <div
        className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/70 md:hidden"
        onClick={onClose}
      >
        <div
          className="bg-black p-4 rounded-xl shadow-xl border border-purple-400 max-w-xs w-full relative"
          onClick={(e) => e.stopPropagation()}
        >
          <button
            className="absolute top-2 right-2 text-white text-xl"
            onClick={onClose}
          >
            ×
          </button>
          <video
            src={videoSrc}
            controls
            autoPlay
            muted
            loop
            className="rounded-lg w-full bg-black"
            style={{ maxHeight: '300px' }}
          />
        </div>
      </div>,
      typeof window !== 'undefined'
        ? document.body
        : document.createElement('div')
    )
  }

  useEffect(() => {
    if (showMobileModal) {
      const originalOverflow = document.body.style.overflow
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = originalOverflow
      }
    }
  }, [showMobileModal])

  return (
    <div className="relative flex items-center justify-center w-full">
      <img
        src={imageSrc}
        className="rounded-xl w-full shadow-lg border border-white/20"
      />
      {/* 预览按钮，居中下方 */}
      <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-10">
        <div
          className="relative group/preview"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <button
            className="relative inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-500/50 active:scale-95 overflow-hidden group bg-white/60 hover:bg-white/80 text-purple-700 shadow-lg hover:shadow-xl px-6 py-3 text-base border border-purple-400"
            type="button"
            onClick={() => {
              if (typeof window !== 'undefined' && window.innerWidth < 768) {
                setShowMobileModal(true)
              } else {
                setShowPreview(true)
              }
            }}
          >
            {/* Shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
            {/* Content */}
            <span className="relative flex items-center gap-2">
              <i className="fas fa-play" />
              {t('previewVideo')}
            </span>
          </button>
          {/* PC端hover时的tooltip */}
          {showPreview && (
            <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-1 block md:block animate-fade-in z-20">
              <div className="bg-black/90 p-2 rounded-xl shadow-xl border border-purple-400 min-w-[240px] max-w-xs">
                <video
                  src={videoSrc}
                  controls
                  autoPlay
                  muted
                  loop
                  className="rounded-lg w-full bg-black"
                  style={{ maxHeight: '200px' }}
                />
              </div>
            </div>
          )}
        </div>
      </div>
      {/* 移动端弹窗 */}
      <MobileVideoModal
        open={showMobileModal}
        onClose={() => setShowMobileModal(false)}
        videoSrc={videoSrc}
      />
    </div>
  )
}

export default PreviewWithVideoTooltip
