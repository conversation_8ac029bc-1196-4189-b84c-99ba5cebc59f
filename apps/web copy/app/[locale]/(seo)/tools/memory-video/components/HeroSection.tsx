import { useTranslations } from 'next-intl'
import CTAButton from './CTAButton'
import VideoSection from './VideoSection'
import { getTranslations } from 'next-intl/server'

interface HeroSectionProps {
  toolUrl: string
}

export default function HeroSection({ toolUrl }: HeroSectionProps) {
  const TRANSFORMATION_URL = toolUrl
  const t = useTranslations('memoryVideo')

  return (
    <section className="relative overflow-hidden h-screen max-md:min-h-screen max-md:h-auto bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764] max-md:pt-20">
      {/* Floating elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-purple-500/20 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-pink-500/20 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-500/20 rounded-full blur-xl animate-pulse delay-500"></div>

      <div className="relative z-10 px-4 sm:px-6 lg:px-8 py-8 lg:py-12 max-md:pt-12">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-[calc(100vh-120px)] max-md:min-h-[calc(100vh-160px)]">
            {/* Left Content */}
            <div className="text-center lg:text-left">
              {/* Badge */}
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-6">
                <i className="fas fa-heart text-purple-400" />
                <span>{t('heroSectionBadge')}</span>
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-snug">
                {t('heroSectionTitle')}
              </h1>

              <p className="text-xl text-white/80 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0">
                {t('heroSectionDescription')}
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                <CTAButton
                  size="lg"
                  className="w-full sm:w-auto"
                  href={TRANSFORMATION_URL}
                >
                  <i className="fas fa-upload mr-2" />
                  {t('heroSectionButton')}
                </CTAButton>
              </div>

              {/* Stats */}
              <div className="flex justify-center lg:justify-start space-x-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{t('heroSectionStat1')}</div>
                  <div className="text-white/60 text-sm">
                    {t('heroSectionStat1Label')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{t('heroSectionStat2')}</div>
                  <div className="text-white/60 text-sm">{t('heroSectionStat2Label')}</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{t('heroSectionStat3')}</div>
                  <div className="text-white/60 text-sm">{t('heroSectionStat3Label')}</div>
                </div>
              </div>
            </div>

            {/* Right Video Section */}
            <VideoSection />
          </div>
        </div>
      </div>
    </section>
  )
}
