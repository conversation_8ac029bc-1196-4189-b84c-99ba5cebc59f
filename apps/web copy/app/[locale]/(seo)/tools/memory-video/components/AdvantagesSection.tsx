import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

interface AdvantagesSectionProps {
  toolUrl: string
}

export default function AdvantagesSection({ toolUrl }: AdvantagesSectionProps) {
  const t = useTranslations('memoryVideo')
  return (
    <section className="py-24 px-4 relative">
      <div className="container mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6 text-purple-400">
            {t('advantagesSectionTitle')}
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('advantagesSectionDescription')}
          </p>
        </div>

        {/* Advantages Grid - Enhanced Layout */}
        <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
          {/* Advantage 1: AI-Powered Technology - H3 with exact README content */}
          <AnimatedCard delay={100}>
            <div className="h-full flex flex-col justify-between">
              <div className="flex items-start gap-6 mb-6">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl shadow-lg">
                    <i className="fas fa-brain text-white text-xl" />
                  </div>
                </div>

                <div className="flex-1">
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4 leading-tight">
                    {t('advantage1Title')}
                  </h3>
                </div>
              </div>

              <div>
                <p className="text-white/80 mb-8 leading-relaxed text-lg">
                  {t('advantage1Description')}
                </p>

                {/* Feature Icons - Enhanced Design */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-check text-green-400 text-base" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('advantage1Feature1')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-check text-green-400 text-base" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('advantage1Feature2')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-check text-green-400 text-base" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('advantage1Feature3')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </AnimatedCard>

          {/* Advantage 2: Customizable Templates - H3 with exact README content */}
          <AnimatedCard delay={200}>
            <div className="h-full flex flex-col justify-between">
              <div className="flex items-start gap-6 mb-6">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl shadow-lg">
                    <i className="fas fa-palette text-white text-xl" />
                  </div>
                </div>

                <div className="flex-1">
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-4 leading-tight">
                    {t('advantage2Title')}
                  </h3>
                </div>
              </div>

              <div>
                <p className="text-white/80 mb-8 leading-relaxed text-lg">
                  {t('advantage2Description')}
                </p>

                {/* Feature Icons - Enhanced Design */}
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-check text-green-400 text-base" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('advantage2Feature1')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-check text-green-400 text-base" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('advantage2Feature2')}
                    </span>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-white/5 rounded-xl border border-white/10">
                    <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
                      <i className="fas fa-check text-green-400 text-base" />
                    </div>
                    <span className="text-white/90 font-medium">
                      {t('advantage2Feature3')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </AnimatedCard>
        </div>

        {/* Bottom CTA - Enhanced Design */}
        <div className="text-center mt-16">
          <div className="max-w-2xl mx-auto">
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur opacity-25 group-hover:opacity-50 transition duration-300" />
              <div className="relative flex flex-col md:flex-row items-center justify-between gap-6 px-8 py-8 bg-gradient-to-r from-purple-600/10 to-pink-600/10 backdrop-blur-sm border border-white/20 rounded-3xl">
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                    <i className="fas fa-heart text-white text-xl" />
                  </div>
                  <div className="text-left">
                    <div className="text-white font-semibold text-lg">
                      {t('advantagesSectionCTA')}
                    </div>
                    <div className="text-white/60 text-sm">
                      {t('advantagesSectionCTADescription')}
                    </div>
                  </div>
                </div>
                <CTAButton className="flex-shrink-0" href={toolUrl}>
                  {t('advantagesSectionCTAButton')}
                </CTAButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
