import { getTranslations } from 'next-intl/server'
import HeroSection from './components/HeroSection'
import AdvantagesSection from './components/AdvantagesSection'
import UseCasesSection from './components/UseCasesSection'
import HowToSection from './components/HowToSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'
import { useTranslations } from 'next-intl'

export async function generateMetadata() {
  const t = await getTranslations('memoryVideo')
  return {
    title: t('pageTitle'),
    description: t('pageDescription'),
    keywords: t('pageKeywords'),
    openGraph: {
      title: t('pageTitle'),
      description: t('pageDescription'),
      type: 'website',
      url: 'https://imggen.ai/memorial-video-maker',
      images: [
        {
          url: 'https://imggen.ai/assets/og-memorial-video.jpg',
          width: 1200,
          height: 630,
          alt: t('pageTitle'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('pageTitle'),
      description: t('pageDescription'),
      images: ['https://imggen.ai/assets/twitter-memorial-video.jpg'],
    },
  }
}

export default function MemorialVideoMakerPage() {
  // 统一配置跳转URL
  const MEMORY_VIDEO_TOOL_URL = '/ai/memory-video'
  const t = useTranslations('memoryVideo')

  return (
    <div className="bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      {/* Font Awesome CDN */}
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        crossOrigin="anonymous"
      />

      {/* Structured Data - Matching README Schema.org JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'SoftwareApplication',
            name: t('pageTitle'),
            description: t('pageDescription'),
            applicationCategory: 'MultimediaApplication',
            operatingSystem: 'Web',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD',
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: '4.9',
              reviewCount: '1250',
            },
            image: 'https://imggen.ai/assets/schema-image-memorial-video.jpg',
            url: 'https://imggen.ai/memorial-video-maker',
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': 'https://imggen.ai/memorial-video-maker',
            },
          }),
        }}
      />

      <main className="relative">
        <HeroSection toolUrl={MEMORY_VIDEO_TOOL_URL} />
        <AdvantagesSection toolUrl={MEMORY_VIDEO_TOOL_URL} />
        <UseCasesSection toolUrl={MEMORY_VIDEO_TOOL_URL} />
        <HowToSection toolUrl={MEMORY_VIDEO_TOOL_URL} />
        <TestimonialsSection toolUrl={MEMORY_VIDEO_TOOL_URL} />
        <FAQSection toolUrl={MEMORY_VIDEO_TOOL_URL} />
      </main>
    </div>
  )
}
