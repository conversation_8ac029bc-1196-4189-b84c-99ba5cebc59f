import React from 'react'
import HeroSection from './components/HeroSection'
import FeatureSection from './components/FeatureSection'
import CaseStudySection from './components/CaseStudySection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations('upscale')
  return {
    title: t('pageTitle'),
    description: t('pageDescription'),
    keywords: t('pageKeywords'),
    openGraph: {
      title: t('pageTitle'),
      description: t('pageDescription'),
      url: 'https://imggen.org/tools/upscale',
      type: 'website',
      images: [
        {
          url: 'https://imggen.org/images/ai-image-upscaler-preview.png',
          width: 1200,
          height: 630,
          alt: t('pageTitle'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('pageTitle'),
      description: t('pageDescription'),
      images: ['https://imggen.org/images/ai-image-upscaler-preview.png'],
    },
  }
}

const UpscalePhotoPage = async () => {
  const t = await getTranslations('upscale')

  // JSON-LD Schema
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('pageTitle'),
    description: t('pageDescription'),
    applicationCategory: 'PhotoApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    image: 'https://imggen.org/images/ai-image-upscaler-preview.png',
    keywords: t('pageKeywords').split(', '),
  }

  // 统一配置跳转URL
  const UPSCALE_TOOL_URL = '/ai/upscale'

  return (
    <div className="relative bg-[#0f172a]">
      {/* Add JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)]"></div>
      {/* Animated background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1),rgba(255,255,255,0)_50%)]"></div>
        <div
          className="absolute h-[200px] w-[400px] bg-purple-500 rounded-full blur-[100px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse"
          style={{ animationDuration: '6s' }}
        ></div>
        <div
          className="absolute h-[150px] w-[300px] bg-pink-500 rounded-full blur-[80px] top-1/4 left-1/4 animate-pulse"
          style={{ animationDuration: '8s', animationDelay: '2s' }}
        ></div>
        <div
          className="absolute h-[180px] w-[350px] bg-fuchsia-500 rounded-full blur-[90px] bottom-1/4 right-1/4 animate-pulse"
          style={{ animationDuration: '7s', animationDelay: '4s' }}
        ></div>
      </div>

      <HeroSection toolUrl={UPSCALE_TOOL_URL} />
      <FeatureSection toolUrl={UPSCALE_TOOL_URL} />
      <CaseStudySection toolUrl={UPSCALE_TOOL_URL} />
      <HowToGuideSection toolUrl={UPSCALE_TOOL_URL} />
      <TestimonialSection toolUrl={UPSCALE_TOOL_URL} />
      <FAQSection toolUrl={UPSCALE_TOOL_URL} />
    </div>
  )
}

export default UpscalePhotoPage
