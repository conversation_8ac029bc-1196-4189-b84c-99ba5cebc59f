import React from 'react'
import { UploadCloud, Settings, Download } from 'lucide-react'
import StepsCarouselClient from './StepsCarouselClient'
import { getTranslations } from 'next-intl/server'

export interface Step {
  icon: React.ReactNode
  title: string
  description: string
  image: string
}

const StepsCarousel = async () => {
  const t = await getTranslations('upscale')

  const steps: Step[] = [
    {
      icon: <UploadCloud className="w-10 h-10 text-[#339bfa]" />,
      title: t('step1Title'),
      description: t('step1Description'),
      image: 'https://picsum.photos/id/15/200/300',
    },
    {
      icon: <Settings className="w-10 h-10 text-pink-400" />,
      title: t('step2Title'),
      description: t('step2Description'),
      image: 'https://picsum.photos/id/16/200/300',
    },
    {
      icon: <Download className="w-10 h-10 text-fuchsia-500" />,
      title: t('step3Title'),
      description: t('step3Description'),
      image: 'https://picsum.photos/id/17/200/300',
    },
  ]

  return <StepsCarouselClient steps={steps} />
}

export default StepsCarousel
