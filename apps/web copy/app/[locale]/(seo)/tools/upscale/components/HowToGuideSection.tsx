import React from 'react'
import <PERSON>Carousel from './StepsCarousel'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

export default async function HowToGuideSection({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('upscale')

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('howToTitle')}
        </h2>

        <StepsCarousel />

        <div className="mt-24 text-center">
          <div className="relative group inline-block w-full md:w-auto">
            <Link href={toolUrl}>
              <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 w-full md:w-auto inline-block px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20">
                {t('howToButton')}
              </button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
