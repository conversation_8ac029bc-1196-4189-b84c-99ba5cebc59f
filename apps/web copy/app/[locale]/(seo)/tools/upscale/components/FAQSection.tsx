import React from 'react'
import FAQClient from './FAQClient'
import { getTranslations } from 'next-intl/server'

const FAQSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('upscale')

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
    {
      question: t('faq5Question'),
      answer: t('faq5Answer'),
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('faqTitle')}
        </h2>

        <FAQClient faqs={faqs} />
      </div>
    </section>
  )
}

export default FAQSection
