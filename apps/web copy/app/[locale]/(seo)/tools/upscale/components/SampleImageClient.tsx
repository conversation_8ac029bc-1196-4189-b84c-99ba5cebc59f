'use client'
import { Tilt } from '@jdion/tilt-react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'

export default function SampleImageClient() {
  const t = useTranslations('upscale')

  return (
    <div className="mt-8">
      <p className="text-gray-400 mb-4">{t('sampleImagePrompt')}</p>
      <div className="flex gap-2">
        {[
          'upscale_sample_1.webp',
          'upscale_sample_2.webp',
          'upscale_sample_3.webp',
          'upscale_sample_4.webp',
        ].map((item) => (
          <Tilt key={item} className="w-24">
            <div
              key={item}
              className="relative max-md:w-20 aspect-square w-24 rounded-lg overflow-hidden cursor-pointer border-2 border-transparent transition-all duration-300 hover:border-purple-500 hover:[transform:rotateX(10deg)_rotateY(-10deg)_scale(1.1)] hover:shadow-2xl hover:shadow-purple-500/30"
            >
              <Image
                src={`/samples/${item}`}
                alt={t('sampleImageAlt')}
                fill
                className="object-cover bg-slate-800"
              />
            </div>
          </Tilt>
        ))}
      </div>
    </div>
  )
}
