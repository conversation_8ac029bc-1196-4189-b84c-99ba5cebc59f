import React from 'react'
import { Upload, <PERSON>, Zap } from 'lucide-react'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

const FeatureSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('upscale')

  const features = [
    {
      icon: <Upload className="w-12 h-12 text-[#339bfa]" />,
      title: t('feature1Title'),
      description: t('feature1Description'),
      gradient: 'from-blue-500/20 to-cyan-500/20',
      borderColor: 'border-blue-500/30',
      iconBg: 'bg-gradient-to-br from-blue-500/20 to-cyan-500/20',
    },
    {
      icon: <Brain className="w-12 h-12 text-pink-400" />,
      title: t('feature2Title'),
      description: t('feature2Description'),
      gradient: 'from-pink-500/20 to-purple-500/20',
      borderColor: 'border-pink-500/30',
      iconBg: 'bg-gradient-to-br from-pink-500/20 to-purple-500/20',
    },
    {
      icon: <Zap className="w-12 h-12 text-fuchsia-400" />,
      title: t('feature3Title'),
      description: t('feature3Description'),
      gradient: 'from-fuchsia-500/20 to-purple-500/20',
      borderColor: 'border-fuchsia-500/30',
      iconBg: 'bg-gradient-to-br from-fuchsia-500/20 to-purple-500/20',
    },
  ]

  return (
    <section className="py-16 relative">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            {t('featureTitle')}
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto">
            {t('featureDescription')}
          </p>
        </div>

        <div className="space-y-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`relative overflow-hidden rounded-2xl border ${feature.borderColor} transition-all duration-500 hover:scale-[1.01] hover:shadow-xl`}
              style={{
                animation: `fade-in-up 0.6s ${index * 0.3}s ease-out both`,
              }}
            >
              {/* Background gradient */}
              <div
                className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} opacity-50`}
              ></div>

              <div className="relative z-10 p-6 md:p-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
                  {/* Left side - Icon and title */}
                  <div className="flex items-start space-x-4">
                    <div
                      className={`flex-shrink-0 p-3 rounded-xl ${feature.iconBg} border ${feature.borderColor}`}
                    >
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl md:text-2xl font-bold text-white leading-tight">
                        {feature.title}
                      </h3>
                    </div>
                  </div>

                  {/* Right side - Description */}
                  <div className="text-gray-300 text-sm leading-relaxed space-y-3">
                    {feature.description.split('\n').map((paragraph, i) => (
                      <p key={i}>{paragraph}</p>
                    ))}
                  </div>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/5 to-transparent rounded-full -translate-y-10 translate-x-10"></div>
              <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-white/5 to-transparent rounded-full translate-y-8 -translate-x-8"></div>
            </div>
          ))}
        </div>

        {/* Call to action */}
        <div className="text-center mt-12">
          <div className="relative group inline-block w-full md:w-auto">
            <Link href={toolUrl}>
              <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 w-full md:w-auto inline-block px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20">
                {t('featureButton')}
              </button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeatureSection
