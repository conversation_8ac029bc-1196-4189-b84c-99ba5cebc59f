import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import BeforeAfterSlider from './BeforeAfterSlider'
import AnimatedCard from './AnimateCard'

export default function Features({ link = '' }) {
  const t = useTranslations('aiImageBackgroundRemover')
  
  return (
    <section className="w-full bg-gradient-to-br from-purple-700/30 via-purple-700/30 to-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <h2 className="mb-4 text-center text-3xl font-bold text-white sm:text-4xl">
        {t('featuresTitle')}
      </h2>
      <p className="mx-auto translate-x-0 mb-12 max-w-3xl text-center text-lg text-white/30">
        {t('featuresDescription')}
      </p>
      <div className="mx-auto max-w-7xl space-y-24 translate-x-0">
        <div className="grid gap-8 lg:grid-cols-2">
          <AnimatedCard>
            <BeforeAfterSlider
              beforeAlt="image of a woman-before the background is removed"
              afterAlt="image of a woman-after the background is removed"
              beforeImage="/images/woman-before.png"
              afterImage="/images/woman-after.png"
            />
          </AnimatedCard>

          <div className="flex flex-col justify-center">
            <h3 className="mb-4 text-2xl font-bold text-white">
              {t('feature1Title')}
            </h3>
            <p className="mb-6 text-white/30">
              {t('feature1Description')}
            </p>
            <Link
              href={link}
              className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-sm relative overflow-hidden w-fit"
            >
              {t('feature1Button')}
            </Link>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          <div className="flex flex-col justify-center">
            <h3 className="mb-4 text-2xl font-bold text-white">
              {t('feature2Title')}
            </h3>
            <p className="mb-6 text-white/30">
              {t('feature2Description')}
            </p>
            <Link
              href={link}
              className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-sm relative overflow-hidden w-fit"
            >
              {t('feature2Button')}
            </Link>
          </div>
          <AnimatedCard>
            <BeforeAfterSlider
              beforeAlt="image of a signature-before the background is removed"
              afterAlt="image of a signature-after the background is removed"
              beforeImage="/images/signature-before.png"
              afterImage="/images/signature-after.png"
            />
          </AnimatedCard>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          <AnimatedCard>
            <BeforeAfterSlider
              beforeAlt="image of a tomatoes-before the background is changed"
              afterAlt="image of a tomatoes-after the background is changed"
              beforeImage="/images/tomatoes-before.png"
              afterImage="/images/tomatoes-after.png"
            />
          </AnimatedCard>
          <div className="flex flex-col justify-center">
            <h3 className="mb-4 text-2xl font-bold text-white">
              {t('feature3Title')}
            </h3>
            <p className="mb-6 text-white/30">
              {t('feature3Description')}
            </p>
            <Link
              href={link}
              className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-sm relative overflow-hidden w-fit"
            >
              {t('feature3Button')}
            </Link>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-2">
          <div className="flex flex-col justify-center">
            <h3 className="mb-4 text-2xl font-bold text-white">
              {t('feature4Title')}
            </h3>
            <p className="mb-6 text-white/30">
              {t('feature4Description')}
            </p>
            <Link
              href={link}
              className="group will-change-auto bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-sm relative overflow-hidden w-fit"
            >
              {t('feature4Button')}
            </Link>
          </div>
          <AnimatedCard>
            <BeforeAfterSlider
              beforeAlt="image of an owl-before the background is changed"
              afterAlt="image of a owl-after the background is changed"
              beforeImage="/images/owl-before.png"
              afterImage="/images/owl-after.png"
            />
          </AnimatedCard>
        </div>
      </div>
    </section>
  )
}
