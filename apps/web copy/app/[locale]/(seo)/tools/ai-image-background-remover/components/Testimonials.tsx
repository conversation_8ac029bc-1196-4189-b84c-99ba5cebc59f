import { useTranslations } from 'next-intl'

export default function Testimonials() {
  const t = useTranslations('aiImageBackgroundRemover')

  const testimonials = [
    {
      content: t('testimonial1Content'),
      author: t('testimonial1Author'),
      role: t('testimonial1Role'),
      avatar: '//sb.kaleidousercontent.com/67418/160x160/0337b3fbf1/techcrunch_author.jpg',
      rating: 5,
    },
    {
      content: t('testimonial2Content'),
      author: t('testimonial2Author'),
      role: t('testimonial2Role'),
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      rating: 5,
    },
    {
      content: t('testimonial3Content'),
      author: t('testimonial3Author'),
      role: t('testimonial3Role'),
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      rating: 5,
    },
    {
      content: t('testimonial4Content'),
      author: t('testimonial4Author'),
      role: t('testimonial4Role'),
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      rating: 5,
    },
    {
      content: t('testimonial5Content'),
      author: t('testimonial5Author'),
      role: t('testimonial5Role'),
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
      rating: 5,
    },
    {
      content: t('testimonial6Content'),
      author: t('testimonial6Author'),
      role: t('testimonial6Role'),
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      rating: 5,
    },
  ]

  const StarRating = ({ rating }) => {
    return (
      <div className="flex space-x-1">
        {[...Array(5)].map((_, i) => (
          <svg
            key={i}
            className={`h-4 w-4 ${
              i < rating ? 'text-yellow-400' : 'text-gray-400'
            }`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    )
  }

  return (
    <section className="w-full bg-gradient-to-br from-purple-700/30 via-purple-700/30 to-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl translate-x-0">
        <h2 className="mb-4 text-center text-3xl font-bold text-white sm:text-4xl">
          {t('testimonialsTitle')}
        </h2>
        <p className="mx-auto mb-12 max-w-3xl text-center text-lg text-white/30">
          {t('testimonialsDescription')}
        </p>

        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="group relative rounded-xl bg-gradient-to-br from-purple-800/20 via-purple-700/30 to-pink-900/20 p-6 shadow-lg backdrop-blur-sm transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-purple-500/25"
            >
              {/* Background glow effect */}
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-600/10 opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
              
              <div className="relative">
                {/* Quote icon */}
                <div className="mb-4 text-purple-400">
                  <svg
                    className="h-8 w-8"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                  </svg>
                </div>

                {/* Star rating */}
                <div className="mb-4">
                  <StarRating rating={testimonial.rating} />
                </div>

                {/* Testimonial content */}
                <p className="mb-6 text-white/80 leading-relaxed">
                  "{testimonial.content}"
                </p>

                {/* User info with avatar */}
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.author}
                      className="h-12 w-12 rounded-full object-cover ring-2 ring-purple-400/30"
                    />
                    {/* Online status indicator */}
                    <div className="absolute -bottom-0 -right-0 h-2 w-2 rounded-full bg-green-400 ring-2 ring-white">
                      <div className="h-full w-full animate-pulse rounded-full bg-green-400"></div>
                    </div>
                  </div>
                  <div>
                    <p className="font-semibold text-white">{testimonial.author}</p>
                    <p className="text-sm text-white/60">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trust indicators */}
        <div className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-white sm:text-3xl">500K+</div>
            <div className="text-sm text-white/60">{t('trustStat1')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white sm:text-3xl">4.9/5</div>
            <div className="text-sm text-white/60">{t('trustStat2')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white sm:text-3xl">1M+</div>
            <div className="text-sm text-white/60">{t('trustStat3')}</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white sm:text-3xl">24/7</div>
            <div className="text-sm text-white/60">{t('trustStat4')}</div>
          </div>
        </div>
      </div>
    </section>
  )
}