'use client'
import { useState } from 'react'
import { useTranslations } from 'next-intl'
import BeforeAfterSlider from './BeforeAfterSlider'

export default function UseCases() {
  const t = useTranslations('aiImageBackgroundRemover')
  const [activeTab, setActiveTab] = useState('portraits')

  const cases = {
    portraits: {
      title: t('portraitsTab'),
      before: '/images/portraits-before.png',
      after: '/images/portraits-after.png',
      beforeAlt: t('portraitsBeforeAlt'),
      afterAlt: t('portraitsAfterAlt'),
    },
    products: {
      title: t('productsTab'),
      before: '/images/product-before.png',
      after: '/images/product-after.png',
      beforeAlt: t('productsBeforeAlt'),
      afterAlt: t('productsAfterAlt'),
    },
    animals: {
      title: t('animalsTab'),
      before: '/images/animal-before.png',
      after: '/images/animal-after.png',
      beforeAlt: t('animalsBeforeAlt'),
      afterAlt: t('animalsAfterAlt'),
    },
    cars: {
      title: t('carsTab'),
      before: '/images/car-before.png',
      after: '/images/car-after.png',
      beforeAlt: t('carsBeforeAlt'),
      afterAlt: t('carsAfterAlt'),
    },
    graphics: {
      title: t('graphicsTab'),
      before: '/images/graphics-before.png',
      after: '/images/graphics-after.png',
      beforeAlt: t('graphicsBeforeAlt'),
      afterAlt: t('graphicsAfterAlt'),
    },
  }

  return (
    <section className="w-full bg-gradient-to-bl to-purple-700/30 via-purple-700/30 from-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <h2 className="mb-4 translate-x-0 text-center text-3xl font-bold text-white sm:text-4xl">
          {t('useCasesTitle')}
        </h2>
        <p className="mx-auto translate-x-0 mb-12 max-w-3xl text-center text-lg text-white/30">
          {t('useCasesDescription')}
        </p>

        <div className="mb-8 flex flex-wrap justify-center gap-4">
          {Object.entries(cases).map(([key, value]) => (
            <button
              key={key}
              onClick={() => setActiveTab(key)}
              className={`rounded-full translate-x-0 px-6 py-2 text-sm font-medium transition-all duration-75 ease-in ${
                activeTab === key
                  ? 'group px-8 py-4 bg-gradient-to-r border border-white/0 border-solid from-pink-500/80 to-purple-600/80 text-white font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20'
                  : 'group px-8 py-4 bg-transparent border border-white/15 border-solid text-white/70 font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20'
              }`}
            >
              {value.title}
            </button>
          ))}
        </div>
        <div className="mx-auto max-w-3xl">
          {Object.keys(cases).map((item) => {
            return (
              <div
                key={item}
                className={`${item === activeTab ? '' : '!hidden'}`}
              >
                <BeforeAfterSlider
                  beforeAlt={cases[activeTab as keyof typeof cases].beforeAlt}
                  afterAlt={cases[activeTab as keyof typeof cases].afterAlt}
                  beforeImage={cases[activeTab as keyof typeof cases].before}
                  afterImage={cases[activeTab as keyof typeof cases].after}
                />
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
