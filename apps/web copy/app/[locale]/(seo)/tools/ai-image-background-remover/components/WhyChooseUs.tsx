import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'
import BeforeAfterSlider from './BeforeAfterSlider'

export default function WhyChooseUs() {
  const t = useTranslations('aiImageBackgroundRemover')
  return (
    <section className="w-full bg-gradient-to-tr from-purple-700/30 via-purple-700/30 to-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <h2 className="mb-4 text-center text-3xl font-bold text-white sm:text-4xl">
          {t('whyChooseUsTitle')}
        </h2>

        <div className="mt-16 grid gap-8 lg:grid-cols-3">
          {/* Feature 1 */}
          <AnimatedCard>
            <div className="flex flex-col items-center text-center px-3 py-8 cursor-pointer">
              <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600">
                <svg
                  className="h-8 w-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                {t('whyFeature1Title')}
              </h3>
              <p className="text-white/70 leading-relaxed">
                {t('whyFeature1Description')}
              </p>
            </div>
          </AnimatedCard>

          {/* Feature 2 */}
          <AnimatedCard>
            <div className="flex flex-col items-center text-center px-3 py-8 cursor-pointer">
              <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600">
                <svg
                  className="h-8 w-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                {t('whyFeature2Title')}
              </h3>
              <p className="text-white/70 leading-relaxed">
                {t('whyFeature2Description')}
              </p>
            </div>
          </AnimatedCard>

          {/* Feature 3 */}
          <AnimatedCard>
            <div className="flex flex-col items-center text-center px-3 py-8 cursor-pointer">
              <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600">
                <svg
                  className="h-8 w-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 011-1h1a2 2 0 100-4H7a1 1 0 01-1-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"
                  />
                </svg>
              </div>
              <h3 className="mb-4 text-xl font-semibold text-white">
                {t('whyFeature3Title')}
              </h3>
              <p className="text-white/70 leading-relaxed">
                {t('whyFeature3Description')}
              </p>
            </div>
          </AnimatedCard>
        </div>

        {/* Detailed Features Section */}
        <div className="mt-20 grid gap-12 lg:grid-cols-2 lg:gap-16">
          <div className="flex flex-col justify-center space-y-8">
            <div className="border-l-4 border-blue-400/50 pl-6">
              <h3 className="mb-3 text-xl font-semibold text-white">
                {t('whyDetail1Title')}
              </h3>
              <p className="text-white/70 leading-relaxed">
                {t('whyDetail1Description')}
              </p>
            </div>

            <div className="border-l-4 border-purple-400/50 pl-6">
              <h3 className="mb-3 text-xl font-semibold text-white">
                {t('whyDetail2Title')}
              </h3>
              <p className="text-white/70 leading-relaxed">
                {t('whyDetail2Description')}
              </p>
            </div>

            <div className="border-l-4 border-pink-400/50 pl-6">
              <h3 className="mb-3 text-xl font-semibold text-white">
                {t('whyDetail3Title')}
              </h3>
              <p className="text-white/70 leading-relaxed">
                {t('whyDetail3Description')}
              </p>
            </div>
          </div>

          <AnimatedCard className="relative">
              <BeforeAfterSlider containerClassName='!w-full !h-full' className='w-full h-full'
                beforeImage="https://sb.kaleidousercontent.com/67418/992x558/ef4cc24685/people-org.png"
                afterImage="https://sb.kaleidousercontent.com/67418/992x558/7632960ff9/people.png"
              />
            </AnimatedCard>
        </div>
      </div>
    </section>
  )
}
