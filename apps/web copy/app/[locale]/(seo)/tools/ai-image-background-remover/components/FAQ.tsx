'use client'
import { Link } from '@i18n/routing'
import { useState } from 'react'
import { useTranslations } from 'next-intl'

export default function FAQ({ link = '' }) {
  const t = useTranslations('aiImageBackgroundRemover')
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
      icon: '🖼️',
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
      icon: '💰',
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
      icon: '🎨',
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
      icon: '📝',
    },
    {
      question: t('faq5Question'),
      answer: t('faq5Answer'),
      icon: '🔄',
    },
    {
      question: t('faq6Question'),
      answer: t('faq6Answer'),
      icon: '🎯',
    },
  ]

  return (
    <section className="w-full bg-gradient-to-br from-purple-700/30 via-purple-700/30 to-pink-900 px-4 py-16 md:px-6 lg:px-8">
      <div className="mx-auto max-w-4xl">
        <h2 className="mb-4 text-center text-3xl font-bold text-white sm:text-4xl">
          {t('faqTitle')}
        </h2>
        <p className="mx-auto mb-12 max-w-2xl text-center text-lg text-white/30">
          {t('faqDescription')}
        </p>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="rounded-lg border border-white/10 bg-white/5 backdrop-blur-sm"
            >
              <button
                className="flex w-full items-center justify-between p-6 text-left"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <div className="flex items-center gap-4">
                  <span className="text-2xl">{faq.icon}</span>
                  <span className="text-lg font-semibold text-white">
                    {faq.question}
                  </span>
                </div>
                <svg
                  className={`h-5 w-5 text-white transition-transform ${
                    openIndex === index ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              {openIndex === index && (
                <div className="border-t border-white/10 px-6 pb-6 pt-4">
                  <p className="text-white/70 leading-relaxed">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-16 rounded-2xl border border-white/10 bg-white/5 p-8 text-center backdrop-blur-sm">
          <h3 className="mb-4 text-2xl font-bold text-white">
            {t('faqStillHaveQuestionsTitle')}
          </h3>
          <p className="mb-6 text-white/70">
            {t('faqStillHaveQuestionsDescription')}
          </p>
          <Link
            href={link}
            className="inline-flex items-center rounded-full bg-gradient-to-r from-purple-500 to-pink-500 px-8 py-3 font-semibold text-white transition-all hover:opacity-90 hover:shadow-lg"
          >
            {t('faqTryItButton')}
          </Link>
        </div>
      </div>
    </section>
  )
}
