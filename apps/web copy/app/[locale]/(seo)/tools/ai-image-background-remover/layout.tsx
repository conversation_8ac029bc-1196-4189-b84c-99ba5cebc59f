import { NavBar } from '@marketing/shared/components/NavBar'
import type { PropsWithChildren } from 'react'
import { Toaster } from 'react-hot-toast'

export default function MarketingLayout({ children }: PropsWithChildren) {
  return (
    <div>
      <Toaster
        position="top-center"
        reverseOrder={false}
        toastOptions={{
          // 默认样式配置
          duration: 2000,
          style: {
            background: '#333',
            color: '#fff',
          },
        }}
      />

      <NavBar />
      <main className="min-h-screen">{children}</main>
      {/* <Footer /> */}
    </div>
  )
}
