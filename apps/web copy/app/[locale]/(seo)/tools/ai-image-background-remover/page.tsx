import { Metadata } from 'next'
import React from 'react'
import { getTranslations } from 'next-intl/server'
import Hero from './components/Hero'
import HowToUse from './components/HowToUse'
import UseCases from './components/UseCases'
import WhyChooseUs from './components/WhyChooseUs'
import Features from './components/Features'
import Testimonials from './components/Testimonials'
import FAQ from './components/FAQ'
import CTA from './components/CTA'
import BackGroundHighlight from './components/BackGroundHighlight'
import './index.css'

const url = 'https://imggen.ai/ai-image-background-remover'
const ogImage = 'https://imggen.ai/images/og-background-remover.jpg'
const twitterImage = 'https://imggen.ai/images/twitter-background-remover.jpg'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('aiImageBackgroundRemover')

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords').split(', '),
    authors: [{ name: 'ImgGen Team' }],
    openGraph: {
      type: 'website',
      url,
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      siteName: 'ImgGen',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: t('openGraphAlt'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: [twitterImage],
      creator: '@imggen_ai',
      site: '@imggen_ai',
    },
  }
}
const link = 'ai/ai-image-background-remover'
export default function AIBackgroundRemoverPage() {
  return (
    <main className="flex min-h-screen flex-col bg-purple-950/60 items-center justify-between">
      <BackGroundHighlight/>
      <Hero link={link}/>
      <HowToUse />
      <UseCases />
      <Features link={link}/>
      <WhyChooseUs />
      <Testimonials />
      <FAQ link={link}/>
      <CTA link={link}/>
    </main>
  )
}
