import React from 'react'
import { Star, Quote } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { whyUsData } from './data/whyUsData'
import { AnimatedCard } from './AnimatedCard'
import { Link } from '@i18n/routing'

const WhyUsSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiArtGenerator')
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <AnimatedCard>
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-6">
              {t('whyUsTitle')}
            </h2>
            <p className="text-gray-300 text-lg md:text-xl leading-relaxed">
              {t('whyUsDescription')}
            </p>
          </div>
        </AnimatedCard>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {whyUsData.map((item, index) => (
            <AnimatedCard key={index} delay={index * 200}>
              <div className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 flex flex-col h-full">
                <Quote className="w-8 h-8 text-purple-400 mb-4" />

                {/* Image */}
                <div className="relative group mb-6">
                  <div className="relative overflow-hidden rounded-xl">
                    <img
                      src={item.img}
                      alt={t(`whyUs${index + 1}Alt`)}
                      className="w-full aspect-video object-cover transform transition duration-500 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20" />
                  </div>
                </div>

                <div className="flex mb-4">
                  {[...Array(item.stars)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-5 h-5 text-yellow-400 fill-yellow-400"
                    />
                  ))}
                </div>

                <h3 className="text-xl font-semibold text-white mb-4">
                  {t(`whyUs${index + 1}Title`)}
                </h3>

                <p className="text-gray-300 mb-6 italic text-lg flex-grow leading-relaxed">
                  {t(`whyUs${index + 1}Description`)}
                </p>

                <div className="flex items-center justify-between mt-auto">
                  <div className="flex items-center">
                    <img
                      src={item.avatar}
                      alt={t(`whyUs${index + 1}Author`)}
                      className="w-10 h-10 rounded-full mr-4 object-cover"
                    />
                    <div>
                      <p className="font-semibold text-white">
                        {t(`whyUs${index + 1}Author`)}
                      </p>
                    </div>
                  </div>

                  <Link
                    href={toolUrl}
                    className="px-6 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-lg shadow-lg hover:opacity-90 hover:scale-105 transition-all text-sm"
                  >
                    {t(`whyUs${index + 1}Button`)}
                  </Link>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>
      </div>
    </section>
  )
}

export default WhyUsSection
