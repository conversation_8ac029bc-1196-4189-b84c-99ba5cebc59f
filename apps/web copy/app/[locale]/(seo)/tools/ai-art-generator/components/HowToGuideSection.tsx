import React from 'react'
import { useTranslations } from 'next-intl'
import { howToData } from './data/howToData'
import { AnimatedCard } from './AnimatedCard'
import { Link } from '@i18n/routing'

const HowToGuideSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiArtGenerator')
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <AnimatedCard>
          <h2 className="text-4xl font-bold text-center text-white mb-24">
            {t('howToTitle')}
          </h2>
          <p className="text-center text-gray-300 text-lg mb-16">
            {t('howToDescription')}
          </p>
        </AnimatedCard>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          {howToData.map((step, index) => (
            <AnimatedCard key={index} delay={index * 200}>
              <div className="text-center">
                {/* Step Number */}
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-500 rounded-full text-2xl font-bold text-white mb-6">
                  {step.step}
                </div>

                {/* Image */}
                <div className="relative group mb-6">
                  <div className="relative overflow-hidden rounded-xl">
                    <img
                      src={step.img}
                      alt={t(`howTo${index + 1}Alt`)}
                      className="w-full aspect-video object-cover transform transition duration-500 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10" />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-white mb-4">
                  {t(`howTo${index + 1}Title`)}
                </h3>
                <p className="text-gray-300 text-lg leading-relaxed">
                  {t(`howTo${index + 1}Description`)}
                </p>
              </div>
            </AnimatedCard>
          ))}
        </div>

        <AnimatedCard delay={600}>
          <div className="text-center mt-12">
            <Link
              href={toolUrl}
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-lg shadow-lg hover:opacity-90 hover:scale-105 transition-all"
            >
              {t('howToCtaButton')}
            </Link>
          </div>
        </AnimatedCard>
      </div>
    </section>
  )
}

export default HowToGuideSection
