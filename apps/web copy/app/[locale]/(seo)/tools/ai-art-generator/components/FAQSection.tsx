'use client'
import React, { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { faqData } from './data/faqData'
import { AnimatedCard } from './AnimatedCard'

const FAQSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiArtGenerator')
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    )
  }

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <AnimatedCard>
          <h2 className="text-4xl font-bold text-center text-white mb-24">
            {t('faqTitle')}
          </h2>
        </AnimatedCard>

        <div className="max-w-4xl mx-auto space-y-6">
          {faqData.map((item, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div className="bg-slate-900/50 backdrop-blur-sm rounded-2xl border border-slate-800 overflow-hidden">
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full px-8 py-6 flex items-center justify-between text-left hover:bg-slate-800/30 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <img
                      src={item.user.avatar}
                      alt={item.user.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h3 className="text-lg font-semibold text-white pr-4">
                        {t(`faq${index + 1}Question`)}
                      </h3>
                      <p className="text-sm text-gray-400">
                        {t(`faq${index + 1}UserName`)} •{' '}
                        {t(`faq${index + 1}UserRole`)}
                      </p>
                    </div>
                  </div>
                  {openItems.includes(index) ? (
                    <ChevronUp className="w-6 h-6 text-purple-400 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-6 h-6 text-purple-400 flex-shrink-0" />
                  )}
                </button>

                {openItems.includes(index) && (
                  <div className="px-8 pb-6 pt-2">
                    <div className="pl-16">
                      <p className="text-gray-300 leading-relaxed">
                        {t(`faq${index + 1}Answer`)}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </AnimatedCard>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FAQSection
