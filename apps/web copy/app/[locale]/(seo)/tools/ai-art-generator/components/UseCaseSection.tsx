import React from 'react'
import { useTranslations } from 'next-intl'
import { useCaseData } from './data/useCaseData'
import { AnimatedCard } from './AnimatedCard'
import { Link } from '@i18n/routing'

const UseCaseSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiArtGenerator')
  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <AnimatedCard>
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              {t('useCaseTitle1')}{' '}
              <span className="text-pink-400">{t('useCaseTitle2')}</span>{' '}
              {t('useCaseTitle3')}
            </h2>
            <p className="text-gray-300 text-lg md:text-xl leading-relaxed">
              {t('useCaseDescription')}
            </p>
          </div>
        </AnimatedCard>

        <div className="space-y-20">
          {useCaseData.map((useCase, index) => (
            <AnimatedCard key={index} delay={index * 200}>
              <div
                className={`flex flex-col ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } items-center gap-12`}
              >
                {/* Content */}
                <div className="flex-1 space-y-6">
                  <div>
                    <h3 className="text-3xl font-bold text-white mb-4">
                      {t(`useCase${index + 1}Title`)}
                    </h3>
                    <p className="text-xl text-pink-400 font-semibold mb-6">
                      {t(`useCase${index + 1}Subtitle`)}
                    </p>
                    <p className="text-gray-300 text-lg leading-relaxed">
                      {t(`useCase${index + 1}Description`)}
                    </p>
                  </div>

                  <Link
                    href={toolUrl}
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-lg shadow-lg hover:opacity-90 hover:scale-105 transition-all"
                  >
                    {t(`useCase${index + 1}Button`)}
                  </Link>
                </div>

                {/* Image */}
                <div className="flex-1">
                  <div className="relative group">
                    <div className="relative overflow-hidden rounded-2xl">
                      <img
                        src={useCase.img}
                        alt={t(`useCase${index + 1}Alt`)}
                        className="w-full aspect-video object-cover transform transition duration-500 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10" />
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>
      </div>
    </section>
  )
}

export default UseCaseSection
