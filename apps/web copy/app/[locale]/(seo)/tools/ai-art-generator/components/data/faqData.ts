export interface FAQ {
  question: string
  answer: string
  user: {
    name: string
    avatar: string
    role: string
  }
}

export const faqData: FAQ[] = [
  {
    question: 'Is this tool similar to a chat gpt image generator?',
    answer: `Yes, our ai art generator free uses similar natural language processing. You describe the image you want in plain English, and our AI brings it to life, making it incredibly intuitive and easy for anyone to use.`,
    user: {
      name: '<PERSON>',
      avatar: '/avator/Snipaste_2025-07-04_23-51-09.jpg',
      role: 'Digital Artist'
    }
  },
  {
    question: 'How good is the ai pencil drawing generator feature?',
    answer: `Our ai pencil drawing generator is one of the most advanced available. It simulates realistic shading and textures, giving you authentic-looking sketches. It's a key feature of our ai image generator no restrictions.`,
    user: {
      name: '<PERSON>',
      avatar: '/avator/Snipaste_2025-07-04_23-52-19.jpg',
      role: 'Art Director'
    }
  },
  {
    question: 'Are there truly no limits with this ai image generator no restrictions?',
    answer: `We are committed to creative freedom. While we have safeguards for harmful content, our tool has far fewer limitations than others. This allows you to explore a vast range of concepts with our ai image generator no restrictions.`,
    user: {
      name: '<PERSON>',
      avatar: '/avator/Snipaste_2025-07-04_23-53-27.jpg',
      role: 'Creative Director'
    }
  },
  {
    question: 'How does the ai vintage photo generator work?',
    answer: `The ai vintage photo generator applies a sophisticated filter and texture layer to your images. Our ai art generator free makes this process simple and highly effective for giving modern photos a classic, nostalgic feel.`,
    user: {
      name: 'David Kim',
      avatar: '/avator/Snipaste_2025-07-04_23-54-46.jpg',
      role: 'Marketing Manager'
    }
  },
]