import React from 'react'
import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import WhyUsSection from './components/WhyUsSection'
import UseCaseSection from './components/UseCaseSection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'
import { Link } from '@i18n/routing'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('aiArtGenerator')

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      images: [
        {
          url: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-art-generator/C7.jpeg',
          width: 1200,
          height: 630,
          alt: t('openGraphAlt'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: [
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-art-generator/C8.jpeg',
      ],
    },
  }
}

const AIArtGeneratorPage = async () => {
  // 统一配置跳转URL
  const AI_ART_GENERATOR_TOOL_URL = '/ai/art-generator'
  const t = await getTranslations('aiArtGenerator')

  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: t('schemaName'),
    description: t('schemaDescription'),
    url: 'https://www.imggen.org/tools/ai-art-generator',
    applicationCategory: 'DesignApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    featureList: [
      t('schemaFeature1'),
      t('schemaFeature2'),
      t('schemaFeature3'),
      t('schemaFeature4'),
      t('schemaFeature5'),
    ],
    author: {
      '@type': 'Organization',
      name: t('schemaAuthorName'),
      url: 'https://www.imggen.org',
    },
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* Header Section */}
      <section className="pt-20 pb-12 relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center text-center max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight mt-6">
              <span className="text-pink-400">{t('headerTitle1')}</span>{' '}
              {t('headerTitle2')}{' '}
              <span className="text-purple-400">{t('headerTitle3')}</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              {t('headerDescription')}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Link
                href={AI_ART_GENERATOR_TOOL_URL}
                className="px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-lg shadow-lg hover:opacity-90 hover:scale-105 transition-all"
              >
                {t('generateArtButton')}
              </Link>
              <Link
                href="#how-to"
                className="px-8 py-4 bg-transparent border-2 border-purple-400 text-purple-400 font-semibold rounded-lg hover:bg-purple-400 hover:text-white transition-all"
              >
                {t('learnHowButton')}
              </Link>
            </div>

            {/* Header Image */}
            <div className="relative group">
              <div className="relative overflow-hidden rounded-2xl">
                <img
                  src="https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-art-generator/C5.jpeg"
                  alt={t('headerImageAlt')}
                  className="w-full aspect-video object-cover transform transition duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <WhyUsSection toolUrl={AI_ART_GENERATOR_TOOL_URL} />
      <UseCaseSection toolUrl={AI_ART_GENERATOR_TOOL_URL} />
      <div id="how-to">
        <HowToGuideSection toolUrl={AI_ART_GENERATOR_TOOL_URL} />
      </div>
      <TestimonialSection toolUrl={AI_ART_GENERATOR_TOOL_URL} />
      <FAQSection toolUrl={AI_ART_GENERATOR_TOOL_URL} />
    </div>
  )
}

export default AIArtGeneratorPage
