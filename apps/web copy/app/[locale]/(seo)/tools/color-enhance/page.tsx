import { getTranslations } from 'next-intl/server'
import Hero<PERSON><PERSON><PERSON> from './components/HeroSection'
import HowToSection from './components/HowToSection'
import UseCasesSection from './components/UseCasesSection'
import WhyUsSection from './components/WhyUsSection'
import AdvantagesSection from './components/AdvantagesSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'
import CTASection from './components/CTASection'

export async function generateMetadata() {
  const t = await getTranslations('colorEnhance')
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      url: 'https://imggen.ai/color-enhance',
      images: [
        {
          url: 'https://imggen.ai/images/og-color-enhance.jpg',
          width: 1200,
          height: 630,
          alt: t('title'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
      images: ['https://imggen.ai/images/twitter-color-enhance.jpg'],
    },
  }
}

export default async function ColorEnhancePage() {
  const t = await getTranslations('colorEnhance')
  // 统一配置跳转URL
  const COLOR_ENHANCE_TOOL_URL = '/ai/color-enhance'

  return (
    <div className="min-h-screen bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      {/* Font Awesome CDN */}
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        crossOrigin="anonymous"
      />

      {/* Structured Data - Schema.org JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'SoftwareApplication',
            name: t('title'),
            description: t('description'),
            applicationCategory: 'MultimediaApplication',
            operatingSystem: 'Web',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD',
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: '4.8',
              reviewCount: '987',
            },
            image: 'https://imggen.ai/assets/schema-image-color-enhance.jpg',
            url: 'https://imggen.ai/color-enhance',
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': 'https://imggen.ai/color-enhance',
            },
          }),
        }}
      />

      <main className="relative">
        <HeroSection toolUrl={COLOR_ENHANCE_TOOL_URL} />
        <HowToSection toolUrl={COLOR_ENHANCE_TOOL_URL} />
        <UseCasesSection toolUrl={COLOR_ENHANCE_TOOL_URL} />
        <WhyUsSection toolUrl={COLOR_ENHANCE_TOOL_URL} />
        <AdvantagesSection toolUrl={COLOR_ENHANCE_TOOL_URL} />
        <TestimonialsSection toolUrl={COLOR_ENHANCE_TOOL_URL} />
        <FAQSection toolUrl={COLOR_ENHANCE_TOOL_URL} />
        <CTASection toolUrl={COLOR_ENHANCE_TOOL_URL} />
      </main>
    </div>
  )
}