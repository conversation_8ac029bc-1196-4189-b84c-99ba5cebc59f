import { Link } from '@i18n/routing'
import CTAButton from './CTAButton'
import { useTranslations } from 'next-intl'

export default function UseCasesSection({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations('colorEnhance')

  const useCases = [
    {
      emoji: '👨‍👩‍👧‍👦',
      title: t('useCase1Title'),
      description: t('useCase1Description'),
      image: '/images/color-enhance/family-photo.png',
      alt: t('familyExampleAlt'),
      buttonText: t('useCase1Button'),
    },
    {
      emoji: '🏞',
      title: t('useCase2Title'),
      description: t('useCase2Description'),
      image: '/images/color-enhance/travel-photo.png',
      alt: t('travelExampleAlt'),
      buttonText: t('useCase2Button'),
    },
    {
      emoji: '📦',
      title: t('useCase3Title'),
      description: t('useCase3Description'),
      image: '/images/color-enhance/product-photo.png',
      alt: t('productExampleAlt'),
      buttonText: t('useCase3Button'),
    },
    {
      emoji: '🎨',
      title: t('useCase4Title'),
      description: t('useCase4Description'),
      image: '/images/color-enhance/social-media.png',
      alt: t('socialExampleAlt'),
      buttonText: t('useCase4Button'),
    },
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-[#1a0b2e] to-[#2d1a4a]">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-6">
            <i className="fas fa-lightbulb text-purple-400" />
            <span>{t('useCasesSectionBadge')}</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            {t('useCasesSectionTitle1')}
            <br />
            <span className="text-purple-400">{t('useCasesSectionTitle2')}</span>
          </h2>
          <p className="text-lg text-white/70 max-w-3xl mx-auto">
            {t('useCasesSectionDescription')}
          </p>
        </div>

        {/* Use Cases Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {useCases.map((useCase, index) => (
            <div key={index} className="group">
              <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden transition-all duration-300 group-hover:bg-white/10 group-hover:border-purple-500/50 group-hover:transform group-hover:scale-105">
                {/* Image Section */}
                <div className="relative h-64 bg-gradient-to-br from-purple-900/30 to-pink-900/30 overflow-hidden">
                  {/* Mock Image Placeholder */}
                  <div className="w-full relative h-full flex items-center justify-center">
                  <img className=' absolute inset-0 w-full h-full object-cover' src={useCase.image} alt={useCase.alt} />
                  </div>

                  {/* Overlay Gradient */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

                  {/* Floating Icon */}
                  <div className="absolute top-4 right-4">
                    <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                      <i className="fas fa-palette text-white text-lg" />
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <span className="text-2xl">{useCase.emoji}</span>
                    <h3 className="text-xl font-semibold text-white">
                      {useCase.title}
                    </h3>
                  </div>

                  <p className="text-white/70 mb-6 leading-relaxed">
                    {useCase.description}
                  </p>

                  <Link href={toolUrl}>
                    <CTAButton
                      variant="secondary"
                      size="md"
                      className="w-full group-hover:bg-gradient-to-r group-hover:from-purple-600 group-hover:to-pink-600 group-hover:text-white transition-all duration-300"
                    >
                      <i className="fas fa-magic mr-2" />
                      {useCase.buttonText}
                    </CTAButton>
                  </Link>
                </div>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-pink-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-semibold text-white mb-4">
              {t('useCasesCTA')}
            </h3>
            <p className="text-white/70 mb-6">
              {t('useCasesCTADescription')}
            </p>
            <Link href={toolUrl}>
              <CTAButton size="lg">
                <i className="fas fa-upload mr-2" />
                {t('useCasesCTAButton')}
              </CTAButton>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
