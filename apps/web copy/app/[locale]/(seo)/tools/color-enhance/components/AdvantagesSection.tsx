import { useTranslations } from 'next-intl'

export default function AdvantagesSection({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations('colorEnhance')

  const advantages = [
    {
      icon: 'fas fa-magic',
      title: t('advantage1Title'),
      description: t('advantage1Description'),
    },
    {
      icon: 'fas fa-unlock',
      title: t('advantage2Title'),
      description: t('advantage2Description'),
    },
    {
      icon: 'fas fa-globe',
      title: t('advantage3Title'),
      description: t('advantage3Description'),
    },
    {
      icon: 'fas fa-hand-pointer',
      title: t('advantage4Title'),
      description: t('advantage4Description'),
    },
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-[#3b0764] to-[#10051A]">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-6">
            <i className="fas fa-trophy text-purple-400" />
            <span>{t('advantagesSectionBadge')}</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            {t('advantagesSectionTitle1')}
            <br />
            <span className="text-purple-400">{t('advantagesSectionTitle2')}</span>
          </h2>
          <p className="text-lg text-white/70 max-w-2xl mx-auto">
            {t('advantagesSectionDescription')}
          </p>
        </div>

        {/* Advantages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {advantages.map((advantage, index) => (
            <div key={index} className="group">
              <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 h-full transition-all duration-300 group-hover:bg-white/10 group-hover:border-purple-500/50 group-hover:transform group-hover:scale-105">
                {/* Icon */}
                <div className="mb-6">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-2xl">
                    <i
                      className={`${advantage.icon} text-3xl text-purple-400`}
                    />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-semibold text-white mb-4">
                  {advantage.title}
                </h3>
                <p className="text-white/70 leading-relaxed">
                  {advantage.description}
                </p>

                {/* Decorative Element */}
                <div className="absolute top-4 right-4 w-20 h-20 bg-gradient-to-r from-purple-600/10 to-pink-600/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-pink-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-2xl p-8 border border-white/10">
            <h3 className="text-2xl font-semibold text-white mb-4">
              {t('advantagesCTA')}
            </h3>
            <p className="text-white/70 mb-6 max-w-2xl mx-auto">
              {t('advantagesCTADescription')}
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <div className="flex items-center gap-2 text-white/80">
                <i className="fas fa-users text-purple-400" />
                <span className="text-sm">{t('advantagesStat1')}</span>
              </div>
              <div className="flex items-center gap-2 text-white/80">
                <i className="fas fa-star text-yellow-400" />
                <span className="text-sm">{t('advantagesStat2')}</span>
              </div>
              <div className="flex items-center gap-2 text-white/80">
                <i className="fas fa-shield-alt text-green-400" />
                <span className="text-sm">{t('advantagesStat3')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
