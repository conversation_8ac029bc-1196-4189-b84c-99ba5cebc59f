import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

export default function HowToSection({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations('colorEnhance')

  const steps = [
    {
      step: '1',
      title: t('howToStep1Title'),
      description: t('howToStep1Description'),
      icon: 'fas fa-upload',
    },
    {
      step: '2',
      title: t('howToStep2Title'),
      description: t('howToStep2Description'),
      icon: 'fas fa-palette',
    },
    {
      step: '3',
      title: t('howToStep3Title'),
      description: t('howToStep3Description'),
      icon: 'fas fa-magic',
    },
    {
      step: '4',
      title: t('howToStep4Title'),
      description: t('howToStep4Description'),
      icon: 'fas fa-sliders-h',
    },
    {
      step: '5',
      title: t('howToStep5Title'),
      description: t('howToStep5Description'),
      icon: 'fas fa-download',
    },
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-[#10051A] to-[#1a0b2e]">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-6">
            <i className="fas fa-route text-purple-400" />
            <span>{t('howToSectionBadge')}</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            {t('howToSectionTitle1')}
            <br />
            <span className="text-purple-400">{t('howToSectionTitle2')}</span>
          </h2>
          <p className="text-lg text-white/70 max-w-2xl mx-auto">
            {t('howToSectionDescription')}
          </p>
        </div>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {steps.map((item, index) => (
            <div key={index} className="relative group">
              {/* Card */}
              <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 h-full transition-all duration-300 group-hover:bg-white/10 group-hover:border-purple-500/50 group-hover:transform group-hover:scale-105">
                {/* Step Number */}
                <div className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                  {item.step}
                </div>

                {/* Icon */}
                <div className="mb-6 pt-4">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-2xl">
                    <i className={`${item.icon} text-2xl text-purple-400`} />
                  </div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-semibold text-white mb-3">
                  {item.title}
                </h3>
                <p className="text-white/70 leading-relaxed">
                  {item.description}
                </p>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-pink-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>

              {/* Connection Line (except for last item) */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-purple-600/50 to-pink-600/50" />
              )}
            </div>
          ))}
        </div>

        {/* Tool Interface Preview */}
        <div className="mt-16 text-center">
          <div className="relative max-w-4xl mx-auto">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-semibold text-white mb-2">
                  {t('howToInterfaceTitle')}
                </h3>
                <p className="text-white/60">
                  {t('howToInterfaceDescription')}
                </p>
              </div>

              {/* Mock Interface */}
              <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-xl p-6 border border-white/10">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                  {/* Before/After Preview */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <div className=" max-md:flex-1 max-md:h-32 max-md:w-32 bg-gray-600/50 rounded-lg h-48 w-48 flex relative items-center justify-center">
                        <img
                          className=" absolute inset-0 w-full h-full rounded-lg object-cover"
                          src="/images/color-enhance/house-before.webp"
                          alt=""
                        />
                        <span className="text-white/60 text-sm">
                          {t('howToInterfaceBefore')}
                        </span>
                      </div>
                      <i className="fas fa-arrow-right text-purple-400" />
                      <div className=" max-md:flex-1 max-md:h-32 max-md:w-32 relative bg-gradient-to-br h-48 w-48 from-yellow-300/50 to-orange-400/50 rounded-lg  flex items-center justify-center">
                        <img
                          className=" absolute inset-0 w-full rounded-lg h-full object-cover"
                          src="/images/color-enhance/house-after.webp"
                          alt=""
                        />
                        <span className="text-white text-sm">
                          {t('howToInterfaceAfter')}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Controls */}
                  <div className="space-y-4">
                    <div className="bg-white/5 rounded-lg p-4">
                      <div className="text-white text-sm mb-2">
                        {t('howToInterfaceBrightness')}
                      </div>
                      <div className="w-full bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-600 to-pink-600 h-2 rounded-full"
                          style={{ width: '60%' }}
                        />
                      </div>
                    </div>
                    <div className="bg-white/5 rounded-lg p-4">
                      <div className="text-white text-sm mb-2">
                        {t('howToInterfaceContrast')}
                      </div>
                      <div className="w-full bg-gray-600 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-purple-600 to-pink-600 h-2 rounded-full"
                          style={{ width: '75%' }}
                        />
                      </div>
                    </div>
                    <Link href={toolUrl}>
                      <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-lg font-semibold">
                        {t('howToInterfaceButton')}
                      </button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
