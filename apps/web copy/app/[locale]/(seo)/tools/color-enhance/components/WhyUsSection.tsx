import { Link } from '@i18n/routing'
import CTAButton from './CTAButton'
import { useTranslations } from 'next-intl'

export default function WhyUsSection({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations('colorEnhance')

  const features = [
    {
      icon: 'fas fa-bullseye',
      title: t('whyUsFeature1Title'),
      description: t('whyUsFeature1Description'),
      image: '/images/color-enhance/cat-correction.png',
      alt: t('whyUsFeature1Alt'),
      buttonText: t('whyUsFeature1Button'),
    },
    {
      icon: 'fas fa-palette',
      title: t('whyUsFeature2Title'),
      description: t('whyUsFeature2Description'),
      image: '/images/color-enhance/sunset-correction.png',
      alt: t('whyUsFeature2Alt'),
      buttonText: t('whyUsFeature2Button'),
    },
    {
      icon: 'fas fa-thermometer-half',
      title: t('whyUsFeature3Title'),
      description: t('whyUsFeature3Description'),
      image: '/images/color-enhance/temperature-correction.png',
      alt: t('whyUsFeature3Alt'),
      buttonText: t('whyUsFeature3Button'),
    },
    {
      icon: 'fas fa-lightbulb',
      title: t('whyUsFeature4Title'),
      description: t('whyUsFeature4Description'),
      image: '/images/color-enhance/restaurant-correction.png',
      alt: t('whyUsFeature4Alt'),
      buttonText: t('whyUsFeature4Button'),
    },
    {
      icon: 'fas fa-adjust',
      title: t('whyUsFeature5Title'),
      description: t('whyUsFeature5Description'),
      image: '/images/color-enhance/sofa-correction.png',
      alt: t('whyUsFeature5Alt'),
      buttonText: t('whyUsFeature5Button'),
    },
    {
      icon: 'fas fa-search',
      title: t('whyUsFeature6Title'),
      description: t('whyUsFeature6Description'),
      image: '/images/color-enhance/city-correction.png',
      alt: t('whyUsFeature6Alt'),
      buttonText: t('whyUsFeature6Button'),
    },
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-[#2d1a4a] to-[#3b0764]">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-6">
            <i className="fas fa-star text-purple-400" />
            <span>{t('whyUsSectionBadge')}</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            {t('whyUsSectionTitle1')}
            <br />
            <span className="text-purple-400">{t('whyUsSectionTitle2')}</span>
          </h2>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="group">
              <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden h-full transition-all duration-300 group-hover:bg-white/10 group-hover:border-purple-500/50 group-hover:transform group-hover:scale-105">
                {/* Image Section */}
                <div className="relative h-48 bg-gradient-to-br from-purple-900/30 to-pink-900/30">
                  <div className="w-full h-full relative flex items-center justify-center">
                    <img
                      className=" absolute inset-0 w-full h-full object-cover"
                      src={feature.image}
                      alt=""
                    />
                    <div className="text-center">
                      <i
                        className={`${feature.icon} text-4xl text-purple-400 mb-2`}
                      />
                      <div className="text-white/60 text-xs italic">
                        {feature.alt}
                      </div>
                    </div>
                  </div>

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-xl flex items-center justify-center">
                      <i className={`${feature.icon} text-purple-400`} />
                    </div>
                    <h3 className="text-lg font-semibold text-white">
                      {feature.title}
                    </h3>
                  </div>

                  <p className="text-white/70 mb-6 leading-relaxed text-sm">
                    {feature.description}
                  </p>

                  <Link href={toolUrl}>
                    <CTAButton
                      variant="secondary"
                      size="sm"
                      className="w-full text-sm group-hover:bg-gradient-to-r group-hover:from-purple-600 group-hover:to-pink-600 group-hover:text-white transition-all duration-300"
                    >
                      {feature.buttonText}
                    </CTAButton>
                  </Link>
                </div>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-pink-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
