import CTAButton from './CTAButton'
import { useTranslations } from 'next-intl'

export default function HeroSection({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations('colorEnhance')

  return (
    <section className="relative min-h-screen flex flex-col items-center justify-center px-4 pt-20 pb-16 overflow-hidden bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Main Content Container */}
        <div className="flex flex-col xl:flex-row items-center gap-8 xl:gap-16">
          {/* Left Column - Header Content */}
          <div className="flex-1 text-center xl:text-left">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-6">
              <i className="fas fa-palette text-purple-400" />
              <span>{t('heroSectionBadge')}</span>
            </div>

            {/* Main Title */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-4 xl:mb-6 leading-tight">
              <span className="text-white">{t('heroSectionTitle1')}</span>
              <br />
              <span className="text-purple-400">{t('heroSectionTitle2')}</span>
            </h1>

            {/* Description */}
            <p className="text-base md:text-lg lg:text-xl text-white/80 max-w-2xl mx-auto xl:mx-0 mb-6 xl:mb-8 leading-relaxed">
              {t('heroSectionDescription')}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row items-center xl:items-start xl:justify-start justify-center gap-4 mb-8 xl:mb-0">
              <CTAButton size="lg" className="w-full sm:w-auto" href={toolUrl}>
                <i className="fas fa-upload mr-2" />
                {t('heroSectionButton1')}
              </CTAButton>
              <CTAButton
                href={toolUrl}
                variant="secondary"
                size="lg"
                className="w-full sm:w-auto"
              >
                <i className="fas fa-play mr-2" />
                {t('heroSectionButton2')}
              </CTAButton>
            </div>
          </div>

          {/* Right Column - Before/After Preview */}
          <div className="flex-1 w-full max-w-2xl">
            <div className="relative">
              {/* Desktop and Tablet Layout */}
              <div className="hidden md:block">
                <div className="flex items-center justify-center gap-4 lg:gap-8">
                  {/* Before Image */}
                  <div className="relative group flex-1 max-w-xs">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300" />
                    <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="text-center mb-3">
                        <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                          <i className="fas fa-image" />
                          {t('heroSectionBefore')}
                        </span>
                      </div>
                      <div className="w-full relative aspect-square bg-gray-600/50 rounded-xl flex items-center justify-center">
                        <img
                          className="object-cover blur-[0.5px] absolute inset-0 w-full h-full"
                          src="https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/a64c6c1cacd446d8af1fdbf58568220e~tplv-tb4s082cfz-aigc_resize_mark:1080:1080.webp?lk3s=43402efa&x-expires=1754784000&x-signature=slu0%2BjzZeYRkN%2Fx0zwPee60TbYk%3D&format=.webp"
                          alt={t('heroSectionOriginalPhoto')}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center justify-center flex-shrink-0">
                    <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full shadow-lg">
                      <i className="fas fa-arrow-right text-white text-xl" />
                    </div>
                  </div>

                  {/* After Image */}
                  <div className="relative group flex-1 max-w-xs">
                    <div className="absolute -inset-1 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300" />
                    <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="text-center mb-3">
                        <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                          <i className="fas fa-sparkles" />
                          {t('heroSectionAfter')}
                        </span>
                      </div>
                      <div className="w-full relative aspect-square bg-gradient-to-br from-yellow-300/80 to-orange-400/80 rounded-xl flex items-center justify-center">
                        <img
                          className="object-cover absolute inset-0 w-full h-full"
                          src="https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/cb363dac1df040c8a211bdec2f38bca4~tplv-tb4s082cfz-aigc_resize_mark:1080:1080.webp?lk3s=43402efa&x-expires=1754784000&x-signature=%2FBJbcFWlYLLEl%2FJiiDS8pCfvy1I%3D&format=.webp"
                          alt={t('heroSectionEnhancedPhoto')}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Mobile Layout */}
              <div className="block md:hidden">
                <div className="space-y-6">
                  {/* Before Image */}
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-75" />
                    <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="text-center mb-3">
                        <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                          <i className="fas fa-image" />
                          {t('heroSectionBefore')}
                        </span>
                      </div>
                      <div className="w-full max-w-xs mx-auto aspect-square bg-gray-600/50 rounded-xl flex items-center justify-center">
                        <div className="text-white/60 text-center">
                          <i className="fas fa-photo-video text-3xl mb-2" />
                          <p className="text-sm">
                            {t('heroSectionOriginalPhoto')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Arrow */}
                  <div className="flex items-center justify-center">
                    <div className="p-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full">
                      <i className="fas fa-arrow-down text-white text-xl" />
                    </div>
                  </div>

                  {/* After Image */}
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl blur opacity-75" />
                    <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-4">
                      <div className="text-center mb-3">
                        <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                          <i className="fas fa-sparkles" />
                          {t('heroSectionAfter')}
                        </span>
                      </div>
                      <div className="w-full max-w-xs mx-auto aspect-square bg-gradient-to-br from-yellow-300/80 to-orange-400/80 rounded-xl flex items-center justify-center">
                        <div className="text-white text-center">
                          <i className="fas fa-palette text-3xl mb-2" />
                          <p className="text-sm">
                            {t('heroSectionEnhancedPhoto')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}
