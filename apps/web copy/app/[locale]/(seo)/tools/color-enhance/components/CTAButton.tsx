import { Link } from '@i18n/routing'
import { ReactNode } from 'react'

interface CTAButtonProps {
  children: ReactNode
  href?: string
  onClick?: () => void
  variant?: 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  disabled?: boolean
}

export default function CTAButton({
  children,
  href,
  onClick,
  variant = 'primary',
  size = 'md',
  className = '',
  disabled = false,
}: CTAButtonProps) {
  const baseClasses = `
    inline-flex items-center justify-center font-semibold rounded-full 
    transition-all duration-300 transform hover:scale-105 active:scale-95
    focus:outline-none focus:ring-4 focus:ring-purple-500/50
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
  `

  const variants = {
    primary: `
      bg-gradient-to-r from-purple-600 to-pink-600 text-white
      shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40
      border border-transparent
    `,
    secondary: `
      bg-white/10 text-white border border-white/20
      backdrop-blur-sm hover:bg-white/20
      shadow-lg shadow-black/10
    `,
  }

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  }

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`

  if (href && !disabled) {
    return (
      <Link href={href} className={classes}>
        {children}
      </Link>
    )
  }

  return (
    <button onClick={onClick} className={classes} disabled={disabled}>
      {children}
    </button>
  )
}