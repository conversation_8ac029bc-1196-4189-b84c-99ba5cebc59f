import { useTranslations } from 'next-intl'

export default function TestimonialsSection({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations('colorEnhance')

  const testimonials = [
    {
      name: t('testimonial1Name'),
      role: t('testimonial1Role'),
      content: t('testimonial1Content'),
      rating: 5,
      avatar: 'EL',
    },
    {
      name: t('testimonial2Name'),
      role: t('testimonial2Role'),
      content: t('testimonial2Content'),
      rating: 5,
      avatar: 'JT',
    },
    {
      name: t('testimonial3Name'),
      role: t('testimonial3Role'),
      content: t('testimonial3Content'),
      rating: 5,
      avatar: 'NK',
    },
    {
      name: t('testimonial4Name'),
      role: t('testimonial4Role'),
      content: t('testimonial4Content'),
      rating: 5,
      avatar: 'DS',
    },
    {
      name: t('testimonial5Name'),
      role: t('testimonial5Role'),
      content: t('testimonial5Content'),
      rating: 5,
      avatar: 'LM',
    },
    {
      name: t('testimonial6Name'),
      role: t('testimonial6Role'),
      content: t('testimonial6Content'),
      rating: 5,
      avatar: 'MR',
    },
  ]

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-[#10051A] to-[#1a0b2e]">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-6">
            <i className="fas fa-heart text-purple-400" />
            <span>{t('testimonialsSectionBadge')}</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            {t('testimonialsSectionTitle1')}
            <br />
            <span className="text-purple-400">
              {t('testimonialsSectionTitle2')}
            </span>
          </h2>
          <p className="text-lg text-white/70 max-w-2xl mx-auto">
            {t('testimonialsSectionDescription')}
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="group">
              <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 h-full transition-all duration-300 group-hover:bg-white/10 group-hover:border-purple-500/50 group-hover:transform group-hover:scale-105">
                {/* Rating Stars */}
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <i
                      key={i}
                      className="fas fa-star text-yellow-400 text-sm"
                    />
                  ))}
                </div>

                {/* Quote */}
                <div className="mb-6">
                  <i className="fas fa-quote-left text-purple-400/50 text-2xl mb-4 block" />
                  <p className="text-white/80 leading-relaxed italic">
                    {testimonial.content}
                  </p>
                </div>

                {/* Author */}
                <div className="flex items-center gap-3 mt-auto">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-semibold">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-white">
                      {testimonial.name}
                    </div>
                    <div className="text-white/60 text-sm">
                      {testimonial.role}
                    </div>
                  </div>
                </div>

                {/* Decorative Quote Mark */}
                <div className="absolute top-4 right-4 text-purple-400/20 text-4xl">
                  <i className="fas fa-quote-right" />
                </div>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 to-pink-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16">
          <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-2xl p-8 border border-white/10">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  {t('testimonialsStat1')}
                </div>
                <div className="text-white/70 text-sm">
                  {t('photosEnhancedLabel')}
                </div>
              </div>
              <div>
                <div className="text-3xl font-bold text-pink-400 mb-2">
                  {t('testimonialsStat2')}
                </div>
                <div className="text-white/70 text-sm">
                  {t('averageRatingLabel')}
                </div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-400 mb-2">
                  {t('testimonialsStat3')}
                </div>
                <div className="text-white/70 text-sm">
                  {t('happyReviewsLabel')}
                </div>
              </div>
              <div>
                <div className="text-3xl font-bold text-pink-400 mb-2">
                  {t('testimonialsStat4')}
                </div>
                <div className="text-white/70 text-sm">
                  {t('freeToUseLabel')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
