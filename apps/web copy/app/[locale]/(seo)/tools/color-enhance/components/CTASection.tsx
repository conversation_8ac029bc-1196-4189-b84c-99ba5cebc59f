import { Link } from '@i18n/routing'
import CTAButton from './CTAButton'
import { useTranslations } from 'next-intl'

export default function CTASection({ toolUrl }: { toolUrl: string }) {
  const t = useTranslations('colorEnhance')

  return (
    <section className="py-20 px-4 bg-gradient-to-b from-[#2d1a4a] to-[#3b0764]">
      <div className="max-w-6xl mx-auto">
        {/* Main CTA Container */}
        <div className="relative">
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10 rounded-3xl blur-xl" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-3xl" />

          <div className="relative bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-8 md:p-12 text-center">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-8">
              <i className="fas fa-rocket text-purple-400" />
              <span>{t('ctaSectionBadge')}</span>
            </div>

            {/* Main Title */}
            <h2 className="text-3xl md:text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              {t('ctaSectionTitle1')}
              <br />
              <span className="text-purple-400">{t('ctaSectionTitle2')}</span>
            </h2>

            {/* Description */}
            <p className="text-lg md:text-xl text-white/80 max-w-4xl mx-auto mb-8 leading-relaxed">
              {t('ctaSectionDescription')}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
              <Link href={toolUrl}>
                <CTAButton size="lg" className="w-full sm:w-auto">
                  <i className="fas fa-upload mr-2" />
                  {t('ctaSectionButton1')}
                </CTAButton>
              </Link>
              <Link href={toolUrl}>
                <CTAButton
                  variant="secondary"
                  size="lg"
                  className="w-full sm:w-auto"
                >
                  <i className="fas fa-play mr-2" />
                  {t('ctaSectionButton2')}
                </CTAButton>
              </Link>
            </div>

            {/* Features List */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="flex items-center justify-center gap-3 text-white/80">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full flex items-center justify-center">
                  <i className="fas fa-check text-purple-400" />
                </div>
                <span>{t('ctaSectionFeature1')}</span>
              </div>
              <div className="flex items-center justify-center gap-3 text-white/80">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full flex items-center justify-center">
                  <i className="fas fa-shield-alt text-purple-400" />
                </div>
                <span>{t('ctaSectionFeature2')}</span>
              </div>
              <div className="flex items-center justify-center gap-3 text-white/80">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full flex items-center justify-center">
                  <i className="fas fa-user-secret text-purple-400" />
                </div>
                <span>{t('ctaSectionFeature3')}</span>
              </div>
            </div>

            {/* Enhanced Photo Preview */}
            <div className="relative max-w-2xl mx-auto">
              <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 rounded-2xl p-6 border border-white/10">
                <div className="text-center mb-4">
                  <span className="inline-flex items-center gap-2 px-3 py-1 bg-white/20 rounded-full text-white text-sm font-medium">
                    <i className="fas fa-sparkles" />
                    {t('ctaSectionEnhancedPhoto')}
                  </span>
                </div>

                {/* Mock Enhanced Image */}
                <div className="w-full relative h-64 bg-gradient-to-br from-yellow-300/80 via-orange-400/80 to-red-400/60 rounded-xl flex items-center justify-center relative overflow-hidden">
                  <img
                    className=" absolute inset-0 w-full h-full object-cover"
                    src="/images/color-enhance/girl-enhance.webp"
                    alt=""
                  />
                  {/* <div className="text-white text-center z-10">
                    <i className="fas fa-image text-4xl mb-2" />
                    <p className="text-lg font-semibold">
                      {t('ctaSectionYourEnhancedPhoto')}
                    </p>
                    <p className="text-sm opacity-80">
                      {t('ctaSectionProfessionalResults')}
                    </p>
                  </div> */}

                  {/* Animated shine effect */}
                  <div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 animate-pulse"
                    style={{ animationDuration: '2s' }}
                  />
                </div>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-purple-400 mb-1">
                  {t('ctaSectionStat1')}
                </div>
                <div className="text-white/60 text-sm">
                  {t('ctaSectionPhotosEnhancedLabel')}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-pink-400 mb-1">
                  {t('ctaSectionStat2')}
                </div>
                <div className="text-white/60 text-sm">
                  {t('ctaSectionUserRatingLabel')}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-400 mb-1">
                  {t('ctaSectionStat3')}
                </div>
                <div className="text-white/60 text-sm">
                  {t('ctaSectionProcessingTimeLabel')}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-pink-400 mb-1">
                  {t('ctaSectionStat4')}
                </div>
                <div className="text-white/60 text-sm">
                  {t('ctaSectionFreeSecureLabel')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
