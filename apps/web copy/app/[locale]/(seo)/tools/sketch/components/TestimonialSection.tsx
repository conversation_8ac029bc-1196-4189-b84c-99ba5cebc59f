import React from 'react'
import { Star, Quote } from 'lucide-react'
import Image from 'next/image'
import { getTranslations } from 'next-intl/server'

const TestimonialSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('sketch')

  const testimonials = [
    {
      content: t('testimonial1Content'),
      author: t('testimonial1Author'),
      avatar: '/users/user_1.png',
      stars: 5,
    },
    {
      content: t('testimonial2Content'),
      author: t('testimonial2Author'),
      avatar: '/users/user_2.png',
      stars: 5,
    },
    {
      content: t('testimonial3Content'),
      author: t('testimonial3Author'),
      avatar: '/users/user_3.png',
      stars: 5,
    },
    {
      content: t('testimonial4Content'),
      author: t('testimonial4Author'),
      avatar: '/users/user_4.png',
      stars: 5,
    },
    {
      content: t('testimonial5Content'),
      author: t('testimonial5Author'),
      avatar: '/users/user_5.png',
      stars: 5,
    },
    {
      content: t('testimonial6Content'),
      author: t('testimonial6Author'),
      avatar: '/users/user_6.png',
      stars: 5,
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('testimonialTitle')}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 group"
            >
              <Quote className="w-8 h-8 text-purple-400 mb-4" />
              <div className="flex mb-4">
                {[...Array(testimonial.stars)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-yellow-400 fill-yellow-400"
                  />
                ))}
              </div>
              <p className="text-gray-300 mb-6 italic text-lg flex-grow">
                {testimonial.content}
              </p>
              <div className="flex items-center mt-auto">
                <Image
                  src={testimonial.avatar}
                  alt={testimonial.author}
                  width={40}
                  height={40}
                  className="rounded-full mr-4"
                />
                <div>
                  <p className="font-semibold text-white">
                    {testimonial.author}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
