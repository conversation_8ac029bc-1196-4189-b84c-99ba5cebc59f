import React from 'react'
import { getTranslations } from 'next-intl/server'

const FAQSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('sketch')

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
    {
      question: t('faq5Question'),
      answer: t('faq5Answer'),
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('faqTitle')}
        </h2>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <details
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 transition-all duration-300 group"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.1}s ease-out both`,
              }}
              open={index === 0}
            >
              <summary className="w-full text-left p-6 flex justify-between items-center cursor-pointer font-semibold text-white text-lg">
                {faq.question}
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 20 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-gray-400 transition-transform duration-300 group-open:rotate-180 group-open:text-purple-400"
                >
                  <path
                    d="M5 7.5L10 12.5L15 7.5"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </summary>

              <div className="px-6 pb-6 pt-2 text-gray-400">
                <div className="space-y-4">
                  {faq.answer.split('\n').map((paragraph, i) =>
                    paragraph.startsWith('-') ? (
                      <ul key={i} className="pl-5 space-y-2">
                        <li className="list-disc list-outside">
                          {paragraph.substring(1).trim()}
                        </li>
                      </ul>
                    ) : (
                      <p key={i} className="text-gray-300">
                        {paragraph}
                      </p>
                    )
                  )}
                </div>
              </div>
            </details>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FAQSection
