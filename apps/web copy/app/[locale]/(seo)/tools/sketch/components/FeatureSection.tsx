import React from 'react'
import { Upload, <PERSON>, Zap } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

const FeatureSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('sketch')

  const features = [
    {
      icon: <Upload className="w-12 h-12 text-[#339bfa]" />,
      title: t('feature1Title'),
      description: t('feature1Description'),
    },
    {
      icon: <Brain className="w-12 h-12 text-pink-400" />,
      title: t('feature2Title'),
      description: t('feature2Description'),
    },
    {
      icon: <Zap className="w-12 h-12 text-fuchsia-400" />,
      title: t('feature3Title'),
      description: t('feature3Description'),
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('featureTitle')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 flex flex-col"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-gradient-to-br from-slate-800 to-slate-900 p-4 rounded-xl mr-4 flex-shrink-0">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-white">
                  {feature.title}
                </h3>
              </div>
              <div className="text-gray-400 space-y-4 flex-grow">
                {feature.description.split('\n').map((paragraph, i) => (
                  <p key={i} className="text-gray-300">
                    {paragraph}
                  </p>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FeatureSection
