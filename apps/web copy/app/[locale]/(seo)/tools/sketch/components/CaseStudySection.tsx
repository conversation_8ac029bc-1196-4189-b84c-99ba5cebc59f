import React from 'react'
import StaticImageSplit from '../../components/StaticImageSplit'
import { getTranslations } from 'next-intl/server'

const CaseStudySection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('sketch')

  const caseStudies = [
    {
      title: t('caseStudy1Title'),
      description: t('caseStudy1Description'),
      beforeImage: '/samples/sketch-artist-before.jpg',
      afterImage: '/samples/sketch-artist-after.jpg',
    },
    {
      title: t('caseStudy2Title'),
      description: t('case2Description'),
      beforeImage: '/samples/sketch-architecture-before.jpg',
      afterImage: '/samples/sketch-architecture-after.jpg',
    },
    {
      title: t('caseStudy3Title'),
      description: t('caseStudy3Description'),
      beforeImage: '/samples/sketch-doodle-before.jpg',
      afterImage: '/samples/sketch-doodle-after.jpg',
    },
    {
      title: t('caseStudy4Title'),
      description: t('caseStudy4Description'),
      beforeImage: '/samples/sketch-product-before.jpg',
      afterImage: '/samples/sketch-product-after.jpg',
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('caseStudyTitle')}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          {caseStudies.map((caseStudy, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl overflow-hidden group transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              <div className="overflow-hidden transition-transform duration-500 ease-in-out group-hover:scale-[1.01]">
                <StaticImageSplit
                  leftImage={caseStudy.beforeImage}
                  rightImage={caseStudy.afterImage}
                  fullImageMode
                />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-semibold mb-3 text-white">
                  {caseStudy.title}
                </h3>
                <p className="text-gray-400">{caseStudy.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CaseStudySection
