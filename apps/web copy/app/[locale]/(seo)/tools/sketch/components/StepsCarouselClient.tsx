'use client'
import React, { useState, useEffect, useRef, ReactNode } from 'react'
import Image from 'next/image'

interface Step {
  icon: ReactNode
  title: string
  description: string
  image: string
}

interface StepsCarouselClientProps {
  steps: Step[]
}

export default function StepsCarouselClient({
  steps,
}: StepsCarouselClientProps) {
  const [activeStep, setActiveStep] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [progress, setProgress] = useState(0)
  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const DURATION = 5000 // 5秒轮播一次
  const UPDATE_INTERVAL = 50 // 进度条更新间隔

  // 使用单一计时器处理进度和轮播
  useEffect(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }

    if (!isPaused) {
      timerRef.current = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + (100 * UPDATE_INTERVAL) / DURATION

          // 当进度到100%时，切换到下一步
          if (newProgress >= 100) {
            // 首先清除当前计时器，防止重复触发
            if (timerRef.current) {
              clearInterval(timerRef.current)
            }

            // 确保按照 1->2->3->1 的顺序进行
            const nextStep = (activeStep + 1) % steps.length

            // 在下一帧更新，确保平滑过渡
            setTimeout(() => {
              setActiveStep(nextStep)
              setProgress(0)
            }, 0)

            return 100
          }
          return newProgress
        })
      }, UPDATE_INTERVAL)
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [activeStep, isPaused, steps.length])

  // 重置进度条并更改活动步骤
  const handleStepClick = (index: number) => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
    }
    setActiveStep(index)
    setProgress(0)
  }

  // 鼠标进入当前活动步骤时暂停
  const handleStepMouseEnter = (index: number) => {
    if (index === activeStep) {
      setIsPaused(true)
    }
  }

  // 鼠标离开当前活动步骤时恢复
  const handleStepMouseLeave = (index: number) => {
    if (index === activeStep) {
      setIsPaused(false)
    }
  }

  // 触摸事件处理
  const handleStepTouchStart = (index: number) => {
    if (index === activeStep) {
      setIsPaused(true)
    }
  }

  const handleStepTouchEnd = (index: number) => {
    if (index === activeStep) {
      setIsPaused(false)
    }
  }

  // 渲染移动端指示器
  const renderStepIndicators = () => (
    <div className="flex justify-center space-x-2 mb-6 md:hidden">
      {steps.map((_, index) => (
        <button
          key={index}
          onClick={() => handleStepClick(index)}
          className={`h-2.5 rounded-full transition-all ${
            activeStep === index
              ? 'w-8 bg-gradient-to-r from-[#e14eca] to-[#ba54f5]'
              : 'w-2.5 bg-[#2b2049]'
          }`}
          aria-label={`Step ${index + 1}`}
        />
      ))}
    </div>
  )

  // 渲染单个步骤项
  const renderStepItem = (step: Step, index: number) => (
    <div
      key={index}
      className={`flex gap-4 p-6 rounded-lg transition-all duration-300 cursor-pointer ${
        activeStep === index
          ? 'bg-[#191335] border border-[#2b2049]'
          : 'bg-[#191335]/30 hover:bg-[#191335]/80 border border-transparent'
      }`}
      onClick={() => handleStepClick(index)}
      onMouseEnter={() => handleStepMouseEnter(index)}
      onMouseLeave={() => handleStepMouseLeave(index)}
      onTouchStart={() => handleStepTouchStart(index)}
      onTouchEnd={() => handleStepTouchEnd(index)}
    >
      <div className="flex-shrink-0 mt-1">{step.icon}</div>
      <div className="w-full">
        <h3 className="font-semibold text-lg mb-2 text-white">{step.title}</h3>
        <p className="text-gray-300 text-sm md:line-clamp-none line-clamp-3">
          {step.description}
        </p>

        {activeStep === index && (
          <div className="h-1 bg-[#2b2049] rounded-full mt-4 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-[#e14eca] to-[#ba54f5] rounded-full transition-all duration-100 ease-linear"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        )}
      </div>
    </div>
  )

  return (
    <>
      {/* 移动端显示指示器 */}
      {renderStepIndicators()}

      <div className="flex flex-col md:flex-row gap-8 items-center">
        {/* 步骤列表 */}
        <div className="w-full md:w-1/2 order-2 md:order-none space-y-6">
          {steps.map((step, index) => renderStepItem(step, index))}
        </div>

        {/* 图片展示区 */}
        <div className="w-full md:w-1/2 bg-[#191335] border border-[#2b2049] rounded-xl overflow-hidden h-[300px] md:h-[400px] relative flex items-center justify-center order-1 md:order-none">
          {steps.map((step, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-500 flex items-center justify-center ${
                activeStep === index
                  ? 'opacity-100'
                  : 'opacity-0 pointer-events-none'
              }`}
              onMouseEnter={() => setIsPaused(true)}
              onMouseLeave={() => setIsPaused(false)}
              onTouchStart={() => setIsPaused(true)}
              onTouchEnd={() => setIsPaused(false)}
            >
              <Image
                fill
                src={step.image}
                alt={step.title}
                className="w-full h-full object-cover"
              />
            </div>
          ))}
        </div>
      </div>
    </>
  )
}
