'use client'
import { Tilt } from '@jdion/tilt-react'
import Image from 'next/image'

export default function SampleImage() {
  return (
    <div className="mt-8">
      <p className="text-gray-400 mb-4">No image? Try one of these:</p>
      <div className="grid grid-cols-4 gap-4 [perspective:1200px]">
        {['sketch-1.png', 'sketch-2.png', 'sketch-3.png', 'sketch-4.png'].map(
          (item) => (
            <Tilt key={item}>
              <div
                key={item}
                className="relative aspect-square rounded-lg overflow-hidden cursor-pointer border-2 border-transparent transition-all duration-300 hover:border-purple-500 hover:[transform:rotateX(10deg)_rotateY(-10deg)_scale(1.1)] hover:shadow-2xl hover:shadow-purple-500/30"
              >
                <Image
                  src={`/samples/${item}`}
                  alt="Sample Image"
                  fill
                  className="object-cover bg-slate-800"
                />
              </div>
            </Tilt>
          )
        )}
      </div>
    </div>
  )
}
