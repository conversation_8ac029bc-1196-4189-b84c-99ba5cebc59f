import { UploadCloud } from 'lucide-react'
import { getLocale, getTranslations } from 'next-intl/server'
import FeatureSection from './components/FeatureSection'
import CaseStudySection from './components/CaseStudySection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'
import { Link } from '@i18n/routing'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('aiHug.metaTitle'),
    description: t('aiHug.metaDescription'),
    keywords: t('aiHug.metaKeywords'),
    openGraph: {
      title: t('aiHug.ogTitle'),
      description: t('aiHug.ogDescription'),
      url: 'https://www.imggen.org/tools/ai-hug',
      type: 'website',
      images: [
        {
          url: 'https://www.imggen.org/images/og-ai-hug.jpg',
          width: 1200,
          height: 630,
          alt: t('aiHug.ogTitle'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('aiHug.twitterTitle'),
      description: t('aiHug.twitterDescription'),
      images: ['https://www.imggen.org/images/twitter-ai-hug.jpg'],
    },
  }
}

const AIHugPage = async () => {
  const t = await getTranslations()
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('aiHug.jsonLdName'),
    description: t('aiHug.jsonLdDescription'),
    applicationCategory: 'MultimediaApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '5280',
    },
    url: 'https://www.imggen.org/tools/ai-hug',
    image: 'https://www.imggen.org/images/ai-hug-schema.jpg',
  }

  // 统一配置跳转URL
  const AI_HUG_TOOL_URL = '/ai/ai-hug'

  return (
    <div className="relative h-full w-full bg-[#0f172a]">
      <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)]"></div>

      {/* Animated background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1),rgba(255,255,255,0)_50%)]"></div>
        <div
          className="absolute h-[200px] w-[400px] bg-purple-500 rounded-full blur-[100px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse"
          style={{ animationDuration: '6s' }}
        ></div>
        <div
          className="absolute h-[150px] w-[300px] bg-pink-500 rounded-full blur-[80px] top-1/4 left-1/4 animate-pulse"
          style={{ animationDuration: '8s', animationDelay: '2s' }}
        ></div>
        <div
          className="absolute h-[180px] w-[350px] bg-fuchsia-500 rounded-full blur-[90px] bottom-1/4 right-1/4 animate-pulse"
          style={{ animationDuration: '7s', animationDelay: '4s' }}
        ></div>
      </div>

      {/* Add JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <div className="relative z-10 my-20">
        <div className="relative container mx-auto px-4 md:px-8 py-12 md:py-24">
          <div className="text-center animate-fade-in-up">
            {/* Hero Section */}
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-fuchsia-400">
              {t('aiHug.pageTitle')}
            </h1>
            <p className="text-lg md:text-xl text-gray-400 mb-12 font-normal max-w-4xl mx-auto">
              {t('aiHug.pageDescription')}
            </p>

            {/* Main Content Section - Left Right Layout */}
            <div className="max-w-7xl mx-auto mb-16">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center relative">
                {/* Left Side - Upload Section */}
                <div className="space-y-8">
                  <div className="w-full group [perspective:1000px]">
                    <div className="border-2 border-dashed border-gray-700 rounded-xl p-8 bg-slate-900/20 backdrop-blur-sm transition-all duration-500 group-hover:border-purple-500 [transform:rotateX(0deg)] group-hover:[transform:rotateX(10deg)]">
                      <div className="flex flex-col items-center">
                        <div className="relative">
                          <Link href={AI_HUG_TOOL_URL}>
                            <button className="relative inline-block p-px font-semibold leading-6 text-white bg-gray-800 shadow-2xl cursor-pointer group rounded-xl shadow-zinc-900 transition-transform duration-300 ease-in-out hover:scale-105 active:scale-95">
                              <span className="absolute inset-0 rounded-xl bg-gradient-to-r from-pink-500 to-purple-600 p-[2px] opacity-0 transition-opacity duration-500 group-hover:opacity-100"></span>
                              <span className="relative z-10 block px-6 py-3 rounded-xl bg-slate-950 text-gray-400 hover:text-gray-300 hover:font-semibold font-normal">
                                <div className="relative z-10 flex items-center space-x-2">
                                  <UploadCloud className="w-5 h-5 transition-transform duration-500" />
                                  <span className="transition-all duration-500">
                                    {t('aiHug.uploadButton')}
                                  </span>
                                </div>
                              </span>
                            </button>
                          </Link>
                        </div>
                        <p className="text-gray-400 mt-4">
                          {t('aiHug.uploadDragAndDrop')}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Features List with Image */}
                  <div className="flex items-center gap-6">
                    <div className="space-y-4 flex-1">
                      {[
                        { icon: '✨', text: t('aiHug.feature1') },
                        { icon: '🎥', text: t('aiHug.feature2') },
                        { icon: '⚡', text: t('aiHug.feature3') },
                        { icon: '🆓', text: t('aiHug.feature4') },
                      ].map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-center space-x-3 text-gray-300"
                        >
                          <span className="text-lg">{feature.icon}</span>
                          <span className="text-lg">{feature.text}</span>
                        </div>
                      ))}
                    </div>

                    {/* Feature Image */}
                    <div className="flex-shrink-0 relative">
                      <img
                        src="/seo/ai-hug/C-1.png"
                        alt="AI Hug Feature Preview"
                        className="w-64 h-38 object-cover rounded-xl shadow-lg border border-slate-600/30"
                      />
                      {/* Arrow Image */}
                      <img
                        src="/seo/ai-hug/quanquan2.png"
                        alt="Arrow"
                        className="absolute -right-20 top-1/2 transform -translate-y-1/2 w-20 h-24"
                      />
                    </div>
                  </div>
                </div>

                {/* Right Side - AI Hug Video Demo */}
                <div className="flex items-center justify-center h-full">
                  {/* Video Demo */}
                  <div className="relative bg-gradient-to-br from-slate-900/60 to-purple-900/20 backdrop-blur-sm rounded-2xl p-8 border border-slate-700/50 w-full max-w-lg">
                    <video
                      className="w-full h-auto rounded-xl shadow-2xl"
                      autoPlay
                      loop
                      muted
                      playsInline
                    >
                      <source
                        src="https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/C-2.mp4"
                        type="video/mp4"
                      />
                      {t('aiHug.videoUnsupported')}
                    </video>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Components Sections */}
      <FeatureSection toolUrl={AI_HUG_TOOL_URL} />
      <CaseStudySection toolUrl={AI_HUG_TOOL_URL} />
      <HowToGuideSection toolUrl={AI_HUG_TOOL_URL} />
      <TestimonialSection toolUrl={AI_HUG_TOOL_URL} />
      <FAQSection toolUrl={AI_HUG_TOOL_URL} />
    </div>
  )
}

export default AIHugPage
