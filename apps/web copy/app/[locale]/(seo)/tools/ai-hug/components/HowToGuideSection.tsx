import React from 'react'
import { Upload, Download } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'

const HowToGuideSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations()

  const steps = [
    {
      icon: <Upload className="w-12 h-12 text-[#339bfa]" />,
      title: t('aiHug.step1Title'),
      description: t('aiHug.step1Description'),
      stepNumber: '01',
    },
    {
      icon: <Download className="w-12 h-12 text-pink-400" />,
      title: t('aiHug.step2Title'),
      description: t('aiHug.step2Description'),
      stepNumber: '02',
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-6">
          {t('aiHug.howToTitle')}
        </h2>
        <p className="text-lg text-gray-400 text-center mb-24 max-w-3xl mx-auto">
          {t('aiHug.howToDescription')}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16">
          {steps.map((step, index) => (
            <div
              key={index}
              className="relative bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.3}s ease-out both`,
              }}
            >
              {/* Step Number */}
              <div className="absolute -top-6 -left-6 w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-500 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                {step.stepNumber}
              </div>

              {/* Icon and Title */}
              <div className="flex items-center mb-6">
                <div className="bg-gradient-to-br from-slate-800 to-slate-900 p-4 rounded-xl mr-4 flex-shrink-0">
                  {step.icon}
                </div>
                <h3 className="text-2xl font-semibold text-white">
                  {step.title}
                </h3>
              </div>

              {/* Description */}
              <p className="text-gray-300 text-lg leading-relaxed">
                {step.description}
              </p>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="text-center">
          <Link href={toolUrl}>
            <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 inline-block px-10 py-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium text-lg rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20">
              {t('aiHug.createButton')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default HowToGuideSection
