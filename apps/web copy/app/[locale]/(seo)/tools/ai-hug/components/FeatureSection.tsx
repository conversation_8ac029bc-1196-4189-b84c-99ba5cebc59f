import React from 'react'
import { <PERSON><PERSON>, Shield } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'

const FeatureSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations()

  const features = [
    {
      icon: <Zap className="w-12 h-12 text-[#339bfa]" />,
      title: t('aiHug.feature1Title'),
      subtitle: t('aiHug.feature1Subtitle'),
      description: t('aiHug.feature1Description'),
    },
    {
      icon: <Shield className="w-12 h-12 text-pink-400" />,
      title: t('aiHug.feature2Title'),
      subtitle: t('aiHug.feature2Subtitle'),
      description: t('aiHug.feature2Description'),
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-6">
          {t('aiHug.featureSectionTitle')}
        </h2>
        <p className="text-lg text-gray-400 text-center mb-24 max-w-3xl mx-auto">
          {t('aiHug.featureSectionDescription')}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 flex flex-col"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              <div className="flex items-center mb-6">
                <div className="bg-gradient-to-br from-slate-800 to-slate-900 p-4 rounded-xl mr-4 flex-shrink-0">
                  {feature.icon}
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-2">
                    {feature.title}
                  </h3>
                  <h4 className="text-lg font-medium text-purple-400">
                    {feature.subtitle}
                  </h4>
                </div>
              </div>
              <p className="text-gray-300 flex-grow">{feature.description}</p>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Link href={toolUrl}>
            <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 inline-block px-8 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20">
              {t('aiHug.tryForFreeButton')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default FeatureSection
