import React from 'react'
import Image from 'next/image'
import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'

const CaseStudySection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations()

  const caseStudies = [
    {
      title: t('aiHug.case1Title'),
      description: t('aiHug.case1Description'),
      imageDescription:
        'A grandfather and granddaughter sharing a warm embrace, created with our ai hug generator.',
      ctaText: t('aiHug.case1Cta'),
      layout: 'left-image',
      videoSrc:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/E-2.mp4',
      thumbnailSrc: '/seo/ai-hug/E-1.jpeg',
    },
    {
      title: t('aiHug.case2Title'),
      description: t('aiHug.case2Description'),
      imageDescription:
        'Two teenage athletes celebrating with a hug, turned into a virtual hug gif.',
      ctaText: t('aiHug.case2Cta'),
      layout: 'right-image',
      videoSrc:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/B-2.mp4',
      thumbnailSrc: '/seo/ai-hug/B-1.png',
    },
    {
      title: t('aiHug.case3Title'),
      description: t('aiHug.case3Description'),
      imageDescription:
        'Two professionals hugging to celebrate a success, an image generated by our ai tool.',
      ctaText: t('aiHug.case3Cta'),
      layout: 'left-image',
      videoSrc:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/F-2.mp4',
      thumbnailSrc: '/seo/ai-hug/F-1.jpeg',
    },
    {
      title: t('aiHug.case4Title'),
      description: t('aiHug.case4Description'),
      imageDescription:
        'Two artists sharing a creative hug, showcasing our hug images generator.',
      ctaText: t('aiHug.case4Cta'),
      layout: 'right-image',
      videoSrc:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/ai-hug/A-2.mp4',
      thumbnailSrc: '/seo/ai-hug/A-1.png',
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-6">
          {t('aiHug.caseStudyTitle')}
        </h2>
        <p className="text-lg text-gray-400 text-center mb-24 max-w-3xl mx-auto">
          {t('aiHug.caseStudyDescription')}
        </p>

        <div className="space-y-20">
          {caseStudies.map((caseStudy, index) => (
            <div
              key={index}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                caseStudy.layout === 'right-image'
                  ? 'lg:grid-flow-col-dense'
                  : ''
              }`}
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              {/* Content */}
              <div
                className={
                  caseStudy.layout === 'right-image' ? 'lg:col-start-2' : ''
                }
              >
                <h3 className="text-3xl font-semibold mb-6 text-white">
                  {caseStudy.title}
                </h3>
                <p className="text-gray-300 text-lg mb-8 leading-relaxed">
                  {caseStudy.description}
                </p>
                <Link href={toolUrl}>
                  <button className="relative p-px leading-6 bg-slate-900 cursor-pointer duration-300 ease-in-out hover:scale-105 active:scale-95 inline-block px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-medium rounded-full hover:opacity-90 transition-all hover:-translate-y-1 shadow-lg shadow-purple-500/20">
                    {caseStudy.ctaText}
                  </button>
                </Link>
              </div>

              {/* Image/Video */}
              <div
                className={
                  caseStudy.layout === 'right-image' ? 'lg:col-start-1' : ''
                }
              >
                <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10">
                  {caseStudy.videoSrc ? (
                    // Show video with thumbnail overlay
                    <div className="relative">
                      <video
                        className="w-full h-auto rounded-xl shadow-2xl"
                        autoPlay
                        loop
                        muted
                        playsInline
                      >
                        <source src={caseStudy.videoSrc} type="video/mp4" />
                        {t('aiHug.videoUnsupported')}
                      </video>

                      {/* Thumbnail overlay in bottom right */}
                      <div className="absolute bottom-4 right-4 w-24 h-16 rounded-lg overflow-hidden border-2 border-white/80 shadow-lg">
                        <img
                          src={caseStudy.thumbnailSrc}
                          alt="Original image"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black/10"></div>
                        <div className="absolute bottom-0 left-0 right-0 text-white text-xs font-medium bg-black/70 px-2 py-1 text-center">
                          {t('aiHug.originalLabel')}
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Show placeholder
                    <div className="aspect-video bg-gradient-to-br from-purple-900/20 to-pink-900/20 rounded-xl flex items-center justify-center">
                      <div className="text-center text-gray-400">
                        <div className="w-16 h-16 bg-purple-600/30 rounded-full flex items-center justify-center mb-4 mx-auto">
                          <svg
                            className="w-8 h-8"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <p className="text-sm">{caseStudy.imageDescription}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CaseStudySection
