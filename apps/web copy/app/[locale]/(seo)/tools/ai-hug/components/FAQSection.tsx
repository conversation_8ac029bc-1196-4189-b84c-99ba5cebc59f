'use client'
import React, { useState } from 'react'
import { ChevronDown } from 'lucide-react'
import { useTranslations } from 'next-intl'

const FAQSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations()

  const faqs = [
    {
      question: t('aiHug.faq1Question'),
      answer: t('aiHug.faq1Answer'),
    },
    {
      question: t('aiHug.faq2Question'),
      answer: t('aiHug.faq2Answer'),
    },
    {
      question: t('aiHug.faq3Question'),
      answer: t('aiHug.faq3Answer'),
    },
    {
      question: t('aiHug.faq4Question'),
      answer: t('aiHug.faq4Answer'),
    },
  ]

  const [openIndices, setOpenIndices] = useState<number[]>([0])

  const toggleFAQ = (index: number) => {
    setOpenIndices((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    )
  }

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('aiHug.faqTitle')}
        </h2>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => {
            const isOpen = openIndices.includes(index)
            return (
              <div
                key={index}
                className={`bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 transition-all duration-300 ${
                  isOpen ? 'border-purple-500' : 'hover:border-slate-700'
                }`}
                style={{
                  animation: `fade-in-up 0.5s ${index * 0.1}s ease-out both`,
                }}
              >
                <button
                  className="w-full text-left p-6 flex justify-between items-center cursor-pointer"
                  onClick={() => toggleFAQ(index)}
                >
                  <span className="font-semibold text-white text-lg">
                    {faq.question}
                  </span>
                  <ChevronDown
                    className={`w-5 h-5 text-gray-400 transition-transform duration-300 ${
                      isOpen ? 'transform rotate-180 text-purple-400' : ''
                    }`}
                  />
                </button>

                <div
                  className={`grid transition-all duration-500 ease-in-out ${
                    isOpen
                      ? 'grid-rows-[1fr] opacity-100'
                      : 'grid-rows-[0fr] opacity-0'
                  }`}
                >
                  <div className="overflow-hidden">
                    <div className="px-6 pb-6 pt-2 text-gray-400 transition-colors duration-300">
                      <div
                        className={`transition-opacity duration-500 delay-200 ${
                          isOpen ? 'opacity-100' : 'opacity-0'
                        }`}
                      >
                        <p className="text-gray-300 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default FAQSection
