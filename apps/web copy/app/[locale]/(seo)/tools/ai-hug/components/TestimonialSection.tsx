import React from 'react'
import { Star, Quote } from 'lucide-react'
import Image from 'next/image'
import { getTranslations } from 'next-intl/server'

const TestimonialSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations()

  const testimonials = [
    {
      content: t('aiHug.testimonial1'),
      author: t('aiHug.testimonial1Author'),
      avatar: '/users/user_1.png',
      stars: 5,
    },
    {
      content: t('aiHug.testimonial2'),
      author: t('aiHug.testimonial2Author'),
      avatar: '/users/user_2.png',
      stars: 5,
    },
    {
      content: t('aiHug.testimonial3'),
      author: t('aiHug.testimonial3Author'),
      avatar: '/users/user_3.png',
      stars: 5,
    },
    {
      content: t('aiHug.testimonial4'),
      author: t('aiHug.testimonial4Author'),
      avatar: '/users/user_4.png',
      stars: 5,
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('aiHug.testimonialTitle')}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 flex flex-col"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.15}s ease-out both`,
              }}
            >
              <Quote className="w-6 h-6 text-purple-400 mb-4" />

              {/* Stars */}
              <div className="flex mb-4">
                {[...Array(testimonial.stars)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-4 h-4 text-yellow-400 fill-yellow-400"
                  />
                ))}
              </div>

              {/* Content */}
              <p className="text-gray-300 mb-6 italic text-sm flex-grow leading-relaxed">
                "{testimonial.content}"
              </p>

              {/* Author */}
              <div className="flex items-center mt-auto">
                <Image
                  src={testimonial.avatar}
                  alt={testimonial.author}
                  width={32}
                  height={32}
                  className="rounded-full mr-3"
                />
                <div>
                  <p className="font-semibold text-white text-sm">
                    {testimonial.author}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
