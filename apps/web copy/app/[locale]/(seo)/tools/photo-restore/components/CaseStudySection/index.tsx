import { getTranslations } from 'next-intl/server';
import React from 'react'
import StaticImageSplit from '../../../components/StaticImageSplit'

const CaseStudySection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations();
  const caseStudies = [
    {
      title: t('photorestore.caseStudy1Title'),
      description: t('photorestore.caseStudy1Description'),
      beforeImage: '/samples/photo-restoration-case-1-before.png',
      afterImage: '/samples/photo-restoration-case-1-after.png',
    },
    {
      title: t('photorestore.caseStudy2Title'),
      description: t('photorestore.caseStudy2Description'),
      beforeImage: '/samples/photo-restoration-case-2-before.png',
      afterImage: '/samples/photo-restoration-case-2-after.png',
    },
    {
      title: t('photorestore.caseStudy3Title'),
      description: t('photorestore.caseStudy3Description'),
      beforeImage: '/samples/photo-restoration-case-3-before.png',
      afterImage: '/samples/photo-restoration-case-3-after.png',
    },
    {
      title: t('photorestore.caseStudy4Title'),
      description: t('photorestore.caseStudy4Description'),
      beforeImage: '/samples/photo-restoration-case-4-before.png',
      afterImage: '/samples/photo-restoration-case-4-after.png',
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-8">
          {t('photorestore.caseStudyTitle')}
        </h2>

        <div className="text-center mb-16">
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed" dangerouslySetInnerHTML={{ __html: t('photorestore.caseStudyDescription') }} />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
          {caseStudies.map((caseStudy, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl overflow-hidden group transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              <div className="overflow-hidden transition-transform duration-500 ease-in-out group-hover:scale-[1.01]">
                <StaticImageSplit
                  leftImage={caseStudy.beforeImage}
                  rightImage={caseStudy.afterImage}
                />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-semibold mb-3 text-white">
                  {caseStudy.title}
                </h3>
                <p
                  className="text-gray-400"
                  dangerouslySetInnerHTML={{ __html: caseStudy.description }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default CaseStudySection
