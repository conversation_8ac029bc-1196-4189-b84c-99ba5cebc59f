import { getTranslations } from 'next-intl/server';
import React from 'react'
import { Shield, Lock, Palette } from 'lucide-react'
import Image from 'next/image'

const FeatureSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations();
  const features = [
    {
      icon: <Shield className="w-12 h-12 text-[#339bfa]" />,
      title: t('photorestore.feature1Title'),
      description: t('photorestore.feature1Description'),
      layout: 'left-content',
      image: '/samples/enhancer restore old photo.gif',
    },
    {
      icon: <Lock className="w-12 h-12 text-pink-400" />,
      title: t('photorestore.feature2Title'),
      description: t('photorestore.feature2Description'),
      layout: 'right-content',
      image: '/samples/istockphoto-2084953035-612x612.webp',
    },
    {
      icon: <Palette className="w-12 h-12 text-fuchsia-400" />,
      title: t('photorestore.feature3Title'),
      description: t('photorestore.feature3Description'),
      layout: 'left-content',
      image: '/samples/istockphoto-1199145131-612x612.webp',
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('photorestore.featureSectionTitle')}
        </h2>

        <div className="space-y-20">
          {features.map((feature, index) => (
            <div
              key={index}
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                feature.layout === 'right-content'
                  ? 'lg:grid-flow-col-dense'
                  : ''
              }`}
              style={{
                animation: `fade-in-up 0.5s ${index * 0.2}s ease-out both`,
              }}
            >
              {/* Content Column */}
              <div
                className={`${
                  feature.layout === 'right-content' ? 'lg:col-start-2' : ''
                }`}
              >
                <div className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10">
                  <div className="flex items-center mb-6">
                    <div className="bg-gradient-to-br from-slate-800 to-slate-900 p-4 rounded-xl mr-4 flex-shrink-0">
                      {feature.icon}
                    </div>
                    <h3 className="text-2xl font-semibold text-white">
                      {feature.title}
                    </h3>
                  </div>
                  <div className="text-gray-400 space-y-4">
                    <p
                      className="text-gray-300 leading-relaxed"
                      dangerouslySetInnerHTML={{ __html: feature.description }}
                    />
                  </div>
                </div>
              </div>

              {/* Image Column */}
              <div
                className={`${
                  feature.layout === 'right-content' ? 'lg:col-start-1' : ''
                }`}
              >
                <div className="rounded-2xl overflow-hidden shadow-2xl border border-gray-800 transition-all duration-300 hover:border-purple-500">
                  <div className="relative aspect-video w-full">
                    <Image
                      src={feature.image}
                      alt={feature.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FeatureSection
