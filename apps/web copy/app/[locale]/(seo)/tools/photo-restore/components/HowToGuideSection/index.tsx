import { getTranslations } from 'next-intl/server'
import React from 'react'
import { Upload, Download } from 'lucide-react'
import { Link } from '@i18n/routing'

const HowToGuideSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations()
  const steps = [
    {
      icon: <Upload className="w-10 h-10 text-[#339bfa]" />,
      title: t('photorestore.howToStep1Title'),
      description: t('photorestore.howToStep1Description'),
      imageSrc: '/path-to-your-upload-image.jpg',
    },
    {
      icon: <Download className="w-10 h-10 text-fuchsia-500" />,
      title: t('photorestore.howToStep2Title'),
      description: t('photorestore.howToStep2Description'),
      imageSrc: '/path-to-your-download-image.jpg',
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('photorestore.howToTitle')}
        </h2>
        <p className="text-lg text-gray-400 text-center mb-16 max-w-3xl mx-auto">
          {t('photorestore.howToDescription')}
        </p>

        <div className="max-w-6xl mx-auto space-y-16">
          {/* Step 1 */}
          <div className="grid md:grid-cols-2 gap-8 items-center">
            {/* Left content */}
            <div className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-slate-800 to-slate-900 text-[#339bfa] mb-6">
                <Upload className="w-6 h-6" />
              </div>

              <h3 className="text-xl font-semibold text-white mb-4">
                {steps[0].title}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {steps[0].description}
              </p>
            </div>

            {/* Right image */}
            <div className="relative h-[280px] md:h-[320px] rounded-xl overflow-hidden">
              <img
                src="/seo/Upload-Your-Damaged-Photo.jpg"
                alt="Upload Your Damaged Photo"
                className="absolute inset-0 w-full h-full object-cover"
                loading="lazy"
              />
            </div>
          </div>

          {/* Step 2 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            {/* Image placeholder */}
            <div className="aspect-video bg-gray-100 rounded-xl overflow-hidden relative">
              <img
                src="/seo/Restore-and-Download-Instantly.jpg"
                alt="Restore and Download Instantly"
                className="w-full h-full object-cover"
              />
            </div>

            <div className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10">
              {/* Icon */}
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-slate-800 to-slate-900 text-fuchsia-500 mb-6">
                <Download className="w-6 h-6" />
              </div>

              <h3 className="text-xl font-semibold text-white mb-4">
                {steps[1].title}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {steps[1].description}
              </p>
            </div>
          </div>
        </div>

        {/* CTA Button */}
        <div className="mt-16 text-center">
          <Link href={toolUrl}>
            <button className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-purple-500 to-pink-500 rounded-full hover:opacity-90 transition-opacity">
              {t('photorestore.howToCta')}
              <svg
                className="ml-2 w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default HowToGuideSection
