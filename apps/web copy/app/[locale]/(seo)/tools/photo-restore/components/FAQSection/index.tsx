import { getTranslations } from 'next-intl/server'
import React from 'react'
import { ChevronDown } from 'lucide-react'

const FAQSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations()
  const faqs = [
    {
      question: t('photorestore.faq1Question'),
      answer: t('photorestore.faq1Answer'),
    },
    {
      question: t('photorestore.faq2Question'),
      answer: t('photorestore.faq2Answer'),
    },
    {
      question: t('photorestore.faq3Question'),
      answer: t('photorestore.faq3Answer'),
    },
    {
      question: t('photorestore.faq4Question'),
      answer: t('photorestore.faq4Answer'),
    },
  ]

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('photorestore.faqTitle')}
        </h2>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <details
              key={index}
              className="group bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 open:border-purple-500 hover:border-slate-700"
              open={index === 0}
            >
              <summary className="w-full text-left p-6 flex justify-between items-center cursor-pointer list-none">
                <span className="font-semibold text-white text-lg">
                  {faq.question}
                </span>
                <ChevronDown className="w-5 h-5 text-gray-400 transition-transform duration-300 group-open:rotate-180 group-open:text-purple-400" />
              </summary>

              <div className="px-6 pb-6 pt-2 text-gray-400">
                <div className="space-y-4">
                  {faq.answer.split('\n').map((paragraph, i) =>
                    paragraph.startsWith('-') ? (
                      <ul key={i} className="pl-5 space-y-2">
                        <li className="list-disc list-outside">
                          {paragraph.substring(1).trim()}
                        </li>
                      </ul>
                    ) : (
                      <p key={i} className="text-gray-300">
                        {paragraph}
                      </p>
                    )
                  )}
                </div>
              </div>
            </details>
          ))}
        </div>

        {/* <style jsx>{`
          details > summary::-webkit-details-marker {
            display: none;
          }
        `}</style> */}
      </div>
    </section>
  )
}

export default FAQSection
