import { getTranslations } from 'next-intl/server'
import SampleImageClient from './SampleImageClient'

export default async function SampleImageServer() {
  const t = await getTranslations()

  const images = [
    'photo-restoration-case-1-before.png',
    'photo-restoration-case-2-before.png',
    'photo-restoration-case-3-before.png',
    'photo-restoration-case-4-before.png',
  ]

  return (
    <SampleImageClient
      title={t('photorestore.sampleImageTitle')}
      images={images}
    />
  )
}
