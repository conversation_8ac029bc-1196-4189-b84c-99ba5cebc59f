'use client'

import React from 'react'
import ReactCompareImage from 'react-compare-image'

interface ImageCompareProps {
  leftImage: string
  rightImage: string
  sliderPositionPercentage?: number
}

const ImageCompare = ({
  leftImage,
  rightImage,
  sliderPositionPercentage = 0.3,
}: ImageCompareProps) => {
  return (
    <ReactCompareImage
      leftImage={leftImage}
      rightImage={rightImage}
      sliderLineWidth={2}
      sliderLineColor="#3B82F6"
      sliderPositionPercentage={sliderPositionPercentage}
      handle={
        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center shadow-md rotate-90">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="w-5 h-5 text-white"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M8 9l4-4 4 4m0 6l-4 4-4-4"
            />
          </svg>
        </div>
      }
      hover={false}
    />
  )
}

export default ImageCompare
