'use client'
import React, { useState } from 'react'
import { ChevronDown } from 'lucide-react'
import { useTranslations } from 'next-intl'

const FAQSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('colorize')
  const [openIndices, setOpenIndices] = useState<number[]>([0])

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
    {
      question: t('faq5Question'),
      answer: t('faq5Answer'),
    },
  ]

  const toggleFAQ = (index: number) => {
    setOpenIndices((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    )
  }

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('faqTitle')}
        </h2>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => {
            const isOpen = openIndices.includes(index)
            return (
              <div
                key={index}
                className={`bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 transition-all duration-300 ${
                  isOpen ? 'border-purple-500' : 'hover:border-slate-700'
                }`}
                style={{
                  animation: `fade-in-up 0.5s ${index * 0.1}s ease-out both`,
                }}
              >
                <button
                  className="w-full text-left p-6 flex justify-between items-center cursor-pointer"
                  onClick={() => toggleFAQ(index)}
                >
                  <span className="font-semibold text-white text-lg">
                    {faq.question}
                  </span>
                  <ChevronDown
                    className={`w-5 h-5 text-gray-400 transition-transform duration-300 ${
                      isOpen ? 'transform rotate-180 text-purple-400' : ''
                    }`}
                  />
                </button>

                <div
                  className={`grid transition-all duration-500 ease-in-out ${
                    isOpen
                      ? 'grid-rows-[1fr] opacity-100'
                      : 'grid-rows-[0fr] opacity-0'
                  }`}
                >
                  <div className="overflow-hidden">
                    <div className="px-6 pb-6 pt-2 text-gray-400 transition-colors duration-300">
                      <div
                        className={`transition-opacity duration-500 delay-200 space-y-4 ${
                          isOpen ? 'opacity-100' : 'opacity-0'
                        }`}
                      >
                        {faq.answer.split('\n').map((paragraph, i) =>
                          paragraph.startsWith('-') ? (
                            <ul key={i} className="pl-5 space-y-2">
                              <li className="list-disc list-outside">
                                {paragraph.substring(1).trim()}
                              </li>
                            </ul>
                          ) : (
                            <p key={i} className="text-gray-300">
                              {paragraph}
                            </p>
                          )
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default FAQSection
