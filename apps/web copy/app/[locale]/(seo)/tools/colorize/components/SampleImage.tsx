'use client'
import { Tilt } from '@jdion/tilt-react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'

export default function SampleImage() {
  const t = useTranslations('colorize')

  return (
    <div className="mt-8">
      <p className="text-gray-400 mb-4">{t('sampleImagePrompt')}</p>
      <div className="grid grid-cols-4 gap-4 [perspective:1200px]">
        {[
          '53d8435a025b4d4384d9ec972155993d.jpg',
          '13e716b45f144c9d82e7b8ca548c3c19.jpg',
          '6db7172c1bbc4842b8ab1ad3449cfb09.jpg',
          'd9ee043477cf4ea9ad184d45764d9bb3.jpg',
        ].map((item) => (
          <Tilt key={item}>
            <div
              key={item}
              className="relative aspect-square rounded-lg overflow-hidden cursor-pointer border-2 border-transparent transition-all duration-300 hover:border-purple-500 hover:[transform:rotateX(10deg)_rotateY(-10deg)_scale(1.1)] hover:shadow-2xl hover:shadow-purple-500/30"
            >
              <Image
                src={`/samples/${item}`}
                alt={t('sampleImageAlt')}
                fill
                className="object-cover bg-slate-800"
              />
            </div>
          </Tilt>
        ))}
      </div>
    </div>
  )
}