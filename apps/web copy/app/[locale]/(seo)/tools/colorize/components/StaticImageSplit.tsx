'use client'

import React from 'react'
import Image from 'next/image'

interface StaticImageSplitProps {
  leftImage: string
  rightImage: string
  leftImageLabel?: string
  rightImageLabel?: string
}

const StaticImageSplit: React.FC<StaticImageSplitProps> = ({
  leftImage,
  rightImage,
  leftImageLabel = 'Before',
  rightImageLabel = 'After',
}) => {
  return (
    <div className="relative w-full aspect-video overflow-hidden shadow-2xl border border-gray-800">
      {/* Right image as the base layer */}
      <Image
        src={rightImage}
        alt="Colorized result"
        fill
        className="object-cover"
      />
      {/* Left image in a clipped container */}
      <div className="absolute top-0 left-0 w-1/2 h-full overflow-hidden">
        <div className="relative w-[200%] h-full">
          <Image
            src={leftImage}
            alt="Original black and white"
            fill
            className="object-cover"
          />
        </div>
      </div>
      {/* Divider line */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 h-full w-0.5 bg-purple-200" />

      {/* Labels */}
      <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm text-white px-4 py-1.5 rounded-full text-sm font-semibold tracking-wider">
        {leftImageLabel}
      </div>
      <div className="absolute top-4 right-4 bg-black/70 backdrop-blur-sm text-white px-4 py-1.5 rounded-full text-sm font-semibold tracking-wider">
        {rightImageLabel}
      </div>
    </div>
  )
}

export default StaticImageSplit
