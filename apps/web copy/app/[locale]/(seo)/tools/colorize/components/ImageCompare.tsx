// components/ImageCompare.tsx
'use client'
import React from 'react'
import ReactCompareImage from 'react-compare-image'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { useTranslations } from 'next-intl'

interface ImageCompareProps {
  leftImage: string
  rightImage: string
  leftImageLabel?: string
  rightImageLabel?: string
}

const ImageCompare: React.FC<ImageCompareProps> = ({
  leftImage,
  rightImage,
  leftImageLabel,
  rightImageLabel,
}) => {
  const t = useTranslations('colorize')

  return (
    <div className="relative rounded-2xl overflow-hidden group">
      <ReactCompareImage
        leftImage={leftImage}
        rightImage={rightImage}
        sliderLineWidth={3}
        sliderLineColor="#a855f7" // purple-500
        handle={
          <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center border-2 border-white shadow-lg transition-all duration-300 group-hover:scale-110 group-hover:shadow-purple-400/50">
            <div className="flex items-center text-white">
              <ChevronLeft size={20} />
              <ChevronRight size={20} />
            </div>
          </div>
        }
      />
      <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm text-white px-4 py-1.5 rounded-full text-sm font-semibold tracking-wider">
        {leftImageLabel || t('compareBefore')}
      </div>
      <div className="absolute top-4 right-4 bg-black/70 backdrop-blur-sm text-white px-4 py-1.5 rounded-full text-sm font-semibold tracking-wider">
        {rightImageLabel || t('compareAfter')}
      </div>
    </div>
  )
}

export default ImageCompare