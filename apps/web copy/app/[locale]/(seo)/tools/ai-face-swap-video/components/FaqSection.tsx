import React from 'react'
import Faq from './Faq'
import { useTranslations } from 'next-intl'

const FaqSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiFaceSwapVideo')

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
  ]

  return (
    <section className="py-16 lg:py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            {t('faqTitle')}
          </h2>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <Faq key={index} index={index} faq={faq} />
          ))}
        </div>
      </div>
    </section>
  )
}

export default FaqSection