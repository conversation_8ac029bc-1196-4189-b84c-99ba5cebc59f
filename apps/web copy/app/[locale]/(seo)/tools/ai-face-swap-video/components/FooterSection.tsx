import { Link } from '@i18n/routing'
import React from 'react'
import { useTranslations } from 'next-intl'

const FooterSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiFaceSwapVideo')

  return (
    <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-purple-600 to-pink-600">
      <div className="max-w-4xl mx-auto text-center">
        <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6">
          {t('footerTitle')}
        </h2>
        <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
          {t('footerDescription')}
        </p>
        <Link href={toolUrl}>
          <button className="bg-white text-purple-600 px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
            {t('footerButton')}
          </button>
        </Link>
      </div>
    </section>
  )
}

export default FooterSection