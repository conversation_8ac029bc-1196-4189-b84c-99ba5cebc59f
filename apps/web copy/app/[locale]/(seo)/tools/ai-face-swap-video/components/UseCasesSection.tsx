import React from 'react'
import { <PERSON>, <PERSON>, Star, Users } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const UseCasesSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiFaceSwapVideo')
  const isLoaded = true

  return (
    <section className="py-16 lg:py-24 px-4 sm:px-6 lg:px-8 bg-black/20">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            {t('useCasesTitle')}
          </h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            {t('useCasesDescription')}
          </p>
        </div>

        <div className="space-y-16">
          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="relative group overflow-hidden">
              <div className="bg-gradient-to-br opacity-0 from-purple-500/20 to-pink-500/20 rounded-2xl p-8 aspect-video flex items-center justify-center border-2 border-dashed border-white/20 hover:border-white/40 transition-all duration-300 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 to-pink-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="text-center relative z-10">
                  <div className="bg-white/10 rounded-full p-4 mb-4 mx-auto w-fit">
                    <Play className="w-12 h-12 text-white group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <p className="text-white/80 font-medium">
                    {t('useCase1SubTitle')}
                  </p>
                  <p className="text-white/60 text-sm mt-2">
                    {t('useCase1SubDescription')}
                  </p>
                </div>
              </div>

              <video
                muted
                autoPlay
                preload="metadata"
                loop
                playsInline
                className="w-full h-full absolute inset-0 rounded-2xl object-cover opacity-90"
                src="/videos/face-swap.mp4"
              ></video>
            </div>
            <div
              className={`transition-all duration-700 ${
                isLoaded
                  ? 'translate-x-0 opacity-100'
                  : 'translate-x-10 opacity-0'
              }`}
            >
              <h3 className="text-2xl lg:text-3xl font-bold text-white mb-6">
                {t('useCase1Title')}
              </h3>
              <p className="text-white/80 text-lg leading-relaxed mb-6">
                {t('useCase1Description')}
              </p>
              <div className="flex items-center space-x-4 mb-6">
                <div className="bg-green-400/20 rounded-lg px-3 py-1">
                  <span className="text-green-400 font-semibold">
                    {t('useCase1Stat1')}
                  </span>
                </div>
                <div className="bg-yellow-400/20 rounded-lg px-3 py-1">
                  <span className="text-yellow-400 font-semibold">
                    {t('useCase1Stat2')}
                  </span>
                </div>
              </div>
              <Link href={toolUrl}>
                <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:scale-105 active:scale-95 transition-all duration-300 transform hover:from-purple-400 hover:to-pink-400">
                  {t('useCase1Button')}
                </button>
              </Link>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="lg:order-2">
              <div className="relative group">
                <div className="bg-gradient-to-br opacity-0 from-blue-500/20 to-cyan-500/20 rounded-2xl p-8 aspect-video flex items-center justify-center border-2 border-dashed border-white/20 hover:border-white/40 transition-all duration-300 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-cyan-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="text-center relative z-10">
                    <div className="bg-white/10 rounded-full p-4 mb-4 mx-auto w-fit">
                      <Users className="w-12 h-12 text-white group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <p className="text-white/80 font-medium">
                      {t('useCase2SubTitle')}
                    </p>
                    <p className="text-white/60 text-sm mt-2">
                      {t('useCase2SubDescription')}
                    </p>
                    <div className="flex justify-center space-x-4 mt-3">
                      <div className="text-xs bg-red-500/20 px-2 py-1 rounded">
                        {t('useCase2Stat3')}
                      </div>
                      <div className="text-xs bg-blue-500/20 px-2 py-1 rounded">
                        {t('useCase2Stat4')}
                      </div>
                    </div>
                  </div>
                </div>
                <video
                  muted
                  autoPlay
                  preload="metadata"
                  loop
                  playsInline
                  className="w-full h-full absolute inset-0 rounded-2xl object-cover opacity-90"
                  src="/videos/face-tk.mp4"
                ></video>
              </div>
            </div>
            <div
              className={`lg:order-1 transition-all duration-700 delay-200 ${
                isLoaded
                  ? 'translate-x-0 opacity-100'
                  : '-translate-x-10 opacity-0'
              }`}
            >
              <h3 className="text-2xl lg:text-3xl font-bold text-white mb-6">
                {t('useCase2Title')}
              </h3>
              <p className="text-white/80 text-lg leading-relaxed mb-6">
                {t('useCase2Description')}
              </p>
              <div className="flex items-center space-x-4 mb-6">
                <div className="bg-pink-400/20 rounded-lg px-3 py-1">
                  <span className="text-pink-400 font-semibold">
                    {t('useCase2Stat1')}
                  </span>
                </div>
                <div className="bg-purple-400/20 rounded-lg px-3 py-1">
                  <span className="text-purple-400 font-semibold">
                    {t('useCase2Stat2')}
                  </span>
                </div>
              </div>
              <Link href={toolUrl}>
                <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:scale-105 active:scale-95 transition-all duration-300 transform hover:from-purple-400 hover:to-pink-400">
                  {t('useCase2Button')}
                </button>
              </Link>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="relative group">
              <div className="bg-gradient-to-br opacity-0 from-orange-500/20 to-red-500/20 rounded-2xl p-8 aspect-video flex items-center justify-center border-2 border-dashed border-white/20 hover:border-white/40 transition-all duration-300 overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-600/10 to-red-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="text-center relative z-10">
                  <div className="bg-white/10 rounded-full p-4 mb-4 mx-auto w-fit">
                    <Star className="w-12 h-12 text-white group-hover:scale-110 transition-transform duration-300" />
                  </div>
                  <p className="text-white/80 font-medium">
                    {t('useCase3SubTitle')}
                  </p>
                  <p className="text-white/60 text-sm mt-2">
                    {t('useCase3SubDescription')}
                  </p>
                  <div className="mt-3 bg-orange-500/20 px-3 py-1 rounded text-xs">
                    <span className="text-orange-400 font-semibold">
                      {t('useCase3Stat3')}
                    </span>
                  </div>
                </div>
              </div>

              <video
                muted
                autoPlay
                preload="metadata"
                loop
                playsInline
                className="w-full h-full absolute inset-0 rounded-2xl object-cover opacity-90"
                src="/videos/face-creative.mp4"
              ></video>
            </div>
            <div
              className={`transition-all duration-700 delay-400 ${
                isLoaded
                  ? 'translate-x-0 opacity-100'
                  : 'translate-x-10 opacity-0'
              }`}
            >
              <h3 className="text-2xl lg:text-3xl font-bold text-white mb-6">
                {t('useCase3Title')}
              </h3>
              <p className="text-white/80 text-lg leading-relaxed mb-6">
                {t('useCase3Description')}
              </p>
              <div className="flex items-center space-x-4 mb-6">
                <div className="bg-orange-400/20 rounded-lg px-3 py-1">
                  <span className="text-orange-400 font-semibold">
                    {t('useCase3Stat1')}
                  </span>
                </div>
                <div className="bg-green-400/20 rounded-lg px-3 py-1">
                  <span className="text-green-400 font-semibold">
                    {t('useCase3Stat2')}
                  </span>
                </div>
              </div>
              <Link href={toolUrl}>
                <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:scale-105 active:scale-95 transition-all duration-300 transform hover:from-purple-400 hover:to-pink-400">
                  {t('useCase3Button')}
                </button>
              </Link>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="lg:order-2">
              <div className="relative group">
                <div className="bg-gradient-to-br opacity-0 from-pink-500/20 to-rose-500/20 rounded-2xl p-8 aspect-video flex items-center justify-center border-2 border-dashed border-white/20 hover:border-white/40 transition-all duration-300 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-600/10 to-rose-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="text-center relative z-10">
                    <div className="bg-white/10 rounded-full p-4 mb-4 mx-auto w-fit">
                      <Heart className="w-12 h-12 text-white group-hover:scale-110 transition-transform duration-300" />
                    </div>
                    <p className="text-white/80 font-medium">
                      {t('useCase4SubTitle')}
                    </p>
                    <p className="text-white/60 text-sm mt-2">
                      {t('useCase4SubDescription')}
                    </p>
                    <div className="flex justify-center space-x-2 mt-3">
                      <div className="text-xs bg-pink-500/20 px-2 py-1 rounded">
                        {t('useCase4Stat3')}
                      </div>
                      <div className="text-xs bg-red-500/20 px-2 py-1 rounded">
                        {t('useCase4Stat4')}
                      </div>
                    </div>
                  </div>
                </div>
                <img
                  className="w-full h-full absolute inset-0 rounded-2xl object-cover"
                  src="https://d28dkohlqf5vwj.cloudfront.net/projects/ai-headshot-generator-landing-page.jpeg"
                  alt=""
                />
              </div>
            </div>
            <div
              className={`lg:order-1 transition-all duration-700 delay-600 ${
                isLoaded
                  ? 'translate-x-0 opacity-100'
                  : '-translate-x-10 opacity-0'
              }`}
            >
              <h3 className="text-2xl lg:text-3xl font-bold text-white mb-6">
                {t('useCase4Title')}
              </h3>
              <p className="text-white/80 text-lg leading-relaxed mb-6">
                {t('useCase4Description')}
              </p>
              <div className="flex items-center space-x-4 mb-6">
                <div className="bg-pink-400/20 rounded-lg px-3 py-1">
                  <span className="text-pink-400 font-semibold">
                    {t('useCase4Stat1')}
                  </span>
                </div>
                <div className="bg-rose-400/20 rounded-lg px-3 py-1">
                  <span className="text-rose-400 font-semibold">
                    {t('useCase4Stat2')}
                  </span>
                </div>
              </div>
              <Link href={toolUrl}>
                <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg hover:scale-105 active:scale-95 transition-all duration-300 transform hover:from-purple-400 hover:to-pink-400">
                  {t('useCase4Button')}
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default UseCasesSection