'use client'
import { ChevronDown } from 'lucide-react'
import React, { useState } from 'react'

const Faq = (props: { index: number; faq: any }) => {
  const { index, faq } = props
  const [openFaq, setOpenFaq] = useState<number | null>(null)
  return (
    <div
      key={index}
      className="bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 overflow-hidden"
    >
      <button
        className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200"
        onClick={() => setOpenFaq(openFaq === index ? null : index)}
      >
        <h3 className="text-lg font-semibold text-white pr-4">
          {faq.question}
        </h3>
        <ChevronDown
          className={`w-5 h-5 text-white/60 transition-transform duration-200 ${
            openFaq === index ? 'rotate-180' : ''
          }`}
        />
      </button>
      {
        <div className={`px-6 pb-4 ${openFaq === index ? 'block' : 'hidden'}`}>
          <p className="text-white/80 leading-relaxed">{faq.answer}</p>
        </div>
      }
    </div>
  )
}

export default Faq
