'use client'
import { Play, Pause, Volume2, VolumeX } from 'lucide-react'
import React, { useState, useRef, useEffect } from 'react'
import { useTranslations } from 'next-intl'

const ShowVideo = () => {
  const t = useTranslations('aiFaceSwapVideo')
  const [isPlaying, setIsPlaying] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [isMuted, setIsMuted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const autoPlay = async () => {
      try {
        video.muted = true
        setIsMuted(true)

        await video.play()
        setIsPlaying(true)
        setShowControls(false)
        setIsLoading(false)
      } catch (error) {
        console.error('Auto play failed:', error)
        setIsLoading(false)
        setShowControls(true)
      }
    }

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
      autoPlay()
    }

    const handleCanPlay = () => {
      if (isLoading) {
        setIsLoading(false)
      }
    }

    if (video.readyState >= 2) {
      handleLoadedMetadata()
    } else {
      video.addEventListener('loadedmetadata', handleLoadedMetadata)
    }

    video.addEventListener('canplay', handleCanPlay)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('canplay', handleCanPlay)
    }
  }, [isLoading])

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const updateTime = () => setCurrentTime(video.currentTime)

    video.addEventListener('timeupdate', updateTime)

    return () => {
      video.removeEventListener('timeupdate', updateTime)
    }
  }, [])

  const handlePlayClick = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
        setIsPlaying(false)
      } else {
        try {
          await videoRef.current.play()
          setIsPlaying(true)
          setTimeout(() => setShowControls(false), 1000)
        } catch (error) {
          console.error('Video play error:', error)
        }
      }
    }
  }

  const handleVideoClick = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
        setIsPlaying(false)
        setShowControls(true)
      } else {
        try {
          await videoRef.current.play()
          setIsPlaying(true)
          setShowControls(false)
        } catch (error) {
          console.error('Video play error:', error)
        }
      }
    }
  }

  const handleVideoEnded = () => {
    setIsPlaying(false)
    setShowControls(true)
  }

  const handleMouseEnter = () => {
    if (isPlaying) setShowControls(true)
  }

  const handleMouseLeave = () => {
    if (isPlaying) setShowControls(false)
  }

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${String(seconds).padStart(2, '0')}`
  }

  return (
    <div
      className="absolute inset-0 flex items-center justify-center cursor-pointer"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="relative w-full h-full">
        <video
          ref={videoRef}
          className="w-full h-full object-cover opacity-80"
          src="https://supawork.ai/pages/ai-video-face-swap/videos/hero_1.mp4"
          onClick={handleVideoClick}
          onEnded={handleVideoEnded}
          muted={isMuted}
          loop={true}
          preload="metadata"
          playsInline
          autoPlay
        />

        {isLoading && (
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-4 mx-auto"></div>
              <p className="text-white/80 text-sm">{t('showVideoLoading')}</p>
            </div>
          </div>
        )}

        {showControls && !isLoading && (
          <div
            className="absolute inset-0 bg-black/30 flex items-center justify-center transition-all duration-300"
            onClick={handlePlayClick}
          >
            <div className="bg-white/20 backdrop-blur-sm rounded-full p-6 hover:bg-white/30 transition-all duration-300 hover:scale-110 cursor-pointer">
              {isPlaying ? (
                <Pause className="w-12 h-12 text-white" />
              ) : (
                <Play className="w-12 h-12 text-white ml-1" />
              )}
            </div>
          </div>
        )}

        <div className="absolute bottom-4 left-4 right-4 max-md:hidden">
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3">
            <div className="flex items-center justify-between text-white text-sm mb-2">
              <span>{t('showVideoDemo')}</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleMute}
                  className="hover:bg-white/20 rounded p-1 transition-colors duration-200"
                >
                  {isMuted ? (
                    <VolumeX className="w-4 h-4" />
                  ) : (
                    <Volume2 className="w-4 h-4" />
                  )}
                </button>
                <span>
                  {formatTime(currentTime)} / {formatTime(duration)}
                </span>
              </div>
            </div>
            <div className="w-full bg-white/20 rounded-full h-1">
              <div
                className="bg-gradient-to-r from-purple-500 to-pink-500 h-1 rounded-full transition-all duration-300"
                style={{
                  width:
                    duration > 0 ? `${(currentTime / duration) * 100}%` : '0%',
                }}
              ></div>
            </div>
          </div>
        </div>

        {isPlaying && !showControls && (
          <div className="absolute top-4 right-4">
            <div className="bg-green-500/20 backdrop-blur-sm rounded-full px-3 py-1">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-400 text-xs font-medium">{t('showVideoLive')}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ShowVideo