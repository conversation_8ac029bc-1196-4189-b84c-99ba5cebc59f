import { Clock } from 'lucide-react'
import { Link } from '@i18n/routing'
import React from 'react'
import { useTranslations } from 'next-intl'

const AdvantageSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiFaceSwapVideo')

  return (
    <section className="py-16 lg:py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            {t('advantageTitle')}
          </h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto whitespace-wrap">
            {t('advantageDescription1')}
          </p>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            {t('advantageDescription2')}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 lg:gap-12 mb-12">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 hover:border-white/30 hover:scale-105 transition-all duration-300 transform group">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-yellow-400/20 rounded-lg mr-4 group-hover:bg-yellow-400/30 transition-colors duration-300">
                <Clock className="w-8 h-8 text-yellow-400" />
              </div>
              <h3 className="text-2xl font-bold text-white">
                {t('advantage1Title')}
              </h3>
            </div>
            <p className="text-white/80 text-lg leading-relaxed">
              {t('advantage1Description')}
            </p>
            <div className="mt-6 flex items-center space-x-4">
              <div className="bg-yellow-400/20 rounded-lg px-3 py-1">
                <span className="text-yellow-400 font-semibold">
                  {t('advantage1Stat1')}
                </span>
              </div>
              <div className="bg-green-400/20 rounded-lg px-3 py-1">
                <span className="text-green-400 font-semibold">
                  {t('advantage1Stat2')}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 hover:bg-white/15 hover:border-white/30 hover:scale-105 transition-all duration-300 transform group">
            <div className="flex items-center mb-4">
              <div className="p-2 bg-purple-400/20 rounded-lg mr-4 group-hover:bg-purple-400/30 transition-colors duration-300"></div>
              <h3 className="text-2xl font-bold text-white">
                {t('advantage2Title')}
              </h3>
            </div>
            <p className="text-white/80 text-lg leading-relaxed">
              {t('advantage2Description')}
            </p>
            <div className="mt-6 flex items-center space-x-4">
              <div className="bg-purple-400/20 rounded-lg px-3 py-1">
                <span className="text-purple-400 font-semibold">
                  {t('advantage2Stat1')}
                </span>
              </div>
              <div className="bg-blue-400/20 rounded-lg px-3 py-1">
                <span className="text-blue-400 font-semibold">
                  {t('advantage2Stat2')}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center">
          <Link href={toolUrl}>
            <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-xl hover:scale-105 active:scale-95 hover:opacity-90 transition-all duration-300 transform">
              {t('advantageButton')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default AdvantageSection
