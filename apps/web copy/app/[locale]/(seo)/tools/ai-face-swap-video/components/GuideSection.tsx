import React from 'react'
import { Upload, Zap } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const GuideSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiFaceSwapVideo')

  return (
    <section className="py-16 lg:py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            {t('guideTitle')}
          </h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            {t('guideDescription')}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 relative">
            <div className="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">
              1
            </div>
            <div className="flex items-center mb-4">
              <Upload className="w-8 h-8 text-purple-400 mr-3" />
              <h3 className="text-xl font-bold text-white">
                {t('guideStep1Title')}
              </h3>
            </div>
            <p className="text-white/80 leading-relaxed">
              {t('guideStep1Description')}
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 relative">
            <div className="absolute -top-4 -left-4 w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">
              2
            </div>
            <div className="flex items-center mb-4">
              <Zap className="w-8 h-8 text-yellow-400 mr-3" />
              <h3 className="text-xl font-bold text-white">
                {t('guideStep2Title')}
              </h3>
            </div>
            <p className="text-white/80 leading-relaxed">
              {t('guideStep2Description')}
            </p>
          </div>
        </div>

        <div className="text-center">
          <Link href={toolUrl}>
            <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-xl hover:scale-105 transition-all duration-300">
              {t('guideButton')}
            </button>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default GuideSection