import React from 'react'
import { useTranslations } from 'next-intl'

const BeforeAfter = () => {
  const t = useTranslations('aiFaceSwapVideo')

  return (
    <div className="absolute max-md:hidden -bottom-4 -left-4 bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20">
      <div className="flex items-center space-x-3">
        <div className="text-center">
          <div className="text-xs text-white/60">{t('before')}</div>
          <div className="w-12 h-12 bg-gradient-to-br from-gray-400 to-gray-600 rounded-lg"></div>
        </div>
        <div className="text-white/60">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 7l5 5m0 0l-5 5m5-5H6"
            />
          </svg>
        </div>
        <div className="text-center">
          <div className="text-xs text-white/60">{t('after')}</div>
          <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-lg"></div>
        </div>
      </div>
    </div>
  )
}

export default BeforeAfter