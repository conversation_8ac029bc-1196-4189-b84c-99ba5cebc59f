import { Star } from 'lucide-react'
import React from 'react'
import { useTranslations } from 'next-intl'

const TestimonialsSection = ({ toolUrl }: { toolUrl: string }) => {
  const t = useTranslations('aiFaceSwapVideo')

  const testimonials = [
    {
      text: t('testimonial1Text'),
      author: t('testimonial1Author'),
    },
    {
      text: t('testimonial2Text'),
      author: t('testimonial2Author'),
    },
    {
      text: t('testimonial3Text'),
      author: t('testimonial3Author'),
    },
    {
      text: t('testimonial4Text'),
      author: t('testimonial4Author'),
    },
  ]

  return (
    <section className="py-16 lg:py-24 px-4 sm:px-6 lg:px-8 bg-black/20">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
            {t('testimonialsTitle')}
          </h2>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20"
            >
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-yellow-400 fill-current"
                  />
                ))}
              </div>
              <p className="text-white/90 text-lg leading-relaxed mb-4">
                "{testimonial.text}"
              </p>
              <p className="text-purple-300 font-semibold">
                {testimonial.author}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialsSection