import { <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import { Metadata } from 'next'
import AdvantageSection from './components/AdvantageSection'
import UseCasesSection from './components/UseCasesSection'
import GuideSection from './components/GuideSection'
import TestimonialsSection from './components/TestimonialsSection'
import FooterSection from './components/FooterSection'
import FaqSection from './components/FaqSection'
import ShowVideo from './components/ShowVideo'
import BeforeAfter from './components/BeforeAfter'
import { Link } from '@i18n/routing'

export async function generateMetadata() {
  const t = await getTranslations('aiFaceSwapVideo')
  const url = 'https://imggen.ai/free-ai-face-swap-video'
  const ogImage = 'https://imggen.ai/assets/og-image-face-swap.jpg'
  const twitterImage = 'https://imggen.ai/assets/twitter-card-face-swap.jpg'

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords').split(', '),
    authors: [{ name: 'ImgGen Team' }],
    creator: 'ImgGen',
    publisher: 'ImgGen',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://imggen.ai'),
    alternates: {
      canonical: url,
    },
    openGraph: {
      type: 'website',
      url,
      title: t('title'),
      description: t('description'),
      siteName: 'ImgGen',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: t('ogImageAlt'),
        },
      ],
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
      images: [twitterImage],
      creator: '@imggen_ai',
      site: '@imggen_ai',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code',
      yahoo: 'your-yahoo-verification-code',
    },
    other: {
      'application-name': 'ImgGen',
      'apple-mobile-web-app-title': 'ImgGen',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'mobile-web-app-capable': 'yes',
      'msapplication-TileColor': '#8B5CF6',
      'msapplication-config': '/browserconfig.xml',
      'theme-color': '#8B5CF6',
    },
  }
}

const generateStructuredData = async () => {
  const t = await getTranslations('aiFaceSwapVideo')
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('structuredDataName'),
    description: t('structuredDataDescription'),
    applicationCategory: 'MultimediaApplication',
    operatingSystem: 'Web',
    url: 'https://imggen.ai/free-ai-face-swap-video',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '1250',
      bestRating: '5',
      worstRating: '1',
    },
    author: {
      '@type': 'Organization',
      name: 'ImgGen',
      url: 'https://imggen.ai',
    },
    publisher: {
      '@type': 'Organization',
      name: 'ImgGen',
      logo: {
        '@type': 'ImageObject',
        url: 'https://imggen.ai/logo.png',
      },
    },
    mainEntity: {
      '@type': 'WebPage',
      url: 'https://imggen.ai/free-ai-face-swap-video',
      name: t('structuredDataMainEntityName'),
      description: t('structuredDataMainEntityDescription'),
    },
    featureList: [
      t('feature1'),
      t('feature2'),
      t('feature3'),
      t('feature4'),
      t('feature5'),
      t('feature6'),
      t('feature7'),
    ],
    screenshot: [
      {
        '@type': 'ImageObject',
        url: 'https://imggen.ai/assets/screenshot-1.jpg',
        caption: t('screenshot1Caption'),
      },
      {
        '@type': 'ImageObject',
        url: 'https://imggen.ai/assets/screenshot-2.jpg',
        caption: t('screenshot2Caption'),
      },
    ],
    softwareVersion: '2.0',
    releaseNotes: t('releaseNotes'),
    downloadUrl: 'https://imggen.ai/free-ai-face-swap-video',
    installUrl: 'https://imggen.ai/free-ai-face-swap-video',
    softwareRequirements: t('softwareRequirements'),
    permissions: t('permissions'),
    fileSize: t('fileSize'),
    requirements: t('requirements'),
    category: t('category'),
    keywords: t('keywordsSchema'),
  }
}

const Index = async () => {
  const t = await getTranslations('aiFaceSwapVideo')
  const structuredData = await generateStructuredData()
  const AI_FACE_SWAP_VIDEO_TOOL_URL = '/ai/face-swap-video'

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      <div className="min-h-screen bg-gradient-to-br from-black via-blue-900 to-indigo-900 ">
        <header className="relative overflow-hidden h-screen max-md:min-h-screen max-md:h-auto">
          <div className="relative z-10 px-4 sm:px-6 lg:px-8 py-8 lg:py-12">
            <div className="max-w-7xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-[calc(100vh-120px)]">
                <div className="text-center lg:text-left">
                  <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-snug">
                    {t('heroTitle1')}{' '}
                    <span className="text-pink-400 leading-snug">
                      {t('heroTitle2')}
                    </span>{' '}
                    {t('heroTitle3')}
                  </h1>
                  <p className="text-xl text-white/80 mb-8 leading-relaxed max-w-2xl mx-auto lg:mx-0">
                    {t('heroDescription')}
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <Link href={AI_FACE_SWAP_VIDEO_TOOL_URL}>
                      <button className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-xl hover:scale-105 active:scale-95 transition-all duration-300 transform hover:from-purple-400 hover:to-pink-400">
                        {t('heroButton1')}
                      </button>
                    </Link>
                    <button className="bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/20 transition-all duration-300 border border-white/20">
                      {t('heroButton2')}
                    </button>
                  </div>

                  <div className="flex justify-center lg:justify-start space-x-8 mt-8">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {t('heroStat1')}
                      </div>
                      <div className="text-white/60 text-sm">
                        {t('heroStat1Label')}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {t('heroStat2')}
                      </div>
                      <div className="text-white/60 text-sm">
                        {t('heroStat2Label')}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {t('heroStat3')}
                      </div>
                      <div className="text-white/60 text-sm">
                        {t('heroStat3Label')}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="relative group">
                  <div className="relative bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-2xl p-4 border border-white/20 overflow-hidden">
                    <div className="relative aspect-video rounded-xl overflow-hidden bg-black/40">
                      <ShowVideo />
                    </div>

                    <div className="mt-4 text-center">
                      <h3 className="text-white font-semibold text-lg mb-2">
                        {t('videoTitle')}
                      </h3>
                      <p className="text-white/70 text-sm ml-4">
                        {t('videoDescription')}
                      </p>
                    </div>

                    <div className="absolute max-md:hidden -top-2 -right-2 w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                    <div className="absolute max-md:hidden -bottom-2 -left-2 w-6 h-6 bg-purple-400 rounded-full animate-pulse delay-1000"></div>
                  </div>

                  <BeforeAfter />
                </div>
              </div>
            </div>
          </div>

          <div className="absolute top-20 left-10 w-20 h-20 bg-purple-500/20 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-32 h-32 bg-pink-500/20 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-blue-500/20 rounded-full blur-xl animate-pulse delay-500"></div>
        </header>

        <AdvantageSection toolUrl={AI_FACE_SWAP_VIDEO_TOOL_URL} />

        <UseCasesSection toolUrl={AI_FACE_SWAP_VIDEO_TOOL_URL} />

        <GuideSection toolUrl={AI_FACE_SWAP_VIDEO_TOOL_URL} />

        <TestimonialsSection toolUrl={AI_FACE_SWAP_VIDEO_TOOL_URL} />

        <FaqSection toolUrl={AI_FACE_SWAP_VIDEO_TOOL_URL} />

        <FooterSection toolUrl={AI_FACE_SWAP_VIDEO_TOOL_URL} />
      </div>
    </>
  )
}

export default Index
