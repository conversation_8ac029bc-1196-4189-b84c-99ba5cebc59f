// Function to get prompt database with translations
const getPromptDatabase = (t: (key: any) => string) => ({
  tattoo: [
    {
      title: t('tattooGeometricFoxTattooTitle'),
      prompt: t('tattooGeometricFoxTattooPrompt'),
      technique: t('tattooGeometricFoxTattooTechnique'),
      url: 'https://oss.x2one.us/text2image/tattoo/Geometric%20Fox%20Tattoo.jpeg',
    },
    {
      title: t('tattooJapaneseDragonTitle'),
      prompt: t('tattooJapaneseDragonPrompt'),
      technique: t('tattooJapaneseDragonTechnique'),
      url: 'https://oss.x2one.us/text2image/tattoo/Japanese%20Dragon.jpeg',
    },
    {
      title: t('tattooBotanicalSleeveTitle'),
      prompt: t('tattooBotanicalSleevePrompt'),
      technique: t('tattooBotanicalSleeveTechnique'),
      url: 'https://oss.x2one.us/text2image/tattoo/Botanical%20Sleeve.jpeg',
    },
    {
      title: t('tattooCelticArmbandTitle'),
      prompt: t('tattooCelticArmbandPrompt'),
      technique: t('tattooCelticArmbandTechnique'),
      url: 'https://oss.x2one.us/text2image/tattoo/Celtic%20Armband.jpeg',
    },
    {
      title: t('tattooWatercolorWolfTitle'),
      prompt: t('tattooWatercolorWolfPrompt'),
      technique: t('tattooWatercolorWolfTechnique'),
      url: 'https://oss.x2one.us/text2image/tattoo/Watercolor%20Wolf.jpeg',
    },
    {
      title: t('tattooMountainLineworkTitle'),
      prompt: t('tattooMountainLineworkPrompt'),
      technique: t('tattooMountainLineworkTechnique'),
      url: 'https://oss.x2one.us/text2image/tattoo/Mountain%20Linework.jpeg',
    },
    // todo
    {
      title: t('tattooBotanicalSleeveTitle'),
      prompt: t('tattooBotanicalSleevePrompt'),
      technique: t('tattooBotanicalSleeveTechnique'),
      url: 'https://oss.x2one.us/text2image/tattoo/Botanical%20Sleeve.jpeg',
    },
    {
      title: t('tattooWatercolorWolfTitle'),
      prompt: t('tattooWatercolorWolfPrompt'),
      technique: t('tattooWatercolorWolfTechnique'),
      url: 'https://oss.x2one.us/text2image/tattoo/Watercolor%20Wolf.jpeg',
    },
  ],

  graffiti: [
    {
      title: t('graffitiWildstyleMasterpieceTitle'),
      prompt: t('graffitiWildstyleMasterpiecePrompt'),
      technique: t('graffitiWildstyleMasterpieceTechnique'),
      url: 'https://oss.x2one.us/text2image/graffiti/Wildstyle%20Masterpiece.jpeg',
    },
    {
      title: t('graffitiChromePiecesTitle'),
      prompt: t('graffitiChromePiecesPrompt'),
      technique: t('graffitiChromePiecesTechnique'),
      url: 'https://oss.x2one.us/text2image/graffiti/Chrome%20Pieces.jpeg',
    },
    {
      title: t('graffitiNeonThrowUpTitle'),
      prompt: t('graffitiNeonThrowUpPrompt'),
      technique: t('graffitiNeonThrowUpTechnique'),
      url: 'https://oss.x2one.us/text2image/graffiti/Neon%20Throw-up.jpeg',
    },
    {
      title: t('graffitiCharacterBombTitle'),
      prompt: t('graffitiCharacterBombPrompt'),
      technique: t('graffitiCharacterBombTechnique'),
      url: 'https://oss.x2one.us/text2image/graffiti/Character%20Bomb.jpeg',
    },
    {
      title: t('graffitiMultiLayerStencilTitle'),
      prompt: t('graffitiMultiLayerStencilPrompt'),
      technique: t('graffitiMultiLayerStencilTechnique'),
      url: 'https://oss.x2one.us/text2image/graffiti/Multi-Layer%20Stencil.jpeg',
    },
    {
      title: t('graffiti3DGraffitiTitle'),
      prompt: t('graffiti3DGraffitiPrompt'),
      technique: t('graffiti3DGraffitiTechnique'),
      url: 'https://oss.x2one.us/text2image/graffiti/3D%20Graffiti.jpeg',
    },
    // todo
    {
      title: t('graffitiNeonThrowUpTitle'),
      prompt: t('graffitiNeonThrowUpPrompt'),
      technique: t('graffitiNeonThrowUpTechnique'),
      url: 'https://oss.x2one.us/text2image/graffiti/Neon%20Throw-up.jpeg',
    },
    {
      title: t('graffitiCharacterBombTitle'),
      prompt: t('graffitiCharacterBombPrompt'),
      technique: t('graffitiCharacterBombTechnique'),
      url: 'https://oss.x2one.us/text2image/graffiti/Character%20Bomb.jpeg',
    },
  ],

  fantasy_art: [
    {
      title: t('fantasyArtDragonsRealmTitle'),
      prompt: t('fantasyArtDragonsRealmPrompt'),
      technique: t('fantasyArtDragonsRealmTechnique'),
      url: 'https://oss.x2one.us/text2image/fantasy_art/1.jpeg',
    },
    {
      title: t('fantasyArtEnchantedForestTitle'),
      prompt: t('fantasyArtEnchantedForestPrompt'),
      technique: t('fantasyArtEnchantedForestTechnique'),
      url: 'https://oss.x2one.us/text2image/fantasy_art/2.jpeg',
    },
    {
      title: t('fantasyArtCrystalCitadelTitle'),
      prompt: t('fantasyArtCrystalCitadelPrompt'),
      technique: t('fantasyArtCrystalCitadelTechnique'),
      url: 'https://oss.x2one.us/text2image/fantasy_art/3.jpeg',
    },
    {
      title: t('fantasyArtWizardsStudyTitle'),
      prompt: t('fantasyArtWizardsStudyPrompt'),
      technique: t('fantasyArtWizardsStudyTechnique'),
      url: 'https://oss.x2one.us/text2image/fantasy_art/4.jpeg',
    },
    {
      title: t('fantasyArtPortalGatewayTitle'),
      prompt: t('fantasyArtPortalGatewayPrompt'),
      technique: t('fantasyArtPortalGatewayTechnique'),
      url: 'https://oss.x2one.us/text2image/fantasy_art/5.jpeg',
    },
    {
      title: t('fantasyArtBattleSceneTitle'),
      prompt: t('fantasyArtBattleScenePrompt'),
      technique: t('fantasyArtBattleSceneTechnique'),
      url: 'https://oss.x2one.us/text2image/fantasy_art/6.jpeg',
    },
    // todo
    {
      title: t('fantasyArtCrystalCitadelTitle'),
      prompt: t('fantasyArtCrystalCitadelPrompt'),
      technique: t('fantasyArtCrystalCitadelTechnique'),
      url: 'https://oss.x2one.us/text2image/fantasy_art/7.jpeg',
    },
    {
      title: t('fantasyArtWizardsStudyTitle'),
      prompt: t('fantasyArtWizardsStudyPrompt'),
      technique: t('fantasyArtWizardsStudyTechnique'),
      url: 'https://oss.x2one.us/text2image/fantasy_art/8.jpeg',
    },
  ],

  pixel_art: [
    {
      title: t('pixelArtRetroGamingSceneTitle'),
      prompt: t('pixelArtRetroGamingScenePrompt'),
      technique: t('pixelArtRetroGamingSceneTechnique'),
      url: `https://oss.x2one.us/text2image/pixel_art/Dragon's%20Realm.jpeg`,
    },
    {
      title: t('pixelArtPixelLandscapeTitle'),
      prompt: t('pixelArtPixelLandscapePrompt'),
      technique: t('pixelArtPixelLandscapeTechnique'),
      url: 'https://oss.x2one.us/text2image/pixel_art/Enchanted%20Forest.jpeg',
    },
    {
      title: t('pixelArtCharacterSpriteTitle'),
      prompt: t('pixelArtCharacterSpritePrompt'),
      technique: t('pixelArtCharacterSpriteTechnique'),
      url: 'https://oss.x2one.us/text2image/pixel_art/Crystal%20Citadel.jpeg',
    },
    {
      title: t('pixelArtPixelCityTitle'),
      prompt: t('pixelArtPixelCityPrompt'),
      technique: t('pixelArtPixelCityTechnique'),
      url: `https://oss.x2one.us/text2image/pixel_art/Wizard's%20Study.jpeg`,
    },
    {
      title: t('pixelArtBossBattleTitle'),
      prompt: t('pixelArtBossBattlePrompt'),
      technique: t('pixelArtBossBattleTechnique'),
      url: 'https://oss.x2one.us/text2image/pixel_art/Portal%20Gateway.jpeg',
    },
    {
      title: t('pixelArtPixelNatureTitle'),
      prompt: t('pixelArtPixelNaturePrompt'),
      technique: t('pixelArtPixelNatureTechnique'),
      url: 'https://oss.x2one.us/text2image/pixel_art/Battle%20Scene.jpeg',
    },
    // todo
    {
      title: t('pixelArtPixelLandscapeTitle'),
      prompt: t('pixelArtPixelLandscapePrompt'),
      technique: t('pixelArtPixelLandscapeTechnique'),
      url: 'https://oss.x2one.us/text2image/pixel_art/Portal%20Gateway.jpeg',
    },
    {
      title: t('pixelArtCharacterSpriteTitle'),
      prompt: t('pixelArtCharacterSpritePrompt'),
      technique: t('pixelArtCharacterSpriteTechnique'),
      url: 'https://oss.x2one.us/text2image/pixel_art/Crystal%20Citadel.jpeg',
    },
  ],

  abstract_art: [
    {
      title: t('abstractArtGeometricAbstractionTitle'),
      prompt: t('abstractArtGeometricAbstractionPrompt'),
      technique: t('abstractArtGeometricAbstractionTechnique'),
      url: 'https://oss.x2one.us/text2image/abstract_art/Geometric%20Abstraction.jpeg',
    },
    {
      title: t('abstractArtFluidDynamicsTitle'),
      prompt: t('abstractArtFluidDynamicsPrompt'),
      technique: t('abstractArtFluidDynamicsTechnique'),
      url: 'https://oss.x2one.us/text2image/abstract_art/Fluid%20Dynamics.jpeg',
    },
    {
      title: t('abstractArtDigitalGlitchTitle'),
      prompt: t('abstractArtDigitalGlitchPrompt'),
      technique: t('abstractArtDigitalGlitchTechnique'),
      url: 'https://oss.x2one.us/text2image/abstract_art/Digital%20Glitch.jpeg',
    },
    {
      title: t('abstractArtFractalCompositionTitle'),
      prompt: t('abstractArtFractalCompositionPrompt'),
      technique: t('abstractArtFractalCompositionTechnique'),
      url: 'https://oss.x2one.us/text2image/abstract_art/Fractal%20Composition.jpeg',
    },
    {
      title: t('abstractArtColorFieldTitle'),
      prompt: t('abstractArtColorFieldPrompt'),
      technique: t('abstractArtColorFieldTechnique'),
      url: 'https://oss.x2one.us/text2image/abstract_art/Color%20Field.jpeg',
    },
    {
      title: t('abstractArtMinimalistAbstractTitle'),
      prompt: t('abstractArtMinimalistAbstractPrompt'),
      technique: t('abstractArtMinimalistAbstractTechnique'),
      url: 'https://oss.x2one.us/text2image/abstract_art/Minimalist%20Abstract.jpeg',
    },
    // todo
    {
      title: t('abstractArtFractalCompositionTitle'),
      prompt: t('abstractArtFractalCompositionPrompt'),
      technique: t('abstractArtFractalCompositionTechnique'),
      url: 'https://oss.x2one.us/text2image/abstract_art/Fractal%20Composition.jpeg',
    },
    {
      title: t('abstractArtColorFieldTitle'),
      prompt: t('abstractArtColorFieldPrompt'),
      technique: t('abstractArtColorFieldTechnique'),
      url: 'https://oss.x2one.us/text2image/abstract_art/Color%20Field.jpeg',
    },
  ],

  portrait_art: [
    {
      title: t('portraitArtRenaissanceStyleTitle'),
      prompt: t('portraitArtRenaissanceStylePrompt'),
      technique: t('portraitArtRenaissanceStyleTechnique'),
      url: 'https://oss.x2one.us/text2image/portrait_art/Renaissance%20Style.jpeg',
    },
    {
      title: t('portraitArtModernPortraitTitle'),
      prompt: t('portraitArtModernPortraitPrompt'),
      technique: t('portraitArtModernPortraitTechnique'),
      url: 'https://oss.x2one.us/text2image/portrait_art/Modern%20Portrait.jpeg',
    },
    {
      title: t('portraitArtPopArtPortraitTitle'),
      prompt: t('portraitArtPopArtPortraitPrompt'),
      technique: t('portraitArtPopArtPortraitTechnique'),
      url: 'https://oss.x2one.us/text2image/portrait_art/Pop%20Art%20Portrait.jpeg',
    },
    {
      title: t('portraitArtPencilDrawingTitle'),
      prompt: t('portraitArtPencilDrawingPrompt'),
      technique: t('portraitArtPencilDrawingTechnique'),
      url: 'https://oss.x2one.us/text2image/portrait_art/Pencil%20Drawing.jpeg',
    },
    {
      title: t('portraitArtImpressionistPortraitTitle'),
      prompt: t('portraitArtImpressionistPortraitPrompt'),
      technique: t('portraitArtImpressionistPortraitTechnique'),
      url: 'https://oss.x2one.us/text2image/portrait_art/Impressionist%20Portrait.jpeg',
    },
    {
      title: t('portraitArtDigitalPortraitTitle'),
      prompt: t('portraitArtDigitalPortraitPrompt'),
      technique: t('portraitArtDigitalPortraitTechnique'),
      url: 'https://oss.x2one.us/text2image/portrait_art/Digital%20Portrait.jpeg',
    },
    // todo
    {
      title: t('portraitArtPopArtPortraitTitle'),
      prompt: t('portraitArtPopArtPortraitPrompt'),
      technique: t('portraitArtPopArtPortraitTechnique'),
      url: 'https://oss.x2one.us/text2image/portrait_art/Pop%20Art%20Portrait.jpeg',
    },
    {
      title: t('portraitArtPencilDrawingTitle'),
      prompt: t('portraitArtPencilDrawingPrompt'),
      technique: t('portraitArtPencilDrawingTechnique'),
      url: 'https://oss.x2one.us/text2image/portrait_art/Pencil%20Drawing.jpeg',
    },
  ],

  concept_art: [
    {
      title: t('conceptArtSciFiVehicleTitle'),
      prompt: t('conceptArtSciFiVehiclePrompt'),
      technique: t('conceptArtSciFiVehicleTechnique'),
      url: 'https://oss.x2one.us/text2image/concept_art/Sci-fi%20Vehicle.jpeg',
    },
    {
      title: t('conceptArtCharacterDesignTitle'),
      prompt: t('conceptArtCharacterDesignPrompt'),
      technique: t('conceptArtCharacterDesignTechnique'),
      url: 'https://oss.x2one.us/text2image/concept_art/Character%20Design.jpeg',
    },
    {
      title: t('conceptArtEnvironmentDesignTitle'),
      prompt: t('conceptArtEnvironmentDesignPrompt'),
      technique: t('conceptArtEnvironmentDesignTechnique'),
      url: 'https://oss.x2one.us/text2image/concept_art/Environment%20Design.jpeg',
    },
    {
      title: t('conceptArtCreatureDesignTitle'),
      prompt: t('conceptArtCreatureDesignPrompt'),
      technique: t('conceptArtCreatureDesignTechnique'),
      url: 'https://oss.x2one.us/text2image/concept_art/Creature%20Design.jpeg',
    },
    {
      title: t('conceptArtPropDesignTitle'),
      prompt: t('conceptArtPropDesignPrompt'),
      technique: t('conceptArtPropDesignTechnique'),
      url: 'https://oss.x2one.us/text2image/concept_art/Prop%20Design.jpeg',
    },
    {
      title: t('conceptArtMechDesignTitle'),
      prompt: t('conceptArtMechDesignPrompt'),
      technique: t('conceptArtMechDesignTechnique'),
      url: 'https://oss.x2one.us/text2image/concept_art/Mech%20Design.jpeg',
    },
    // todo
    {
      title: t('conceptArtEnvironmentDesignTitle'),
      prompt: t('conceptArtEnvironmentDesignPrompt'),
      technique: t('conceptArtEnvironmentDesignTechnique'),
      url: 'https://oss.x2one.us/text2image/concept_art/Environment%20Design.jpeg',
    },
    {
      title: t('conceptArtCreatureDesignTitle'),
      prompt: t('conceptArtCreatureDesignPrompt'),
      technique: t('conceptArtCreatureDesignTechnique'),
      url: 'https://oss.x2one.us/text2image/concept_art/Creature%20Design.jpeg',
    },
  ],

  architectural: [
    {
      title: t('architecturalModernMinimalistTitle'),
      prompt: t('architecturalModernMinimalistPrompt'),
      technique: t('architecturalModernMinimalistTechnique'),
      url: 'https://oss.x2one.us/text2image/architectural/Modern%20Minimalist.jpeg',
    },
    {
      title: t('architecturalGothicCathedralTitle'),
      prompt: t('architecturalGothicCathedralPrompt'),
      technique: t('architecturalGothicCathedralTechnique'),
      url: 'https://oss.x2one.us/text2image/architectural/Gothic%20Cathedral.jpeg',
    },
    {
      title: t('architecturalUrbanHighriseTitle'),
      prompt: t('architecturalUrbanHighrisePrompt'),
      technique: t('architecturalUrbanHighriseTechnique'),
      url: 'https://oss.x2one.us/text2image/architectural/Urban%20Highrise.jpeg',
    },
    {
      title: t('architecturalJapaneseTempleTitle'),
      prompt: t('architecturalJapaneseTemplePrompt'),
      technique: t('architecturalJapaneseTempleTechnique'),
      url: 'https://oss.x2one.us/text2image/architectural/Japanese%20Temple.jpeg',
    },
    {
      title: t('architecturalFutureCityTitle'),
      prompt: t('architecturalFutureCityPrompt'),
      technique: t('architecturalFutureCityTechnique'),
      url: 'https://oss.x2one.us/text2image/architectural/Future%20City.jpeg',
    },
    {
      title: t('architecturalDesertVillaTitle'),
      prompt: t('architecturalDesertVillaPrompt'),
      technique: t('architecturalDesertVillaTechnique'),
      url: 'https://oss.x2one.us/text2image/architectural/Desert%20Villa.jpeg',
    },
    // todo
    {
      title: t('architecturalUrbanHighriseTitle'),
      prompt: t('architecturalUrbanHighrisePrompt'),
      technique: t('architecturalUrbanHighriseTechnique'),
      url: 'https://oss.x2one.us/text2image/architectural/Urban%20Highrise.jpeg',
    },
    {
      title: t('architecturalJapaneseTempleTitle'),
      prompt: t('architecturalJapaneseTemplePrompt'),
      technique: t('architecturalJapaneseTempleTechnique'),
      url: 'https://oss.x2one.us/text2image/architectural/Japanese%20Temple.jpeg',
    },
  ],

  product_design: [
    {
      title: t('productDesignLuxuryWatchTitle'),
      prompt: t('productDesignLuxuryWatchPrompt'),
      technique: t('productDesignLuxuryWatchTechnique'),
      url: 'https://oss.x2one.us/text2image/product_design/Luxury%20Watch.jpeg',
    },
    {
      title: t('productDesignModernFurnitureTitle'),
      prompt: t('productDesignModernFurniturePrompt'),
      technique: t('productDesignModernFurnitureTechnique'),
      url: 'https://oss.x2one.us/text2image/product_design/Modern%20Furniture.jpeg',
    },
    {
      title: t('productDesignTechDeviceTitle'),
      prompt: t('productDesignTechDevicePrompt'),
      technique: t('productDesignTechDeviceTechnique'),
      url: 'https://oss.x2one.us/text2image/product_design/Tech%20Device.jpeg',
    },
    {
      title: t('productDesignSportsEquipmentTitle'),
      prompt: t('productDesignSportsEquipmentPrompt'),
      technique: t('productDesignSportsEquipmentTechnique'),
      url: 'https://oss.x2one.us/text2image/product_design/Sports%20Equipment.jpeg',
    },
    {
      title: t('productDesignLuxuryBagTitle'),
      prompt: t('productDesignLuxuryBagPrompt'),
      technique: t('productDesignLuxuryBagTechnique'),
      url: 'https://oss.x2one.us/text2image/product_design/Luxury%20Bag.jpeg',
    },
    {
      title: t('productDesignKitchenApplianceTitle'),
      prompt: t('productDesignKitchenAppliancePrompt'),
      technique: t('productDesignKitchenApplianceTechnique'),
      url: 'https://oss.x2one.us/text2image/product_design/Kitchen%20Appliance.jpeg',
    },
    // todo
    {
      title: t('productDesignLuxuryWatchTitle'),
      prompt: t('productDesignLuxuryWatchPrompt'),
      technique: t('productDesignLuxuryWatchTechnique'),
      url: 'https://oss.x2one.us/text2image/product_design/Luxury%20Watch.jpeg',
    },
    {
      title: t('productDesignModernFurnitureTitle'),
      prompt: t('productDesignModernFurniturePrompt'),
      technique: t('productDesignModernFurnitureTechnique'),
      url: 'https://oss.x2one.us/text2image/product_design/Modern%20Furniture.jpeg',
    },
  ],

  fashion_design: [
    {
      title: t('fashionDesignHauteCoutureTitle'),
      prompt: t('fashionDesignHauteCouturePrompt'),
      technique: t('fashionDesignHauteCoutureTechnique'),
      url: 'https://oss.x2one.us/text2image/fashion_design/Haute%20Couture.jpeg',
    },
    {
      title: t('fashionDesignStreetStyleTitle'),
      prompt: t('fashionDesignStreetStylePrompt'),
      technique: t('fashionDesignStreetStyleTechnique'),
      url: 'https://oss.x2one.us/text2image/fashion_design/Street%20Style.jpeg',
    },
    {
      title: t('fashionDesignEveningWearTitle'),
      prompt: t('fashionDesignEveningWearPrompt'),
      technique: t('fashionDesignEveningWearTechnique'),
      url: 'https://oss.x2one.us/text2image/fashion_design/Evening%20Wear.jpeg',
    },
    {
      title: t('fashionDesignMinimalistFashionTitle'),
      prompt: t('fashionDesignMinimalistFashionPrompt'),
      technique: t('fashionDesignMinimalistFashionTechnique'),
      url: 'https://oss.x2one.us/text2image/fashion_design/Minimalist%20Fashion.jpeg',
    },
    {
      title: t('fashionDesignAthleticWearTitle'),
      prompt: t('fashionDesignAthleticWearPrompt'),
      technique: t('fashionDesignAthleticWearTechnique'),
      url: 'https://oss.x2one.us/text2image/fashion_design/Athletic%20Wear.jpeg',
    },
    {
      title: t('fashionDesignBusinessAttireTitle'),
      prompt: t('fashionDesignBusinessAttirePrompt'),
      technique: t('fashionDesignBusinessAttireTechnique'),
      url: 'https://oss.x2one.us/text2image/fashion_design/Business%20Attire.jpeg',
    },
    // todo
    {
      title: t('fashionDesignHauteCoutureTitle'),
      prompt: t('fashionDesignHauteCouturePrompt'),
      technique: t('fashionDesignHauteCoutureTechnique'),
      url: 'https://oss.x2one.us/text2image/fashion_design/Haute%20Couture.jpeg',
    },
    {
      title: t('fashionDesignStreetStyleTitle'),
      prompt: t('fashionDesignStreetStylePrompt'),
      technique: t('fashionDesignStreetStyleTechnique'),
      url: 'https://oss.x2one.us/text2image/fashion_design/Street%20Style.jpeg',
    },
  ],

  landscape: [
    {
      title: t('landscapeMountainVistaTitle'),
      prompt: t('landscapeMountainVistaPrompt'),
      technique: t('landscapeMountainVistaTechnique'),
      url: 'https://oss.x2one.us/text2image/landscape/Mountain%20Vista.jpeg',
    },
    {
      title: t('landscapeCoastalSceneTitle'),
      prompt: t('landscapeCoastalScenePrompt'),
      technique: t('landscapeCoastalSceneTechnique'),
      url: 'https://oss.x2one.us/text2image/landscape/Coastal%20Scene.jpeg',
    },
    {
      title: t('landscapeDesertLandscapeTitle'),
      prompt: t('landscapeDesertLandscapePrompt'),
      technique: t('landscapeDesertLandscapeTechnique'),
      url: 'https://oss.x2one.us/text2image/landscape/Desert%20Landscape.jpeg',
    },
    {
      title: t('landscapeForestInteriorTitle'),
      prompt: t('landscapeForestInteriorPrompt'),
      technique: t('landscapeForestInteriorTechnique'),
      url: 'https://oss.x2one.us/text2image/landscape/Forest%20Interior.jpeg',
    },
    {
      title: t('landscapeRuralSceneTitle'),
      prompt: t('landscapeRuralScenePrompt'),
      technique: t('landscapeRuralSceneTechnique'),
      url: 'https://oss.x2one.us/text2image/landscape/Rural%20Scene.jpeg',
    },
    {
      title: t('landscapeWinterLandscapeTitle'),
      prompt: t('landscapeWinterLandscapePrompt'),
      technique: t('landscapeWinterLandscapeTechnique'),
      url: 'https://oss.x2one.us/text2image/landscape/Winter%20Landscape.jpeg',
    },
    // todo
    {
      title: t('landscapeCoastalSceneTitle'),
      prompt: t('landscapeCoastalScenePrompt'),
      technique: t('landscapeCoastalSceneTechnique'),
      url: 'https://oss.x2one.us/text2image/landscape/Coastal%20Scene.jpeg',
    },
    {
      title: t('landscapeDesertLandscapeTitle'),
      prompt: t('landscapeDesertLandscapePrompt'),
      technique: t('landscapeDesertLandscapeTechnique'),
      url: 'https://oss.x2one.us/text2image/landscape/Desert%20Landscape.jpeg',
    },
  ],

  food_photography: [
    {
      title: t('foodPhotographyGourmetPlateTitle'),
      prompt: t('foodPhotographyGourmetPlatePrompt'),
      technique: t('foodPhotographyGourmetPlateTechnique'),
      url: 'https://oss.x2one.us/text2image/food_photography/Gourmet%20Plate.jpeg',
    },
    {
      title: t('foodPhotographyDessertCreationTitle'),
      prompt: t('foodPhotographyDessertCreationPrompt'),
      technique: t('foodPhotographyDessertCreationTechnique'),
      url: 'https://oss.x2one.us/text2image/food_photography/Dessert%20Creation.jpeg',
    },
    {
      title: t('foodPhotographyBeverageShotTitle'),
      prompt: t('foodPhotographyBeverageShotPrompt'),
      technique: t('foodPhotographyBeverageShotTechnique'),
      url: 'https://oss.x2one.us/text2image/food_photography/Beverage%20Shot.jpeg',
    },
    {
      title: t('foodPhotographyRawIngredientsTitle'),
      prompt: t('foodPhotographyRawIngredientsPrompt'),
      technique: t('foodPhotographyRawIngredientsTechnique'),
      url: 'https://oss.x2one.us/text2image/food_photography/Raw%20Ingredients.jpeg',
    },
    {
      title: t('foodPhotographyStreetFoodTitle'),
      prompt: t('foodPhotographyStreetFoodPrompt'),
      technique: t('foodPhotographyStreetFoodTechnique'),
      url: 'https://oss.x2one.us/text2image/food_photography/Street%20Food.jpeg',
    },
    {
      title: t('foodPhotographyTableSettingTitle'),
      prompt: t('foodPhotographyTableSettingPrompt'),
      technique: t('foodPhotographyTableSettingTechnique'),
      url: 'https://oss.x2one.us/text2image/food_photography/Table%20Setting.jpeg',
    },
    // todo
    {
      title: t('foodPhotographyDessertCreationTitle'),
      prompt: t('foodPhotographyDessertCreationPrompt'),
      technique: t('foodPhotographyDessertCreationTechnique'),
      url: 'https://oss.x2one.us/text2image/food_photography/Dessert%20Creation.jpeg',
    },
    {
      title: t('foodPhotographyBeverageShotTitle'),
      prompt: t('foodPhotographyBeverageShotPrompt'),
      technique: t('foodPhotographyBeverageShotTechnique'),
      url: 'https://oss.x2one.us/text2image/food_photography/Beverage%20Shot.jpeg',
    },
  ],
})

// ES6 导出（用于 React 组件）
export { getPromptDatabase }

// CommonJS 导出（用于 Node.js 测试）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { getPromptDatabase }
}
