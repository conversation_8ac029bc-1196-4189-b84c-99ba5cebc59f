'use client'

import { useState, useEffect, useRef } from 'react'
import { ChevronDown, HelpCircle, Sparkles } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Link } from '@i18n/routing'

interface FAQSectionProps {
  toolUrl?: string
}

const FAQSection = ({ toolUrl }: FAQSectionProps = {}) => {
  const t = useTranslations('aiTextToImage')
  const [isVisible, setIsVisible] = useState(false)
  const [openFAQ, setOpenFAQ] = useState<number | null>(0)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
  ]

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index)
  }

  return (
    <section
      ref={sectionRef}
      className="py-20 bg-gradient-to-b from-slate-900 to-slate-800 relative overflow-hidden"
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-40 left-10 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse" />
        <div
          className="absolute bottom-40 right-10 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: '2s' }}
        />
      </div>

      <div className="relative z-10 container mx-auto px-4">
        {/* 标题部分 */}
        <div
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="inline-flex items-center gap-2 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-4 py-2 text-purple-300 text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            {t('faqTagText')}
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('faqTitle')}{' '}
            <span className="text-pink-400 ">{t('faqTitleHighlight')}</span>
          </h2>
        </div>

        {/* FAQ列表 */}
        <div className="max-w-4xl mx-auto">
          {/* SEO友好的隐藏内容 */}
          <div className="sr-only">
            <h3>Frequently Asked Questions</h3>
            {faqs.map((faq, index) => (
              <div
                key={`seo-faq-${index}`}
                itemScope
                itemType="https://schema.org/Question"
              >
                <h4 itemProp="name">{faq.question}</h4>
                <div
                  itemScope
                  itemType="https://schema.org/Answer"
                  itemProp="acceptedAnswer"
                >
                  <div itemProp="text">{faq.answer}</div>
                </div>
              </div>
            ))}
          </div>

          {/* 交互式FAQ */}
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className={`transition-all duration-1000 ${
                  isVisible
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div
                  className={`group bg-slate-800/50 backdrop-blur-sm border rounded-2xl overflow-hidden transition-all duration-300 ${
                    openFAQ === index
                      ? 'border-purple-500/50 shadow-lg shadow-purple-500/20'
                      : 'border-slate-700/50 hover:border-purple-500/30'
                  }`}
                >
                  {/* 问题按钮 */}
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full px-8 py-6 text-left flex items-center justify-between group-hover:bg-slate-700/20 transition-colors duration-300"
                  >
                    <div className="flex items-center gap-4">
                      <div
                        className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${
                          openFAQ === index
                            ? 'bg-gradient-to-r from-purple-600 to-pink-600'
                            : 'bg-slate-700 group-hover:bg-purple-600/50'
                        }`}
                      >
                        <HelpCircle className="w-5 h-5 text-white" />
                      </div>
                      <h3
                        className={`text-lg font-semibold transition-colors duration-300 ${
                          openFAQ === index
                            ? 'text-purple-300'
                            : 'text-white group-hover:text-purple-300'
                        }`}
                      >
                        {faq.question}
                      </h3>
                    </div>

                    <ChevronDown
                      className={`w-6 h-6 text-gray-400 transition-all duration-300 ${
                        openFAQ === index
                          ? 'rotate-180 text-purple-400'
                          : 'group-hover:text-purple-400'
                      }`}
                    />
                  </button>

                  {/* 答案内容 */}
                  <div
                    className={`overflow-hidden transition-all duration-500 ease-in-out ${
                      openFAQ === index
                        ? 'max-h-96 opacity-100'
                        : 'max-h-0 opacity-0'
                    }`}
                  >
                    <div className="px-8 pb-6">
                      <div className="pl-14">
                        <div className="w-full h-px bg-gradient-to-r from-purple-500/50 to-transparent mb-4" />
                        <p className="text-gray-300 leading-relaxed">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 底部帮助信息 */}
        <div
          className={`text-center mt-16 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          <div className="bg-slate-800/30 backdrop-blur-sm border border-slate-700/30 rounded-2xl p-8 max-w-2xl mx-auto">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                <HelpCircle className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white">
                {t('faqBottomTitle')}
              </h3>
            </div>
            <p className="text-gray-300 mb-6">{t('faqBottomDescription')}</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {toolUrl ? (
                <Link href={toolUrl}>
                  <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-300 hover:scale-105">
                    {t('faqTryButton')}
                  </button>
                </Link>
              ) : (
                <Link href={'/contact'}>
                  <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg font-semibold transition-all duration-300 hover:scale-105">
                    {t('faqContactButton')}
                  </button>
                </Link>
              )}
              <button className="px-6 py-3 border border-purple-500/50 text-purple-300 hover:bg-purple-500/10 rounded-lg font-semibold transition-all duration-300 hover:border-purple-400">
                {t('faqDocButton')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FAQSection
