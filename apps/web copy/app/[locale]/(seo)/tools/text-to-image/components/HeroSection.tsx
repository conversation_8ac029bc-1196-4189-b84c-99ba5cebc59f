'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@ui/components/button'
import {
  <PERSON><PERSON>les,
  Zap,
  ArrowRight,
  Play,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

interface HeroSectionProps {
  toolUrl: string
}

const HeroSection = ({ toolUrl }: HeroSectionProps) => {
  const t = useTranslations('aiTextToImage')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isVisible, setIsVisible] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  // 示例图片轮播
  const heroImages = [
    '/images/ai-text-to-image/world.png',
    '/images/ai-text-to-image/art.png',
    '/images/ai-text-to-image/movie.png',
    '/images/ai-text-to-image/world1.png',
    '/images/ai-text-to-image/shine.png',
  ]

  useEffect(() => {
    setMounted(true)
    setIsVisible(true)
    const interval = setInterval(() => {
      if (!isPaused) {
        setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
      }
    }, 4000)

    // 键盘导航支持
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        goToPrevious()
      } else if (event.key === 'ArrowRight') {
        goToNext()
      }
    }

    window.addEventListener('keydown', handleKeyDown)

    return () => {
      clearInterval(interval)
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [heroImages.length, isPaused])

  // 手动切换函数
  const goToPrevious = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? heroImages.length - 1 : prev - 1
    )
  }

  const goToNext = () => {
    setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">
      {/* 动态背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              backgroundRepeat: 'repeat',
            }}
          />
        </div>

        {/* 浮动粒子效果 */}
        <div className="absolute inset-0">
          {mounted &&
            [...Array(20)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-purple-400 rounded-full opacity-30 animate-pulse"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${3 + Math.random() * 2}s`,
                }}
              />
            ))}
        </div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* 左侧内容 */}
          <div
            className={`space-y-8 transition-all duration-1000 ${
              isVisible
                ? 'opacity-100 translate-x-0'
                : 'opacity-0 -translate-x-10'
            }`}
          >
            {/* 标签 */}
            <div className="inline-flex items-center gap-2 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-4 py-2 text-purple-300 text-sm font-medium">
              <Sparkles className="w-4 h-4" />
              {t('heroTagText')}
            </div>

            {/* 主标题 */}
            <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight">
              {t('heroTitle')}{' '}
              <span className="text-pink-400">
                {t('heroTitleHighlight')}
              </span>{' '}
              {t('heroTitleEnd')}
            </h1>

            {/* 描述 */}
            <p className="text-xl text-gray-300 leading-relaxed max-w-2xl">
              {t('heroDescription')}
            </p>

            {/* 特性标签 */}
            <div className="flex flex-wrap gap-3">
              {[
                t('heroFeature1'),
                t('heroFeature2'),
                t('heroFeature3'),
                t('heroFeature4'),
              ].map((feature, index) => (
                <span
                  key={feature}
                  className={`px-3 py-1 bg-slate-800/50 border border-slate-700 rounded-full text-sm text-gray-300 transition-all duration-500 hover:border-purple-500/50 hover:text-purple-300 ${
                    isVisible
                      ? 'opacity-100 translate-y-0'
                      : 'opacity-0 translate-y-4'
                  }`}
                  style={{ transitionDelay: `${index * 100}ms` }}
                >
                  {feature}
                </span>
              ))}
            </div>

            {/* CTA按钮 */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href={toolUrl}>
                <Button
                  size="lg"
                  className="group bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105"
                >
                  <Zap className="w-5 h-5 mr-2 group-hover:animate-pulse" />
                  {t('heroStartButton')}
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>

              <Link href={toolUrl}>
                <Button
                  variant="outline"
                  size="lg"
                  className="group border-2 border-purple-500/50 text-purple-300 hover:bg-purple-500/10 px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 hover:border-purple-400"
                >
                  <Play className="w-5 h-5 mr-2" />
                  {t('heroTryButton')}
                </Button>
              </Link>
            </div>
          </div>

          {/* 右侧图片展示 */}
          <div
            className={`relative transition-all duration-1000 delay-300 ${
              isVisible
                ? 'opacity-100 translate-x-0'
                : 'opacity-0 translate-x-10'
            }`}
          >
            <div className="relative">
              {/* 主图片容器 */}
              <div
                className="group relative w-full h-96 md:h-[500px] rounded-2xl overflow-hidden shadow-2xl"
                onMouseEnter={() => setIsPaused(true)}
                onMouseLeave={() => setIsPaused(false)}
              >
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent z-10" />
                {heroImages.map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt={`${t('heroImageAlt')} ${index + 1}`}
                    className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-1000 ${
                      index === currentImageIndex ? 'opacity-100' : 'opacity-0'
                    }`}
                  />
                ))}

                {/* 左右导航按钮 */}
                <button
                  onClick={goToPrevious}
                  className="absolute left-2 md:left-4 top-1/2 -translate-y-1/2 z-20 bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-2 md:p-3 transition-all duration-300 hover:scale-110 opacity-0 group-hover:opacity-100 focus:opacity-100 hover:shadow-lg hover:shadow-purple-500/25"
                  aria-label={t('heroPreviousButton')}
                >
                  <ChevronLeft className="w-4 h-4 md:w-5 md:h-5 text-white hover:text-purple-300 transition-colors" />
                </button>

                <button
                  onClick={goToNext}
                  className="absolute right-2 md:right-4 top-1/2 -translate-y-1/2 z-20 bg-black/50 hover:bg-black/70 backdrop-blur-sm rounded-full p-2 md:p-3 transition-all duration-300 hover:scale-110 opacity-0 group-hover:opacity-100 focus:opacity-100 hover:shadow-lg hover:shadow-purple-500/25"
                  aria-label={t('heroNextButton')}
                >
                  <ChevronRight className="w-4 h-4 md:w-5 md:h-5 text-white hover:text-purple-300 transition-colors" />
                </button>

                {/* 图片标签 */}
                <div className="absolute bottom-4 left-4 z-20 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-2">
                  <p className="text-white text-sm font-medium">
                    {t('heroImageLabel')}
                  </p>
                  <p className="text-gray-300 text-xs">
                    {t('heroImageSubLabel')}
                  </p>
                </div>
              </div>

              {/* 装饰元素 */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full opacity-20 blur-xl animate-pulse" />
              <div
                className="absolute -bottom-4 -left-4 w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full opacity-20 blur-xl animate-pulse"
                style={{ animationDelay: '1s' }}
              />

              {/* 图片指示器 */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2">
                {heroImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentImageIndex
                        ? 'bg-purple-400 w-8'
                        : 'bg-gray-600 hover:bg-gray-500'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
