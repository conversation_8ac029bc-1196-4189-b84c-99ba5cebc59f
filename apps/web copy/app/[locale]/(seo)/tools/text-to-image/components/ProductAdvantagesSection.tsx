'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@ui/components/button'
import { Zap, Shield, Sparkles, ArrowRight, CheckCircle } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import { getPromptDatabase } from '../prompt'
import PhotoGallery from '@/[locale]/components/PhotoGallery'

interface ProductAdvantagesSectionProps {
  toolUrl: string
}

const ProductAdvantagesSection = ({
  toolUrl,
}: ProductAdvantagesSectionProps) => {
  const t = useTranslations('aiTextToImage')
  const promptDatabase = getPromptDatabase(t)
  const [isVisible, setIsVisible] = useState(false)
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const advantages = [
    {
      icon: Zap,
      title: t('advantage1Title'),
      description: t('advantage1Description'),
      image: '/images/ai-text-to-image/robot.png',
      features: [
        t('advantage1Feature1'),
        t('advantage1Feature2'),
        t('advantage1Feature3'),
        t('advantage1Feature4'),
      ],
    },
    {
      icon: Shield,
      title: t('advantage2Title'),
      description: t('advantage2Description'),
      image: '/videos/ai-text-to-image/upload.mp4',
      isVideo: true,
      features: [
        t('advantage2Feature1'),
        t('advantage2Feature2'),
        t('advantage2Feature3'),
        t('advantage2Feature4'),
      ],
    },
  ]
  return (
    <section
      ref={sectionRef}
      className="py-20 bg-gradient-to-b from-slate-900 to-slate-800 relative overflow-hidden"
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl animate-pulse" />
        <div
          className="absolute bottom-20 right-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: '2s' }}
        />
      </div>

      <div className="relative z-10  mx-auto px-4">
        {/* 标题部分 */}
        <div
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="inline-flex items-center gap-2 bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-4 py-2 text-purple-300 text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4" />
            {t('advantagesTagText')}
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('advantagesTitle')}{' '}
            <span className="text-pink-400">
              {t('advantagesTitleHighlight')}
            </span>{' '}
            {t('advantagesTitleEnd')}
          </h2>

          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            {t('advantagesDescription')}
          </p>
        </div>

        {/* 优势展示 */}
        <div className="space-y-20 container">
          {advantages.map((advantage, index) => {
            const Icon = advantage.icon
            const isEven = index % 2 === 0

            return (
              <div
                key={index}
                className={`grid lg:grid-cols-2 gap-12 items-center transition-all duration-1000 ${
                  isVisible
                    ? 'opacity-100 translate-y-0'
                    : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                {/* 图片部分 */}
                <div
                  className={`relative ${isEven ? 'lg:order-1' : 'lg:order-2'}`}
                >
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                    <div className="relative bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-6 group-hover:border-purple-500/30 transition-all duration-300">
                      {advantage.isVideo ? (
                        <>
                          <video
                            muted
                            autoPlay
                            loop
                            playsInline
                            className="w-full aspect-video object-cover rounded-xl"
                            src={advantage.image}
                          ></video>
                        </>
                      ) : (
                        <img
                          src={advantage.image}
                          alt={advantage.title}
                          className="w-full aspect-video object-cover rounded-xl"
                        />
                      )}
                      {/* 浮动特性标签 */}
                      <div className="absolute -top-3 -right-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full p-3 shadow-lg">
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* 内容部分 */}
                <div
                  className={`space-y-6 ${
                    isEven ? 'lg:order-2' : 'lg:order-1'
                  }`}
                >
                  <div className="space-y-4">
                    <h3 className="text-3xl font-bold text-white leading-tight">
                      {advantage.title}
                    </h3>

                    <p className="text-lg text-gray-300 leading-relaxed">
                      {advantage.description}
                    </p>
                  </div>

                  {/* 特性列表 */}
                  <div className="grid grid-cols-2 gap-3">
                    {advantage.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-center gap-2 text-gray-300 hover:text-purple-300 transition-colors duration-200"
                      >
                        <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                        <span className="text-sm font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA按钮 */}
                  <div className="pt-4">
                    <Link href={toolUrl}>
                      <Button className="group bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-purple-500/25 transition-all duration-300 hover:scale-105">
                        {t('advantagesStartButton')}
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* 照片墙组件 */}
        <div className="mt-32">
          <div
            className={`text-center mb-12 transition-all duration-1000 ${
              isVisible
                ? 'opacity-100 translate-y-0'
                : 'opacity-0 translate-y-10'
            }`}
          >
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-purple-500/30 rounded-full px-6 py-3 text-purple-300 text-sm font-medium mb-6">
              <Sparkles className="w-5 h-5" />
              {t('galleryTagText')}
            </div>

            <h3 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {t('galleryTitle')}{' '}
              <span className="text-pink-400">
                {t('galleryTitleHighlight')}
              </span>
            </h3>

            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              {t('galleryDescription')}
            </p>
          </div>
          <PhotoGallery
            promptDatabase={promptDatabase}
            defaultCategory="tattoo"
            toolUrl={toolUrl}
          />
        </div>

        {/* 底部统计 */}
        <div
          className={`mt-20 container grid grid-cols-2 md:grid-cols-4 gap-8 transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
          style={{ transitionDelay: '600ms' }}
        >
          {[
            { number: t('stat1Number'), label: t('stat1Label') },
            { number: t('stat2Number'), label: t('stat2Label') },
            { number: t('stat3Number'), label: t('stat3Label') },
            { number: t('stat4Number'), label: t('stat4Label') },
          ].map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 group-hover:border-purple-500/30 transition-all duration-300">
                <div className="text-3xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400 text-sm">{stat.label}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ProductAdvantagesSection
