import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

export default async function UseCasesSection({
  toolUrl,
}: {
  toolUrl: string
}) {
  const t = await getTranslations()
  const useCases = [
    {
      title: t('phototoanime.useCase1Title'),
      description: t('phototoanime.useCase1Description'),
      image: '/images/photo-to-anime/use-case-1.jpg',
      alt: "A before-and-after image showing a user's photo to anime transformation using the anime girl ai generator.",
      ctaText: t('phototoanime.useCase1Cta'),
      stats: [
        {
          icon: 'fas fa-users',
          value: '5M+',
          label: t('phototoanime.useCase1Stat1Label'),
        },
        {
          icon: 'fas fa-chart-line',
          value: '40%',
          label: t('phototoanime.useCase1Stat2Label'),
        },
        {
          icon: 'fas fa-heart',
          value: '98%',
          label: t('phototoanime.useCase1Stat3Label'),
        },
      ],
    },
    {
      title: t('phototoanime.useCase2Title'),
      description: t('phototoanime.useCase2Description'),
      image: '/images/photo-to-anime/use-case-2.jpg',
      alt: 'A landscape photo that has been converted into the iconic Studio Ghibli art style with our AI tool.',
      ctaText: t('phototoanime.useCase2Cta'),
      stats: [
        {
          icon: 'fas fa-palette',
          value: '98%',
          label: t('phototoanime.useCase2Stat1Label'),
        },
        {
          icon: 'fas fa-clock',
          value: '2 hrs',
          label: t('phototoanime.useCase2Stat2Label'),
        },
        {
          icon: 'fas fa-images',
          value: '1000+',
          label: t('phototoanime.useCase2Stat3Label'),
        },
      ],
    },
    {
      title: t('phototoanime.useCase3Title'),
      description: t('phototoanime.useCase3Description'),
      image: '/images/photo-to-anime/use-case-3.jpg',
      alt: "A pet's photo converted to anime in multiple styles using our ai anime generator.",
      ctaText: t('phototoanime.useCase3Cta'),
      stats: [
        {
          icon: 'fas fa-paw',
          value: '100K+',
          label: t('phototoanime.useCase3Stat1Label'),
        },
        {
          icon: 'fas fa-heart',
          value: '99%',
          label: t('phototoanime.useCase3Stat2Label'),
        },
        {
          icon: 'fas fa-star',
          value: '5★',
          label: t('phototoanime.useCase3Stat3Label'),
        },
      ],
    },
    {
      title: t('phototoanime.useCase4Title'),
      description: t('phototoanime.useCase4Description'),
      image: '/images/photo-to-anime/use-case-4.jpg',
      alt: 'A custom gift created by using our tool to convert a picture to anime.',
      ctaText: t('phototoanime.useCase4Cta'),
      stats: [
        {
          icon: 'fas fa-gift',
          value: '1000+',
          label: t('phototoanime.useCase4Stat1Label'),
        },
        {
          icon: 'fas fa-print',
          value: 'HD',
          label: t('phototoanime.useCase4Stat2Label'),
        },
        {
          icon: 'fas fa-thumbs-up',
          value: '95%',
          label: t('phototoanime.useCase4Stat3Label'),
        },
      ],
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">
              {t('phototoanime.useCasesTitle')}
            </span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('phototoanime.useCasesDescription')}
          </p>
        </div>

        {/* Use Cases Grid */}
        <div className="space-y-16">
          {useCases.map((useCase, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div
                className={`flex flex-col ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } items-center gap-8`}
              >
                {/* Image */}
                <div className="w-full lg:w-1/2">
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300" />
                    <div className="relative">
                      <img
                        src={useCase.image}
                        alt={useCase.alt}
                        className="w-full h-64 md:h-80 object-cover rounded-2xl"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-2xl" />
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="w-full lg:w-1/2 space-y-6">
                  {/* H3 with exact README content */}
                  <h3 className="text-2xl md:text-3xl font-bold text-white">
                    {useCase.title}
                  </h3>

                  {/* P with exact README content */}
                  <p className="text-white/80 leading-relaxed">
                    {useCase.description}
                  </p>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4">
                    {useCase.stats.map((stat, statIndex) => (
                      <div
                        key={statIndex}
                        className="text-center p-3 bg-white/5 rounded-xl border border-white/10"
                      >
                        <i
                          className={`${stat.icon} text-purple-400 text-lg mb-2`}
                        />
                        <div className="text-white font-bold text-lg">
                          {stat.value}
                        </div>
                        <div className="text-white/60 text-xs">
                          {stat.label}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  <div className="pt-4">
                    <Link href={toolUrl}>
                      <CTAButton>
                        <i className="fas fa-wand-magic-sparkles mr-2" />
                        {useCase.ctaText}
                      </CTAButton>
                    </Link>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>
      </div>
    </section>
  )
}
