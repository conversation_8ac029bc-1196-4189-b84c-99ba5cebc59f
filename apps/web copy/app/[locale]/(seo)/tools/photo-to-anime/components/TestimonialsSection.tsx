import { getTranslations } from 'next-intl/server'
import AnimatedCard from './AnimatedCard'

export default async function TestimonialsSection({
  toolUrl,
}: {
  toolUrl: string
}) {
  const t = await getTranslations()
  const testimonials = [
    {
      content: t('phototoanime.testimonial1Content'),
      name: t('phototoanime.testimonial1Name'),
      role: t('phototoanime.testimonial1Role'),
      avatar: '/images/user/user-1.png',
      rating: 5,
    },
    {
      content: t('phototoanime.testimonial2Content'),
      name: t('phototoanime.testimonial2Name'),
      role: t('phototoanime.testimonial2Role'),
      avatar: '/images/user/user-2.png',
      rating: 5,
    },
    {
      content: t('phototoanime.testimonial3Content'),
      name: t('phototoanime.testimonial3Name'),
      role: t('phototoanime.testimonial3Role'),
      avatar: '/images/user/user-3.png',
      rating: 5,
    },
    {
      content: t('phototoanime.testimonial4Content'),
      name: t('phototoanime.testimonial4Name'),
      role: t('phototoanime.testimonial4Role'),
      avatar: '/images/user/user-4.png',
      rating: 5,
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">
              {t('phototoanime.testimonialsTitle')}
            </span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('phototoanime.testimonialsDescription')}
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div className="relative">
                <div className="flex mb-4 justify-between">
                  <div className="flex items-center gap-2">
                    {/* Quote Icon */}
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                      <i className="fas fa-quote-left text-white text-sm" />
                    </div>
                    {/* Stars */}
                    <div className="flex items-center gap-1">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <i
                          key={i}
                          className="fas fa-star text-yellow-400 text-sm"
                        />
                      ))}
                    </div>
                  </div>

                  {/* Verified Badge */}
                  <div className="flex items-center gap-1 px-2 py-1 bg-green-500/20 border border-green-500/30 rounded-full">
                    <i className="fas fa-check-circle text-green-400 text-xs" />
                    <span className="text-green-400 text-xs font-medium">
                      {t('phototoanime.testimonialsVerified')}
                    </span>
                  </div>
                </div>

                {/* Testimonial Content - P with exact README content */}
                <p className="text-white/90 text-lg leading-relaxed mb-6 italic">
                  "{testimonial.content}"
                </p>

                {/* User Info */}
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full" />
                    <img
                      src={testimonial.avatar}
                      alt={`${testimonial.name} - satisfied user`}
                      className="relative w-12 h-12 rounded-full object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">
                      {testimonial.name}
                    </h4>
                    <p className="text-white/60 text-sm">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Social Proof Stats */}
        <div className="mt-16">
          <AnimatedCard delay={400}>
            <div className="text-center py-8">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-white">
                    5M+
                  </div>
                  <div className="text-white/60 text-sm">
                    {t('phototoanime.testimonialsStat1')}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-white">
                    4.9/5
                  </div>
                  <div className="text-white/60 text-sm">
                    {t('phototoanime.testimonialsStat2')}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-white">
                    50K+
                  </div>
                  <div className="text-white/60 text-sm">
                    {t('phototoanime.testimonialsStat3')}
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="text-3xl md:text-4xl font-bold text-white">
                    99%
                  </div>
                  <div className="text-white/60 text-sm">
                    {t('phototoanime.testimonialsStat4')}
                  </div>
                </div>
              </div>
            </div>
          </AnimatedCard>
        </div>
      </div>
    </section>
  )
}
