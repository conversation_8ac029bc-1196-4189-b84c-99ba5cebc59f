import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'
import AnimatedCard from './AnimatedCard'
import CTAButton from './CTAButton'

export default async function HowToSection({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations()
  const steps = [
    {
      number: '01',
      title: t('phototoanime.howToStep1Title'),
      description: t('phototoanime.howToStep1Description'),
      icon: 'fas fa-upload',
      image: '/images/photo-to-anime/how-to-1.jpg',
      alt: 'User uploading a photo to the AI anime generator interface',
    },
    {
      number: '02',
      title: t('phototoanime.howToStep2Title'),
      description: t('phototoanime.howToStep2Description'),
      icon: 'fas fa-wand-magic-sparkles',
      image: '/images/photo-to-anime/how-to-2.jpg',
      alt: 'AI anime generator processing and generating anime-style artwork',
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">
              {t('phototoanime.howToTitle')}
            </span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('phototoanime.howToDescription')}
          </p>
        </div>

        {/* Steps */}
        <div className="space-y-16">
          {steps.map((step, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div
                className={`flex flex-col ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } items-center gap-8`}
              >
                {/* Image */}
                <div className="w-full lg:w-1/2">
                  <div className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-75 transition duration-300" />
                    <div className="relative">
                      <img
                        src={step.image}
                        alt={step.alt}
                        className="w-full h-64 md:h-80 object-cover rounded-2xl"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent rounded-2xl" />

                      {/* Step Number Overlay */}
                      <div className="absolute top-4 left-4">
                        <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-xl">
                            {step.number}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="w-full lg:w-1/2 space-y-6">
                  {/* Icon */}
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl mb-6">
                    <i className={`${step.icon} text-white text-xl`} />
                  </div>

                  {/* H3 with exact README content */}
                  <h3 className="text-2xl md:text-3xl font-bold text-white">
                    {step.title}
                  </h3>

                  {/* P with exact README content */}
                  <p className="text-white/80 leading-relaxed text-lg">
                    {step.description}
                  </p>

                  {/* Additional Features */}
                  <div className="space-y-3">
                    {index === 0 && (
                      <>
                        <div className="flex items-center gap-3 text-white/70">
                          <i className="fas fa-check-circle text-green-400" />
                          <span>{t('phototoanime.howToStep1Feature1')}</span>
                        </div>
                        <div className="flex items-center gap-3 text-white/70">
                          <i className="fas fa-check-circle text-green-400" />
                          <span>{t('phototoanime.howToStep1Feature2')}</span>
                        </div>
                        <div className="flex items-center gap-3 text-white/70">
                          <i className="fas fa-check-circle text-green-400" />
                          <span>{t('phototoanime.howToStep1Feature3')}</span>
                        </div>
                      </>
                    )}
                    {index === 1 && (
                      <>
                        <div className="flex items-center gap-3 text-white/70">
                          <i className="fas fa-check-circle text-green-400" />
                          <span>{t('phototoanime.howToStep2Feature1')}</span>
                        </div>
                        <div className="flex items-center gap-3 text-white/70">
                          <i className="fas fa-check-circle text-green-400" />
                          <span>{t('phototoanime.howToStep2Feature2')}</span>
                        </div>
                        <div className="flex items-center gap-3 text-white/70">
                          <i className="fas fa-check-circle text-green-400" />
                          <span>{t('phototoanime.howToStep2Feature3')}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Bottom CTA - Matching README button style */}
        <div className="text-center mt-16">
          <div className="inline-flex flex-col items-center gap-4 px-8 py-6 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-white/10 rounded-2xl">
            <div className="flex items-center gap-3">
              <i className="fas fa-bolt text-purple-400 text-xl" />
              <span className="text-white font-medium text-lg">
                {t('phototoanime.howToCta')}
              </span>
            </div>
            <Link href={toolUrl}>
              <CTAButton size="lg">
                <i className="fas fa-rocket mr-2" />
                {t('phototoanime.howToCtaButton')}
              </CTAButton>
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
