import { getTranslations } from 'next-intl/server'
import HeroSection from './components/HeroSection'
import AdvantagesSection from './components/AdvantagesSection'
import UseCasesSection from './components/UseCasesSection'
import HowToSection from './components/HowToSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('phototoanime.title'),
    description: t('phototoanime.description'),
    keywords: t('phototoanime.keywords'),
    openGraph: {
      title: t('phototoanime.openGraphTitle'),
      description: t('phototoanime.openGraphDescription'),
      type: 'website',
      url: 'https://imggen.ai/photo-to-anime',
      images: [
        {
          url: 'https://imggen.ai/images/og-photo-to-anime.jpg',
          width: 1200,
          height: 630,
          alt: 'AI Anime Generator: Turn Your Photo to Anime Instantly Online | ImgGen',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('phototoanime.twitterTitle'),
      description: t('phototoanime.twitterDescription'),
      images: ['https://imggen.ai/images/twitter-photo-to-anime.jpg'],
    },
  }
}

export default function PhotoToAnimePage() {
  // 统一配置跳转URL
  const PHOTO_TO_ANIME_TOOL_URL = '/ai/photo-to-anime'

  return (
    <div className="min-h-screen bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      {/* Font Awesome CDN */}
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        crossOrigin="anonymous"
      />

      {/* Structured Data - Matching README Schema.org JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'SoftwareApplication',
            name: 'AI Anime Generator: Turn Your Photo to Anime Instantly Online | ImgGen',
            description:
              'Use our powerful ai anime generator to convert your picture to anime style effortlessly. Discover how to create your own anime character with just one click.',
            applicationCategory: 'MultimediaApplication',
            operatingSystem: 'Web',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD',
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: '4.9',
              reviewCount: '5124',
            },
            image: 'https://imggen.ai/images/schema-photo-to-anime.jpg',
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': 'https://imggen.ai/photo-to-anime',
            },
          }),
        }}
      />

      <main className="relative">
        <HeroSection toolUrl={PHOTO_TO_ANIME_TOOL_URL} />
        <AdvantagesSection toolUrl={PHOTO_TO_ANIME_TOOL_URL} />
        <UseCasesSection toolUrl={PHOTO_TO_ANIME_TOOL_URL} />
        <HowToSection toolUrl={PHOTO_TO_ANIME_TOOL_URL} />
        <TestimonialsSection toolUrl={PHOTO_TO_ANIME_TOOL_URL} />
        <FAQSection toolUrl={PHOTO_TO_ANIME_TOOL_URL} />
      </main>
    </div>
  )
}
