import HeroSection from './components/HeroSection'
import AdvantagesSection from './components/AdvantagesSection'
import UseCasesSection from './components/UseCasesSection'
import HowToSection from './components/HowToSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'

export async function generateMetadata() {
  return {
    title: 'AI Clothes Changer & Virtual Try-On Clothing Tool | ImgGen',
    description:
      'Experience the future with our AI clothes changer. Use our free online AI virtual clothes try-on generator to swap outfits instantly and find your perfect look.',
    keywords:
      'AI clothes changer, clothes swap AI, virtual try on clothing, dress change AI, magic clothing, AI clothing generator, free online AI virtual clothes try-on generator, virtual dressing room, how to change clothes with AI, how to swap clothes virtually with AI for free',
    openGraph: {
      title: 'AI Clothes Changer & Virtual Try-On Clothing Tool | ImgGen',
      description:
        'Experience the future with our AI clothes changer. Use our free online AI virtual clothes try-on generator to swap outfits instantly and find your perfect look.',
      type: 'website',
      url: 'https://imggen.ai/ai-clothes-changer',
      images: [
        {
          url: 'https://imggen.ai/images/og-ai-clothes-changer.jpg',
          width: 1200,
          height: 630,
          alt: 'AI Clothes Changer & Virtual Try-On Clothing Tool | ImgGen',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: 'AI Clothes Changer & Virtual Try-On Clothing Tool | ImgGen',
      description:
        'Experience the future with our AI clothes changer. Use our free online AI virtual clothes try-on generator to swap outfits instantly and find your perfect look.',
      images: ['https://imggen.ai/images/twitter-ai-clothes-changer.jpg'],
    },
  }
}

const Index = async () => {
  const TRANSFORMATION_URL = '/ai/virtual-try-on'
  return (
    <div className="min-h-screen bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      {/* Font Awesome CDN */}
      <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        crossOrigin="anonymous"
      />

      {/* Structured Data - Matching README Schema.org JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'SoftwareApplication',
            name: 'AI Clothes Changer & Virtual Try-On Clothing Tool | ImgGen',
            description:
              'Experience the future with our AI clothes changer. Use our free online AI virtual clothes try-on generator to swap outfits instantly and find your perfect look.',
            applicationCategory: 'MultimediaApplication',
            operatingSystem: 'Web',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'USD',
            },
            aggregateRating: {
              '@type': 'AggregateRating',
              ratingValue: '4.8',
              reviewCount: '1250',
            },
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': 'https://imggen.ai/ai-clothes-changer',
            },
          }),
        }}
      />

      <main className="relative">
        <HeroSection toolUrl={TRANSFORMATION_URL} />
        <AdvantagesSection toolUrl={TRANSFORMATION_URL} />
        <UseCasesSection toolUrl={TRANSFORMATION_URL} />
        <HowToSection toolUrl={TRANSFORMATION_URL} />
        <TestimonialsSection />
        <FAQSection toolUrl={TRANSFORMATION_URL}/>
      </main>
    </div>
  )
}

export default Index
