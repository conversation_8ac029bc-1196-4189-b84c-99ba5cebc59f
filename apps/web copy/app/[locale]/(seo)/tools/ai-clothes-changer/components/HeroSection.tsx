import { getTranslations } from 'next-intl/server'
import CTAButton from './CTAButton'

export default async function HeroSection({ toolUrl }: { toolUrl: string }) {
  const t = await getTranslations('aiClothesChanger')
  return (
    <section className="relative min-h-screen flex flex-col items-center justify-center px-4 pt-20 pb-16 overflow-hidden bg-gradient-to-r from-[#3b0764] via-[#10051A] to-[#3b0764]">
      <div className="relative z-10 max-w-7xl w-full mx-auto">
        <div className="text-center mb-6 lg:mb-12">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white/80 text-sm mb-3">
            <i className="fas fa-sparkles text-purple-400" />
            <span>{t('aiPoweredBadge')}</span>
          </div>

          {/* Main Title */}
          <h1 className="text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-2 lg:mb-3 leading-tight">
            <span className="text-white">{t('heroTitle1')}</span>
            <br />
            <span className="text-purple-400">{t('heroTitle2')}</span>
          </h1>

          {/* Description */}
          <p className="text-base md:text-lg lg:text-xl text-white/80 max-w-3xl mx-auto mb-3 lg:mb-4 leading-relaxed">
            {t('heroDescription')}
          </p>

          {/* CTA Button */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <CTAButton size="lg" className="w-full sm:w-auto" href={toolUrl}>
              <i className="fas fa-upload mr-2" />
              {t('tryAiChangerButton')}
            </CTAButton>
            <CTAButton
              variant="secondary"
              size="lg"
              className="w-full sm:w-auto"
            >
              <i className="fas fa-play mr-2" />
              {t('watchDemoButton')}
            </CTAButton>
          </div>
        </div>

        {/* Before/After Preview (保持不变) */}
        <div className="relative max-w-5xl mx-auto hidden md:block">
          <div className="flex items-center justify-center gap-6 lg:gap-8">
            {/* Before Image */}
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300" />
              <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-3 lg:p-4">
                <div className="text-center mb-2 lg:mb-3">
                  <span className="inline-flex items-center gap-2 px-2 lg:px-3 py-1 bg-white/20 rounded-full text-white text-xs lg:text-sm font-medium">
                    <i className="fas fa-user" />
                    {t('beforeLabel')}
                  </span>
                </div>
                <img
                  src="/images/ai-clothes-changer/before.jpg"
                  alt="Before: Person in plain white t-shirt"
                  className="w-full max-w-[200px] lg:max-w-xs rounded-xl"
                />
              </div>
            </div>

            {/* Arrow */}
            <div className="flex items-center justify-center">
              <div className="p-3 lg:p-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full">
                <i className="fas fa-arrow-right text-white text-lg lg:text-xl" />
              </div>
            </div>

            {/* After Image */}
            <div className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300" />
              <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-3 lg:p-4">
                <div className="text-center mb-2 lg:mb-3">
                  <span className="inline-flex items-center gap-2 px-2 lg:px-3 py-1 bg-white/20 rounded-full text-white text-xs lg:text-sm font-medium">
                    <i className="fas fa-magic" />
                    {t('afterLabel')}
                  </span>
                </div>
                <img
                  src="/images/ai-clothes-changer/after.jpg"
                  alt="After: Same person in elegant business suit through AI clothes changer"
                  className="w-full max-w-[200px] lg:max-w-xs rounded-xl"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Before/After Preview (保持不变) */}
      <div className="animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
        </div>
      </div>
    </section>
  )
}
