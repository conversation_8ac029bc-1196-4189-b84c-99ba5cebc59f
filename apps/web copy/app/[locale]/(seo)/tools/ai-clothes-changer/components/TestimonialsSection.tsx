import { getTranslations } from 'next-intl/server'
import AnimatedCard from './AnimatedCard'

export default async function TestimonialsSection() {
  const t = await getTranslations('aiClothesChanger')
  const testimonials = [
    {
      name: t('testimonial1Name'),
      role: t('testimonial1Role'),
      avatar: '/images/user/user-1.png',
      content: t('testimonial1Quote'),
      rating: 5,
      company: 'Fashion Forward Boutique',
    },
    {
      name: t('testimonial2Name'),
      role: t('testimonial2Role'),
      avatar: '/images/user/user-2.png',
      content: t('testimonial2Quote'),
      rating: 5,
      company: 'StyleExplorer',
    },
    {
      name: t('testimonial3Name'),
      role: t('testimonial3Role'),
      avatar: '/images/user/user-3.png',
      content: t('testimonial3Quote'),
      rating: 5,
      company: 'Elite Styling Studio',
    },
    {
      name: t('testimonial4Name'),
      role: t('testimonial4Role'),
      avatar: '/images/user/user-4.png',
      content: t('testimonial4Quote'),
      rating: 5,
      company: '@DavidCreates',
    },
  ]

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header - H2 with exact README content */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('testimonialsTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('testimonialsDescription')}
          </p>

          {/* Trust Indicators */}
          <div className="flex flex-wrap items-center justify-center gap-8 mt-8">
            <div className="flex items-center gap-2">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <i key={i} className="fas fa-star text-yellow-400 text-lg" />
                ))}
              </div>
              <span className="text-white/80 font-semibold">
                4.8/5 {t('ratingLabel')}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <i className="fas fa-users text-purple-400 text-xl" />
              <span className="text-white/80 font-semibold">
                100K+ {t('usersLabel')}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <i className="fas fa-shield-alt text-green-400 text-xl" />
              <span className="text-white/80 font-semibold">
                {t('securePrivateLabel')}
              </span>
            </div>
          </div>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div className="relative">
                {/* Content */}
                <div className="flex mb-4 justify-between">
                  <div className="flex items-center gap-2">
                    {/* Quote Icon */}
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                      <i className="fas fa-quote-left text-white text-sm" />
                    </div>
                    {/* Stars */}
                    <div className="flex items-center gap-1">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <i
                          key={i}
                          className="fas fa-star text-yellow-400 text-sm"
                        />
                      ))}
                    </div>
                  </div>

                  {/* Verified Badge */}
                  <div className="flex items-center gap-1 px-2 py-1 bg-green-500/20 border border-green-500/30 rounded-full">
                    <i className="fas fa-check-circle text-green-400 text-xs" />
                    <span className="text-green-400 text-xs font-medium">
                      {t('verifiedLabel')}
                    </span>
                  </div>
                </div>

                {/* Testimonial Text - P with exact README content */}
                <p className="text-white/90 leading-relaxed mb-6 text-lg">
                  "{testimonial.content}"
                </p>

                {/* Author Info */}
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur opacity-75" />
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="relative w-12 h-12 rounded-full object-cover"
                    />
                  </div>
                  <div>
                    <div className="text-white font-semibold">
                      {testimonial.name}
                    </div>
                    <div className="text-white/60 text-sm">
                      {testimonial.role}
                    </div>
                    <div className="text-purple-400 text-xs">
                      {testimonial.company}
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        {/* Bottom Stats */}
        <AnimatedCard delay={400} className="mt-16">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-8">
              {t('joinGrowingCommunityTitle')}
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-pink-400 mb-2">
                  100K+
                </div>
                <div className="text-white/70 text-sm">
                  {t('happyUsersLabel')}
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-purple-400 mb-2">
                  1M+
                </div>
                <div className="text-white/70 text-sm">
                  {t('photosProcessedLabel')}
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-2">
                  50+
                </div>
                <div className="text-white/70 text-sm">
                  {t('countriesLabel')}
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-purple-400 mb-2">
                  4.8★
                </div>
                <div className="text-white/70 text-sm">
                  {t('averageRatingLabel')}
                </div>
              </div>
            </div>
          </div>
        </AnimatedCard>
      </div>
    </section>
  )
}
