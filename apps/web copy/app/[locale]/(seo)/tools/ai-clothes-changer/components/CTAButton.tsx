'use client'

import { Link } from '@i18n/routing'

interface CTAButtonProps {
  children: React.ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  href?: string
  target?: string
}

export default function CTAButton({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  className = '',
  href,
}: CTAButtonProps) {
  const baseClasses =
    'relative inline-flex items-center justify-center font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-purple-500/50 active:scale-95 overflow-hidden group'

  const variantClasses = {
    primary:
      'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg hover:shadow-xl',
    secondary:
      'bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white',
  }

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  }

  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      onClick()
    }
  }

  // If href is provided, use Link component for internal navigation

  return (
    <Link
      href={href || ''}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      {/* Shine effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

      {/* Content */}
      <span className="relative flex items-center gap-2">{children}</span>
    </Link>
  )
}
