'use client'
import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>, Zap } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

const AdvantagesSection = ({ link = '' }) => {
  const t = useTranslations('aiHeadshotGenerator')
  return (
    <section className="py-20 bg-gradient-to-br from-purple-900 to-pink-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-purple-500 rounded-full filter blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-pink-500 rounded-full filter blur-3xl animate-pulse" />
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('whyUseTitle')}
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            {t('whyUseDescription')}
          </p>
        </div>

        <AnimatedCard>
          <div className="max-w-4xl mx-auto mb-16">
            <div className="bg-white/5 hover:bg-white/10 shadow-violet-950 backdrop-blur-lg hover:backdrop-blur-3xl hover:shadow-md transition-all duration-150 ease-in rounded-2xl p-4 grid grid-cols-1 gap-4">
              <div className="relative">
                <img
                  src="/images/ai-headshot-generator/id-photo-before-after.webp"
                  alt={t('beforeAfterComparison')}
                  className="w-full aspect-[21/9] object-cover rounded-xl"
                />
                <div className="absolute shadow-sm shadow-white/40 bottom-4 left-4 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full text-white font-medium">
                  {t('before')}
                </div>
                <div className="absolute shadow-sm shadow-fuchsia-400 bottom-4 right-4 bg-gradient-to-r from-pink-500/90 to-purple-500 px-4 py-2 rounded-full text-white font-medium">
                  {t('after')}
                </div>
              </div>
            </div>
          </div>
        </AnimatedCard>
        {/* Before/After Comparison */}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
            <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-12 h-12 rounded-xl flex items-center justify-center mb-6">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-semibold text-white mb-4">
              {t('advantage1Title')}
            </h3>
            <p className="text-gray-300">
              {t('advantage1Description')}
            </p>
          </div>

          <div className="bg-white/5 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
            <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-12 h-12 rounded-xl flex items-center justify-center mb-6">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-semibold text-white mb-4">
              {t('advantage2Title')}
            </h3>
            <p className="text-gray-300">
              {t('advantage2Description')}
            </p>
          </div>
        </div>

        <div className="text-center">
          <Link
            href={link}
            className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold rounded-xl text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20"
          >
            {t('tryImgGenNow')}
          </Link>
        </div>
      </div>
    </section>
  )
}

export default AdvantagesSection
