'use client'
import React from 'react'
import { <PERSON> } from '@i18n/routing'
import { ArrowRight } from 'lucide-react'
import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

const UseCaseSection = ({ link = '' }) => {
  const t = useTranslations('aiHeadshotGenerator')
  return (
    <section className="py-20 bg-gradient-to-br from-purple-900 to-purple-950">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('diverseApplicationsTitle')}
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            {t('diverseApplicationsDescription')}
          </p>
        </div>

        {/* LinkedIn Case */}
        <div className="grid md:grid-cols-2 gap-8 mb-16 items-center">
          <AnimatedCard>
            <div className="relative group">
              <img
                src="/images/ai-headshot-generator/use-case-1.webp"
                alt={t('linkedinProfilePictureAlt')}
                className="rounded-2xl shadow-2xl transform transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent transform transition-transform duration-500 group-hover:scale-105 to-transparent rounded-2xl" />
              <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full text-purple-900 font-medium">
                {t('moreProfileViews')}
              </div>
            </div>
          </AnimatedCard>
          <div className="md:pl-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('corporateHeadshotTitle')}
            </h3>
            <p className="text-gray-300 mb-6">
              {t('corporateHeadshotDescription')}
            </p>
            <Link
              href={link}
              className="inline-flex items-center gap-2 text-pink-400 hover:text-pink-300 transition-colors font-semibold"
            >
              {t('createYourHeadshot')} <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Gaming/Boys Case */}
        <div className="grid md:grid-cols-2 gap-8 mb-16 items-center">
          <div className="md:order-2">
            <AnimatedCard>
              <img
                src="/images/ai-headshot-generator/boys-avatar.png"
                alt={t('boysAvatarAlt')}
                className="rounded-2xl shadow-2xl transform transition-transform duration-500 hover:scale-105"
              />
            </AnimatedCard>
          </div>
          <div className="md:pr-8 md:order-1">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('boysProfileTitle')}
            </h3>
            <p className="text-gray-300 mb-6">
              {t('boysProfileDescription')}
            </p>
            <Link
              href={link}
              className="inline-flex items-center gap-2 text-pink-400 hover:text-pink-300 transition-colors font-semibold"
            >
              {t('designYourPfp')} <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* ID Photo Case */}
        <div className="grid md:grid-cols-2 gap-8 mb-16 items-center">
          <AnimatedCard>
            <div className="relative group grid grid-cols-3 gap-4">
              <img
                src="/images/ai-headshot-generator/id-before.webp"
                alt={t('originalPhotoAlt')}
                className="rounded-lg h-full object-cover shadow-xl col-span-1"
              />
              <div className="col-span-2 grid grid-cols-2 grid-rows-2 gap-2">
                <img
                  src="/images/ai-headshot-generator/id-1.webp"
                  alt={t('idPhotoVariant1')}
                  className="rounded-lg shadow-xl"
                />
                <img
                  src="/images/ai-headshot-generator/id-2.webp"
                  alt={t('idPhotoVariant2')}
                  className="rounded-lg shadow-xl"
                />
                <img
                  src="/images/ai-headshot-generator/id-3.webp"
                  alt={t('idPhotoVariant3')}
                  className="rounded-lg shadow-xl"
                />
                <img
                  src="/images/ai-headshot-generator/id-4.webp"
                  alt={t('idPhotoVariant4')}
                  className="rounded-lg shadow-xl"
                />
              </div>
            </div>
          </AnimatedCard>
          <div className="md:pl-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('idPhotoTitle')}
            </h3>
            <p className="text-gray-300 mb-6">
              {t('idPhotoDescription')}
            </p>
            <Link
              href={link}
              className="inline-flex items-center gap-2 text-pink-400 hover:text-pink-300 transition-colors font-semibold"
            >
              {t('createIdPhotos')} <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>

        {/* Preppy Style Case */}
        <div className="grid md:grid-cols-2 gap-8 mb-16 items-center">
          
          <div className="md:pl-8 max-md:order-2">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('preppyStyleTitle')}
            </h3>
            <p className="text-gray-300 mb-6">
              {t('preppyStyleDescription')}
            </p>
            <Link
              href={link}
              className="inline-flex items-center gap-2 text-pink-400 hover:text-pink-300 transition-colors font-semibold"
            >
              {t('findYourStyle')} <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
          <div className='max-md:order-1'>
            <AnimatedCard>
              <img
                src="/images/ai-headshot-generator/ins-girl.png"
                alt={t('preppyStyleAlt')}
                className="rounded-2xl shadow-2xl transform transition-transform duration-500 hover:scale-105"
              />
            </AnimatedCard>
          </div>
        </div>

        {/* Preview Feature Case */}
        <div className="grid md:grid-cols-2 gap-8 items-center">
          <div className="">
            <AnimatedCard>
              <img
                src="/images/ai-headshot-generator/stander.webp"
                alt={t('standGeneratorAlt')}
                className="rounded-2xl shadow-2xl transform transition-transform duration-500 hover:scale-105"
              />
            </AnimatedCard>
          </div>
          <div className="md:pr-8 ">
            <h3 className="text-2xl font-bold text-white mb-4">
              {t('standGeneratorTitle')}
            </h3>
            <p className="text-gray-300 mb-6">
              {t('standGeneratorDescription')}
            </p>
            <Link
              href={link}
              className="inline-flex items-center gap-2 text-pink-400 hover:text-pink-300 transition-colors font-semibold"
            >
              {t('upgradeYourSocials')} <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

export default UseCaseSection
