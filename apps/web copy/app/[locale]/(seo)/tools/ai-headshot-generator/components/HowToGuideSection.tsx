'use client'
import React from 'react'
import { Upload, ImagePlus } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

const HowToGuideSection = ({ link = '' }) => {
  const t = useTranslations('aiHeadshotGenerator')

  const steps = [
    {
      icon: <Upload className="w-12 h-12 text-pink-400" />,
      title: t('step1Title'),
      description: t('step1Description'),
    },
    {
      icon: <ImagePlus className="w-12 h-12 text-purple-400" />,
      title: t('step2Title'),
      description: t('step2Description'),
    },
  ]
  return (
    <section className="py-20 bg-gradient-to-br from-purple-950 via-purple-900 to-pink-900 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/3 left-1/3 w-96 h-96 bg-purple-500 rounded-full filter blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-pink-500 rounded-full filter blur-3xl animate-pulse" />
      </div>

      <div className="max-w-7xl mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('howItWorksTitle')}
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            {t('howItWorksDescription')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {steps.map((step, index) => (
            <AnimatedCard key={index}>
              <div
                key={index}
                className="bg-white/5 backdrop-blur-lg rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-300"
              >
                <div className="bg-white/10 rounded-xl p-4 w-fit mb-6">
                  {step.icon}
                </div>
                <h3 className="text-2xl font-semibold text-white mb-4">
                  {step.title}
                </h3>
                <p className="text-gray-300">{step.description}</p>
              </div>
            </AnimatedCard>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            href={link}
            className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white font-semibold rounded-xl text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20"
          >
            {t('startCreatingNow')}
          </Link>
        </div>
      </div>
    </section>
  )
}

export default HowToGuideSection
