'use client'
import React from 'react'
import { Star } from 'lucide-react'
import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

const TestimonialsSection = () => {
  const t = useTranslations('aiHeadshotGenerator')

  const testimonials = [
    {
      content: t('testimonial1Content'),
      name: t('testimonial1Name'),
      role: t('testimonial1Role'),
      avatar: 'https://plus.unsplash.com/premium_photo-1658527049634-15142565537a?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8YXZhdGFyfGVufDB8fDB8fHww',
    },
    {
      content: t('testimonial2Content'),
      name: t('testimonial2Name'),
      role: t('testimonial2Role'),
      avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8YXZhdGFyfGVufDB8fDB8fHww',
    },
    {
      content: t('testimonial3Content'),
      name: t('testimonial3Name'),
      role: t('testimonial3Role'),
      avatar: 'https://plus.unsplash.com/premium_photo-1671656349218-5218444643d8?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8YXZhdGFyfGVufDB8fDB8fHww',
    },
    {
      content: t('testimonial4Content'),
      name: t('testimonial4Name'),
      role: t('testimonial4Role'),
      avatar: 'https://plus.unsplash.com/premium_photo-1690407617542-2f210cf20d7e?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTd8fGF2YXRhcnxlbnwwfHwwfHx8MA%3D%3D',
    },
  ]
  return (
    <section className="py-20 bg-gradient-to-br from-purple-900 to-purple-950">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('testimonialsTitle')}
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            {t('testimonialsDescription')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {testimonials.map((testimonial, index) => (
            <AnimatedCard key={index}>
              <div
                key={index}
                className="bg-white/5 backdrop-blur-lg md:h-full rounded-2xl p-6 border border-white/10 hover:border-white/20 transition-all duration-300"
              >
                <div className="flex gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-4 h-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
                <p className="text-gray-300 mb-6 italic">
                  "{testimonial.content}"
                </p>
                <div className="flex items-center gap-3 border-t border-white/10 pt-4">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-10 h-10 rounded-full object-cover bg-purple-800/50"
                  />
                  <div>
                    <div className="font-semibold text-white">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-400">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>

        <AnimatedCard>
          <div className="mt-12 flex flex-wrap justify-center gap-8">
            <div className="bg-white/5 backdrop-blur-lg rounded-2xl px-8 py-4 flex items-center gap-8">
              <div className="flex items-center gap-2">
                <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                <span className="text-white font-bold text-xl">{t('rating')}</span>
              </div>
              <div className="text-gray-300">
                {t('basedOnReviews')}{' '}
                <span className="text-white font-semibold">1,250+</span> {t('reviews')}
              </div>
            </div>
          </div>
        </AnimatedCard>
      </div>
    </section>
  )
}

export default TestimonialsSection
