'use client'
import React from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useTranslations } from 'next-intl'
import AnimatedCard from './AnimateCard'

const FAQSection = () => {
  const t = useTranslations('aiHeadshotGenerator')
  const [expandedFAQ, setExpandedFAQ] = React.useState<number | null>(0)

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
  ]

  return (
    <section className="py-20 bg-gradient-to-br from-purple-950 to-purple-900">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            {t('faqTitle')}
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            {t('faqDescription')}
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <AnimatedCard key={index}>
              <div
                key={index}
                className="bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 overflow-hidden transition-all duration-300 hover:border-white/20"
              >
                <button
                  onClick={() =>
                    setExpandedFAQ(expandedFAQ === index ? null : index)
                  }
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors"
                  aria-expanded={expandedFAQ === index}
                  aria-controls={`faq-panel-${index}`}
                >
                  <h3 className="text-lg font-semibold text-white pr-4">
                    {faq.question}
                  </h3>
                  {expandedFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-pink-400 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-pink-400 flex-shrink-0" />
                  )}
                </button>

                <div
                  id={`faq-panel-${index}`}
                  className={`transition-all duration-300 ease-in-out overflow-hidden ${
                    expandedFAQ === index
                      ? 'max-h-96 opacity-100'
                      : 'max-h-0 opacity-0'
                  }`}
                >
                  <div className="p-6 pt-0">
                    <p className="text-gray-300">{faq.answer}</p>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>
      </div>
    </section>
  )
}

export default FAQSection
