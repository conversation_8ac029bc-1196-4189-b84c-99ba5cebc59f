import { Metadata } from 'next'
import React from 'react'
import { getTranslations } from 'next-intl/server'
import HeaderSection from './components/HeaderSection'
import AdvantagesSection from './components/AdvantagesSection'
import UseCaseSection from './components/UseCaseSection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'
import CTASection from './components/CTASection'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('aiHeadshotGenerator')

  const title = t('title')
  const description = t('description')
  const keywords = t('keywords')
  const openGraphAlt = t('openGraphAlt')

  return {
    title,
    description,
    keywords: keywords.split(', '),
    authors: [{ name: 'ImgGen Team' }],
    creator: 'ImgGen',
    publisher: 'ImgGen',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://imggen.ai'),
    alternates: {
      canonical: 'https://imggen.ai/pfp-maker',
    },
    openGraph: {
      type: 'website',
      url: 'https://imggen.ai/pfp-maker',
      title,
      description,
      siteName: 'ImgGen',
      images: [
        {
          url: 'https://imggen.ai/images/og-pfp-maker.jpg',
          width: 1200,
          height: 630,
          alt: openGraphAlt,
        },
      ],
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: ['https://imggen.ai/images/twitter-pfp-maker.jpg'],
      creator: '@imggen_ai',
      site: '@imggen_ai',
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code',
      yahoo: 'your-yahoo-verification-code',
    },
    other: {
      'application-name': 'ImgGen',
      'apple-mobile-web-app-title': 'ImgGen',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'mobile-web-app-capable': 'yes',
      'msapplication-TileColor': '#8B5CF6',
      'msapplication-config': '/browserconfig.xml',
      'theme-color': '#8B5CF6',
    },
  }
}

const link = '/ai/ai-headshot-generator'

export default async function AIHeadshotGeneratorPage() {
  const t = await getTranslations('aiHeadshotGenerator')

  // Schema.org JSON-LD structured data
  const generateStructuredData = () => {
    return {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: t('title'),
      description: t('description'),
      applicationCategory: 'DesignApplication',
      operatingSystem: 'Web',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
      },
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: '4.9',
        reviewCount: '1250',
      },
      image: 'https://imggen.ai/images/schema-pfp-maker.jpg',
    }
  }

  const structuredData = generateStructuredData()

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <main className="min-h-screen bg-purple-950">
        <HeaderSection link={link} />
        <AdvantagesSection link={link} />
        <UseCaseSection link={link} />
        <HowToGuideSection link={link} />
        <TestimonialsSection />
        <FAQSection />
        <CTASection link={link} />
      </main>
    </>
  )
}
