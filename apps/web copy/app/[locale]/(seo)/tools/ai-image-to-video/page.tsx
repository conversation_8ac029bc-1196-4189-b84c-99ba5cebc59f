import { getTranslations } from 'next-intl/server'
import { Metadata } from 'next'
import AdvantagesSection from './components/AdvantagesSection'
import HeroSection from './components/HeroSection'
import UseCaseSection from './components/UseCaseSection'
import HowItWorksSection from './components/HowItWorksSection'
import TestimonialsSection from './components/TestimonialsSection'
import FAQSection from './components/FAQSection'
import CTASection from './components/CTASection'

const url = 'https://imggen.ai/photo-to-video-ai-free'
const ogImage = 'https://imggen.ai/images/og-photo-to-video.jpg'
const twitterImage = 'https://imggen.ai/images/twitter-photo-to-video.jpg'

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations('aiImageToVideo')

  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords').split(', '),
    authors: [{ name: 'ImgGen Team' }],
    creator: 'ImgGen',
    publisher: 'ImgGen',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://imggen.ai'),
    alternates: {
      canonical: url,
    },
    openGraph: {
      type: 'website',
      url,
      title: t('openGraphTitle'),
      description: t('openGraphDescription'),
      siteName: 'ImgGen',
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: t('openGraphTitle'),
        },
      ],
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title: t('twitterTitle'),
      description: t('twitterDescription'),
      images: [twitterImage],
      creator: '@imggen_ai',
      site: '@imggen_ai',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      google: 'your-google-verification-code',
      yandex: 'your-yandex-verification-code',
      yahoo: 'your-yahoo-verification-code',
    },
    other: {
      'application-name': 'ImgGen',
      'apple-mobile-web-app-title': 'ImgGen',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'mobile-web-app-capable': 'yes',
      'msapplication-TileColor': '#8B5CF6',
      'msapplication-config': '/browserconfig.xml',
      'theme-color': '#8B5CF6',
    },
  }
}

// Schema.org JSON-LD structured data
const generateStructuredData = (t: any) => {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('title'),
    description: t('description'),
    applicationCategory: 'MultimediaApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '1250',
    },
    url: 'https://imggen.ai/photo-to-video-ai-free',
  }
}
const link = 'ai/ai-image-to-video'
const Index = async () => {
  const t = await getTranslations('aiImageToVideo')
  const structuredData = generateStructuredData(t)

  return (
    <>
      {/* Schema.org Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <div className="min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-pink-900">
        {/* Hero Section */}
        <HeroSection link={link} />
        {/* Advantages Section */}
        <AdvantagesSection link={link} />
        {/* Demo Section */}
        {/* <DemoSection /> */}
        {/* Use case Section */}
        <UseCaseSection link={link} />
        {/* How It Works Section */}
        <HowItWorksSection />
        {/* Testimonials Section */}
        <TestimonialsSection />
        {/* FAQ Section */}
        <FAQSection link={link} />
        {/* CTA Section */}
        <CTASection link={link} />
      </div>
    </>
  )
}

export default Index
