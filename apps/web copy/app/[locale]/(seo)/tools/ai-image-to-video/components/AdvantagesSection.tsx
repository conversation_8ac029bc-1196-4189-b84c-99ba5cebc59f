import React from 'react'
import AnimatedCard from './AnimateCard'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const AdvantagesSection = ({link = ''}) => {
  const t = useTranslations('aiImageToVideo')
  return (
    <section className="py-20 bg-gradient-to-br from-purple-900 via-indigo-900 to-pink-800/80">
      <div className="max-w-5xl mx-auto px-4">
        <h2 className="text-3xl sm:text-4xl font-bold text-white mb-8 text-center">
          {t('advantagesTitle')}
        </h2>
        <p className="text-lg text-gray-200 mb-12 text-center max-w-3xl mx-auto">
          {t('advantagesDescription')}
        </p>

        <div className="grid md:grid-cols-1 gap-8">
          <AnimatedCard>
            <div className=" h-full rounded-2xl shadow-xl overflow-hidden flex flex-col md:flex-row">
              <div className="image-placeholder md:w-2/5 flex items-center justify-center p-6">
                <div className="w-full h-56 rounded-xl flex flex-col items-center justify-center">
                  <img
                    src="/images/ai-image-to-video/easy-animation-imgGen.png"
                    className="w-full h-full rounded-2xl object-cover opacity-90"
                    alt=""
                  />
                </div>
              </div>

              <div className="md:w-3/5 p-6 md:p-8 flex flex-col gap-3">
                <h3 className="text-xl font-bold text-white">
                  {t('advantage1Title')}
                </h3>
                <p className="text-gray-200">
                  {t('advantage1Description')}
                </p>
              </div>
            </div>
          </AnimatedCard>
          <AnimatedCard>
            <div className=" h-full rounded-2xl shadow-xl overflow-hidden flex flex-col md:flex-row">
              <div className="image-placeholder md:w-2/5 flex items-center justify-center p-6">
                <div className="w-full h-56 rounded-xl flex flex-col items-center justify-center">
                  <video
                    muted
                    autoPlay
                    preload="metadata"
                    loop
                    playsInline
                    className="w-full h-full rounded-2xl object-cover opacity-90"
                    src="/videos/ai-image-to-video/ai-shield.mp4"
                  ></video>
                </div>
              </div>

              <div className="md:w-3/5 p-6 md:p-8 flex flex-col gap-3">
                <h3 className="text-xl font-bold text-white">
                  {t('advantage2Title')}
                </h3>
                <p className="text-gray-200">
                  {t('advantage2Description')}
                </p>
              </div>
            </div>
          </AnimatedCard>
        </div>

        <div className="flex justify-center mt-10">
          <Link href={link} className="glow-button group px-8 py-4 bg-gradient-to-r from-pink-500 via-fuchsia-500 to-purple-500 text-white font-bold rounded-full text-lg transition-all duration-300 hover:scale-105 hover:shadow-xl relative overflow-hidden">
            <span className="relative z-10">{t('startCreatingFree')}</span>
            <span className="absolute inset-0 bg-gradient-to-r from-pink-600 via-fuchsia-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity"></span>
          </Link>
        </div>
      </div>
    </section>
  )
}
export default AdvantagesSection
