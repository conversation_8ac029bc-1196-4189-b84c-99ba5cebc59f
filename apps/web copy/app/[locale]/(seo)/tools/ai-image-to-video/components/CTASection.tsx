import React from 'react'
import { Play, Upload } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const CTASection = ({link = ''}) => {
  const t = useTranslations('aiImageToVideo')
  return (
    <div className="aii-cta-section py-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
          {t('ctaTitle')}
        </h2>
        <p className="text-lg sm:text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          {t('ctaDescription')}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href={link} className="group px-6 py-3 bg-gradient-to-r from-pink-500 to-purple-500 text-white font-semibold rounded-full text-lg transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/30">
            <span className="flex items-center justify-center gap-2">
              <Upload className="w-5 h-5" />
              {t('createFirstAnimation')}
            </span>
          </Link>
          <Link href={link} className="group px-6 py-3 border-2 border-white/30 text-white font-semibold rounded-full text-lg transition-all duration-300 hover:bg-white/10 hover:border-white/50 backdrop-blur-sm">
            <span className="flex items-center justify-center gap-2">
              <Play className="w-5 h-5" />
              {t('seeExamples')}
            </span>
          </Link>
        </div>
      </div>
    </div>
  )
}
export default CTASection
