'use client'
import { Play, Pause, Volume2, VolumeX } from 'lucide-react'
import React, { useState, useRef, useEffect } from 'react'
import { useTranslations } from 'next-intl'

const ShowVideo = ({src}: {src: string}) => {
  const t = useTranslations('aiImageToVideo')
  const [isPlaying, setIsPlaying] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [isMuted, setIsMuted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)

  // 自动播放功能
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const autoPlay = async () => {
      try {
        // 设置静音以支持自动播放
        video.muted = true
        setIsMuted(true)

        await video.play()
        setIsPlaying(true)
        setShowControls(false)
        setIsLoading(false)
      } catch (error) {
        console.error('Auto play failed:', error)
        setIsLoading(false)
        // 如果自动播放失败，显示播放按钮
        setShowControls(true)
      }
    }

    // 当视频元数据加载完成后尝试自动播放
    const handleLoadedMetadata = () => {
      setDuration(video.duration)
      autoPlay()
    }

    // 备用方案：当视频可以播放时确保加载状态更新
    const handleCanPlay = () => {
      if (isLoading) {
        setIsLoading(false)
      }
    }

    // 如果视频已经可以播放，直接尝试自动播放
    if (video.readyState >= 2) {
      handleLoadedMetadata()
    } else {
      video.addEventListener('loadedmetadata', handleLoadedMetadata)
    }

    // 添加canplay事件监听作为备用
    video.addEventListener('canplay', handleCanPlay)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('canplay', handleCanPlay)
    }
  }, [isLoading])

  // 更新当前时间和进度
  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const updateTime = () => setCurrentTime(video.currentTime)

    video.addEventListener('timeupdate', updateTime)

    return () => {
      video.removeEventListener('timeupdate', updateTime)
    }
  }, [])

  const handlePlayClick = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
        setIsPlaying(false)
      } else {
        try {
          await videoRef.current.play()
          setIsPlaying(true)
          setTimeout(() => setShowControls(false), 1000)
        } catch (error) {
          console.error('Video play error:', error)
        }
      }
    }
  }

  const handleVideoClick = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
        setIsPlaying(false)
        setShowControls(true)
      } else {
        try {
          await videoRef.current.play()
          setIsPlaying(true)
          setShowControls(false)
        } catch (error) {
          console.error('Video play error:', error)
        }
      }
    }
  }

  const handleVideoEnded = () => {
    setIsPlaying(false)
    setShowControls(true)
  }

  const handleMouseEnter = () => {
    if (isPlaying) setShowControls(true)
  }

  const handleMouseLeave = () => {
    if (isPlaying) setShowControls(false)
  }

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted
      setIsMuted(!isMuted)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${String(seconds).padStart(2, '0')}`
  }

  return (
    <div
      className="w-full h-full overflow-hidden rounded-2xl shadow-2xl  flex items-center justify-center cursor-pointer"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="relative w-full h-full">
        {/* Video Element */}
        <video
          ref={videoRef}
          className="w-80 h-60 object-cover opacity-80 border-4 border-white/10 rounded-2xl"
          src={src}
          onClick={handleVideoClick}
          onEnded={handleVideoEnded}
          muted={isMuted}
          loop={true}
          preload="metadata"
          playsInline
          autoPlay
        />

        {/* Loading Overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mb-4 mx-auto"></div>
              <p className="text-white/80 text-sm">{t('loadingVideo')}</p>
            </div>
          </div>
        )}

        {showControls && !isLoading && (
          <div
            className="absolute inset-0 bg-black/30 flex items-center justify-center transition-all duration-300"
            onClick={handlePlayClick}
          >
            <div className="bg-white/20 backdrop-blur-sm rounded-full p-6 hover:bg-white/30 transition-all duration-300 hover:scale-110 cursor-pointer">
              {isPlaying ? (
                <Pause className="w-12 h-12 text-white" />
              ) : (
                <Play className="w-12 h-12 text-white ml-1" />
              )}
            </div>
          </div>
        )}

        {/* Video Controls Overlay */}
       

        {/* Video Status Indicator */}
        {isPlaying && !showControls && (
          <div className="absolute top-4 right-4">
            <div className="bg-green-500/20 backdrop-blur-sm rounded-full px-3 py-1">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                {/* <span className="text-green-400 text-xs font-medium">LIVE</span> */}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ShowVideo
