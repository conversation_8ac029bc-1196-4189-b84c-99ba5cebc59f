import React from 'react'
import { useTranslations } from 'next-intl'

const DemoSection = ({link = ''}) => {
  const t = useTranslations('aiImageToVideo')
  return (
    <section className="aii-demo-section py-32 relative overflow-x-clip">
      {/* 背景渐变与粒子动效 */}
      <div
        aria-hidden="true"
        className="pointer-events-none absolute inset-0 z-0"
      >
        <div className="w-full h-full bg-gradient-to-br from-pink-500/20 via-purple-500/10 to-blue-500/20 blur-3xl opacity-70 rounded-[3rem] animate-gradient-move" />
        {/* 粒子动效 */}
        <div className="absolute inset-0">
          <svg
            className="absolute top-10 left-1/4 w-32 h-32 opacity-30 animate-float-slow"
            viewBox="0 0 100 100"
          >
            <circle cx="50" cy="50" r="40" fill="#f472b6" />
          </svg>
          <svg
            className="absolute bottom-10 right-1/4 w-24 h-24 opacity-20 animate-float"
            viewBox="0 0 100 100"
          >
            <rect x="20" y="20" width="60" height="60" rx="20" fill="#a78bfa" />
          </svg>
        </div>
      </div>
      <div className="relative z-10 max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6 animate-text-shine">
            {t('demoTitle')}
          </h2>
          <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto animate-fade-in">
            {t('demoDescription')}
          </p>
        </div>
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-8">
          {/* 左侧：原始图片 */}
          <div className="flex-1 flex flex-col items-center">
            <div className="rounded-3xl overflow-hidden shadow-2xl border-4 border-white/10 bg-gray-900/60 p-4">
              <img
                src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop"
                alt="Before"
                className="w-72 h-56 object-cover"
              />
            </div>
            <div className="mt-4 text-white text-lg font-semibold">{t('staticLabel')}</div>
          </div>
          {/* 中间：AI动效/箭头 */}
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center shadow-lg animate-pulse">
              <svg
                width="40"
                height="40"
                viewBox="0 0 40 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <defs>
                  <linearGradient
                    id="ai-arrow"
                    x1="0"
                    y1="0"
                    x2="40"
                    y2="40"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#f472b6" />
                    <stop offset="1" stopColor="#a78bfa" />
                  </linearGradient>
                </defs>
                <path
                  d="M10 20h20M20 10l10 10-10 10"
                  stroke="url(#ai-arrow)"
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <div className="mt-2 text-pink-300 font-bold text-xl">
              {t('aiPhotoToVideo')}
            </div>
          </div>
          {/* 右侧：动画后图片/视频 */}
          <div className="flex-1 flex flex-col items-center">
            <div className="rounded-3xl overflow-hidden shadow-2xl border-4 border-pink-400/20 bg-gray-900/60 p-4 relative">
              <img
                src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop"
                alt="After"
                className="w-72 h-56 object-cover"
              />
              <div className="absolute bottom-2 right-2 bg-pink-500/80 text-white px-3 py-1 rounded-full text-xs font-bold shadow">
                {t('animatedLabel')}
              </div>
            </div>
            <div className="mt-4 text-white text-lg font-semibold">
              {t('animatedLabel')}
            </div>
          </div>
        </div>
        {/* 底部数据亮点 */}
        <div className="relative z-10 max-w-4xl mx-auto mt-16 grid grid-cols-1 sm:grid-cols-3 gap-6">
          <div className="bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl p-6 text-center shadow-lg">
            <div className="text-3xl font-bold text-white">300%</div>
            <div className="text-pink-100 mt-2">{t('interactiveEnhancement')}</div>
          </div>
          <div className="bg-gradient-to-r from-pink-600 to-purple-600 rounded-2xl p-6 text-center shadow-lg">
            <div className="text-3xl font-bold text-white">30s</div>
            <div className="text-pink-100 mt-2">{t('rapidGeneration')}</div>
          </div>
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-6 text-center shadow-lg">
            <div className="text-3xl font-bold text-white">100% Free!</div>
            <div className="text-pink-100 mt-2">{t('noWatermark')}</div>
          </div>
        </div>
      </div>
    </section>
  )
}
export default DemoSection
