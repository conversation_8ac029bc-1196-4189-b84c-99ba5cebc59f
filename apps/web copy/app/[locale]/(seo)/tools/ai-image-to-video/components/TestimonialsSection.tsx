import React from 'react'
import { Play, Star, Upload } from 'lucide-react'
import ShowVideo from './ShowVideo'
import AnimatedCard from './AnimateCard'
import { useTranslations } from 'next-intl'

const TestimonialsSection = () => {
  const t = useTranslations('aiImageToVideo')
  const testimonials = [
    {
      name: t('testimonial1Name'),
      role: t('testimonial1Role'),
      company: t('testimonial1Company'),
      rating: 5,
      text: t('testimonial1Text'),
      avatar:
        'https://static.remove.bg/uploader-examples/person/8_thumbnail.jpg',
    },
    {
      name: t('testimonial2Name'),
      role: t('testimonial2Role'),
      company: t('testimonial2Company'),
      rating: 5,
      text: t('testimonial2Text'),
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    },
    {
      name: t('testimonial3Name'),
      role: t('testimonial3Role'),
      company: t('testimonial3Company'),
      rating: 5,
      text: t('testimonial3Text'),
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    },
    {
      name: t('testimonial4Name'),
      role: t('testimonial4Role'),
      company: t('testimonial4Company'),
      rating: 5,
      text: t('testimonial4Text'),
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    },
  ]
  return (
    <div className="aii-testimonials-section py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('testimonialsTitle')}
          </h2>
          <p className="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto">
            {t('testimonialsDescription')}
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className={`${index === testimonials.length -1 ? 'hidden' : ''}  group p-6 bg-gradient-to-br from-purple-800/50 to-pink-800/50 rounded-2xl backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 hover:scale-105`}
            >
              <div className="flex items-center gap-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-4 h-4 text-yellow-400 fill-current"
                  />
                ))}
              </div>
              <p className="text-gray-300 leading-relaxed mb-6 text-sm italic">
                "{testimonial.text}"
              </p>
              <div className="border-t border-white/10 pt-4">
                <div className="flex items-center gap-3">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div>
                    <h4 className="text-white font-semibold text-sm">
                      {testimonial.name}
                    </h4>
                    <p className="text-gray-400 text-xs">{testimonial.role}</p>
                    <p className="text-gray-400 text-xs">
                      {testimonial.company}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
export default TestimonialsSection
