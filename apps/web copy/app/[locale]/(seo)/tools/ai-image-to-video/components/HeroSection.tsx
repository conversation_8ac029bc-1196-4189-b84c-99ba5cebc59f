import React from 'react'
import { Play, Upload } from 'lucide-react'
import ShowVideo from './ShowVideo'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import '../index.css'

const HeroSection = ({ link = '' }) => {
  const t = useTranslations('aiImageToVideo')
  return (
    <section className="min-h-[90vh] h-auto max-h-auto py-28 relative bg-gradient-to-br from-purple-900 via-purple-800 to-pink-800 overflow-x-clip">
      <div className="max-w-5xl mx-auto px-4 flex flex-col lg:flex-row items-center justify-center gap-4 lg:gap-6">
        {/* 左：图片 */}
        <div className="flex-1 animate-slide-in-left flex flex-col items-center max-w-[360px]">
          <div className="group relative rounded-3xl overflow-hidden shadow-2xl border-4 border-white/10 hover:border-white/20 bg-gray-900/60 p-2 transition-all duration-500">
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="rounded-2xl overflow-hidden border-2 border-white/10">
              <div className="w-full h-full overflow-hidden rounded-2xl shadow-2xl  flex items-center justify-center cursor-pointer">
                <div className="relative w-full h-full">
                  {/* Video Element */}
                  <img
                    className="w-80 h-60 object-cover opacity-80 border-4 border-white/10 rounded-2xl"
                    src="/images/ai-image-to-video/sea-before.webp"
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="mt-3 text-white/90 text-sm font-medium bg-black/40 backdrop-blur-sm px-6 py-1.5 rounded-full shadow-lg border border-white/10">
            {t('staticPhoto')}
          </div>
        </div>
        {/* 中：分割线/箭头 */}
        <div className="flex flex-col items-center gap-2 -mx-2">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-pink-400 to-purple-500 flex items-center justify-center shadow-lg hover:shadow-pink-500/30 transition-all duration-500">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center animate-pulse">
              <svg
                width="24"
                height="24"
                viewBox="0 0 40 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="transform"
              >
                <defs>
                  <linearGradient
                    id="hero-arrow"
                    x1="0"
                    y1="0"
                    x2="40"
                    y2="40"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#f472b6" />
                    <stop offset="1" stopColor="#a78bfa" />
                  </linearGradient>
                </defs>
                <path
                  d="M10 20h20M20 10l10 10-10 10"
                  stroke="url(#hero-arrow)"
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
          <div className="text-pink-300 font-bold text-base tracking-wider bg-pink-500/10 px-3 py-1 rounded-lg backdrop-blur-sm">
            {t('aiPhotoToVideo')}
          </div>
        </div>

        {/* 右：视频 */}
        <div className="flex-1 animate-slide-in-right flex flex-col items-center max-w-[360px]">
          <div className="group relative rounded-3xl overflow-hidden shadow-2xl border-4 border-pink-400/20 hover:border-pink-400/30 bg-gray-900/60 p-2 transition-all duration-500">
            <div className="absolute inset-0 bg-gradient-to-br from-pink-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="rounded-2xl overflow-hidden border-2 border-pink-400/20">
              <ShowVideo src="/videos/ai-image-to-video/sea-after.mp4" />
            </div>
          </div>
          <div className="mt-3 text-white/90 text-sm font-medium bg-pink-500/30 backdrop-blur-sm px-6 py-1.5 rounded-full shadow-lg border border-pink-400/30">
            {t('animatedVideo')}
          </div>
        </div>
      </div>

      <div className="relative z-10 max-w-2xl mx-auto mt-8 text-center">
        <h1 className="text-3xl animate-slide-in-bottom sm:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
          {t('heroTitle')}
        </h1>
        <p className="text-lg animate-slide-in-bottom sm:text-xl text-gray-200 mb-4">
          {t('heroDescription')}
        </p>
        <div className="flex animate-slide-in-bottom flex-col sm:flex-row gap-4 justify-center mt-4">
          <Link
            href={link}
            className="group bg-flow-light px-6 py-3 text-white font-semibold rounded-full text-lg relative overflow-hidden"
          >
            <span className="flex items-center justify-center gap-2 relative z-10">
              <Upload className="w-5 h-5 animate-bounce" />
              {t('startCreatingFree')}
            </span>
          </Link>
          <Link
            href={link}
            className="group px-6 py-3 border-2 border-white/30 text-white font-semibold rounded-full text-lg transition-all duration-300 hover:bg-white/10 hover:border-white/50 backdrop-blur-sm hover:scale-105 relative overflow-hidden"
          >
            <span className="flex items-center justify-center gap-2 relative z-10">
              <Play className="w-5 h-5 animate-pulse" />
              {t('seeExamples')}
            </span>
            <span className="absolute left-0 top-0 w-full h-full bg-gradient-to-r from-purple-400/10 via-white/10 to-pink-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-lg animate-shine"></span>
          </Link>
        </div>
      </div>
    </section>
  )
}
export default HeroSection
