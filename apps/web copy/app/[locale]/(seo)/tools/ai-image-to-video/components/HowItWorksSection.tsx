import React from 'react'
import AnimatedCard from './AnimateCard'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const HowItWorksSection = ({link = ''}) => {
  const t = useTranslations('aiImageToVideo')
  return (
    <section className="aii-how-section py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold leading-relaxed text-white mb-6">
            {t('howItWorksTitle')}
          </h2>
          <p className="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto">
            {t('howItWorksDescription')}
          </p>
        </div>

        <div className="space-y-10 max-w-2xl mx-auto">
          {/* Step 1 */}
          <AnimatedCard>
            <div className="bg-gradient-to-br from-purple-800/60 to-pink-800/60 rounded-2xl p-8 shadow-lg flex flex-col gap-4">
              <h3 className="text-xl font-bold text-white mb-2">
                {t('step1Title')}
              </h3>
              <p className="text-gray-200">
                {t('step1Description')}
              </p>
            </div>
          </AnimatedCard>

          {/* Step 2 */}
          <AnimatedCard>
            <div className="bg-gradient-to-br from-purple-800/60 to-pink-800/60 rounded-2xl p-8 shadow-lg flex flex-col gap-4">
              <h3 className="text-xl font-bold text-white mb-2">
                {t('step2Title')}
              </h3>
              <p className="text-gray-200">
                {t('step2Description')}
              </p>
            </div>
          </AnimatedCard>

          <div className="flex justify-center mt-8">
            <Link href={link} className="group px-8 py-4 bg-gradient-to-r from-pink-500 via-fuchsia-500 to-purple-500 text-white font-bold rounded-full text-lg transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-pink-500/40 relative overflow-hidden">
              {t('tryItFree')}
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
export default HowItWorksSection
