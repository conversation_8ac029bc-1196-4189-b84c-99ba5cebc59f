'use client'
import { ChevronDown, ChevronUp } from 'lucide-react'
import React, { useState } from 'react'
import { useTranslations } from 'next-intl'

const FAQSection = () => {
  const t = useTranslations('aiImageToVideo')
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null)

  const faqs = [
    {
      question: t('faq1Question'),
      answer: t('faq1Answer'),
    },
    {
      question: t('faq2Question'),
      answer: t('faq2Answer'),
    },
    {
      question: t('faq3Question'),
      answer: t('faq3Answer'),
    },
    {
      question: t('faq4Question'),
      answer: t('faq4Answer'),
    },
  ]
  return (
    <div className="aii-faq-section py-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            {t('faqTitle')}
          </h2>
          <p className="text-lg sm:text-xl text-gray-300 max-w-3xl mx-auto">
            {t('faqDescription')}
          </p>
        </div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="group bg-gradient-to-br from-purple-800/30 to-pink-800/30 rounded-2xl backdrop-blur-sm border border-white/10 hover:border-white/20 transition-all duration-300 overflow-hidden"
            >
              <button
                onClick={() =>
                  setExpandedFAQ(expandedFAQ === index ? null : index)
                }
                className="w-full p-6 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200"
                aria-expanded={expandedFAQ === index}
                aria-controls={`faq-panel-${index}`}
              >
                <h3 className="text-base sm:text-lg font-semibold text-white pr-4">
                  {faq.question}
                </h3>
                <div className="flex-shrink-0">
                  {expandedFAQ === index ? (
                    <ChevronUp className="w-5 h-5 text-purple-400" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-purple-400" />
                  )}
                </div>
              </button>
              <div
                id={`faq-panel-${index}`}
                className={`transition-all duration-500 ease-in-out ${
                  expandedFAQ === index
                    ? 'max-h-96 opacity-100 py-4'
                    : 'max-h-0 opacity-0 py-0'
                } overflow-hidden`}
                style={{
                  willChange: 'max-height, opacity',
                }}
              >
                <div className="px-6 pt-0">
                  <p className="text-gray-300 leading-relaxed text-sm">
                    {faq.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
export default FAQSection
