import React from 'react'
import { howToGuideData } from './data/howToGuideData'
import AnimatedCard from './AnimatedCard'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

const HowToGuideSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('photoToVideo')

  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">{t('howToTitle')}</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            {t('howToDescription')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-4xl mx-auto">
          {howToGuideData.map((step, idx) => (
            <AnimatedCard key={idx} delay={idx * 200}>
              <div className="flex flex-col items-center text-center">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
                  <span className="text-white font-bold text-2xl">
                    {idx + 1}
                  </span>
                </div>

                <div className="relative group mb-6">
                  <div className="absolute -inset-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur-xl opacity-20 group-hover:opacity-40 transition duration-500" />
                  <div className="relative overflow-hidden rounded-3xl">
                    <img
                      src={step.img}
                      alt={step.alt}
                      className="w-full aspect-video object-cover transform transition duration-500 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20" />
                  </div>
                </div>

                <h3 className="text-2xl font-semibold text-white mb-4">
                  {t(step.title)}
                </h3>
                <p className="text-white/80 text-base leading-relaxed">
                  {t(step.desc)}
                </p>
              </div>
            </AnimatedCard>
          ))}
        </div>

        <AnimatedCard delay={400} className="mt-12 text-center">
          <div className="relative group inline-block">
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl blur opacity-30 group-hover:opacity-60 transition duration-300" />
            <Link
              href={toolUrl}
              className="relative px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-xl shadow-lg hover:opacity-90 hover:scale-105 transition-all text-lg inline-block"
            >
              {t('howToButton')}
            </Link>
          </div>
        </AnimatedCard>
      </div>
    </section>
  )
}

export default HowToGuideSection
