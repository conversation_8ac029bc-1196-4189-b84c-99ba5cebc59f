import React from 'react'
import { ChevronDown } from 'lucide-react'
import { faqData } from './data/faqData'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

const FAQSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('photoToVideo')

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('faqTitle')}
        </h2>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqData.map((faq, index) => (
            <details
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-xl border border-slate-800 group"
              open={index === 0}
            >
              <summary className="w-full text-left p-6 flex justify-between items-center cursor-pointer list-none">
                <div className="flex items-start gap-4 flex-1">
                  <div className="flex-shrink-0">
                    <img
                      src={faq.user.avatar}
                      alt={t(faq.user.name)}
                      className="w-12 h-12 rounded-full object-cover border-2 border-slate-700"
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium text-purple-300 text-sm">
                        {t(faq.user.name)}
                      </span>
                      <span className="text-gray-500 text-sm">•</span>
                      <span className="text-gray-400 text-sm">
                        {t(faq.user.role)}
                      </span>
                    </div>
                    <span className="font-semibold text-white text-lg leading-tight">
                      {t(faq.question)}
                    </span>
                  </div>
                </div>

                <ChevronDown className="w-5 h-5 text-gray-400 group-open:rotate-180 group-open:text-purple-400" />
              </summary>

              <div className="px-6 pb-6 pt-2 text-gray-400">
                <div className="space-y-4">
                  {t(faq.answer)
                    .split('\n')
                    .map((paragraph, i) =>
                      paragraph.startsWith('-') ? (
                        <ul key={i} className="pl-5 space-y-2">
                          <li className="list-disc list-outside">
                            {paragraph.substring(1).trim()}
                          </li>
                        </ul>
                      ) : (
                        <p key={i} className="text-gray-300">
                          {paragraph}
                        </p>
                      )
                    )}
                </div>
              </div>
            </details>
          ))}
        </div>

        <div className="mt-12 text-center">
          <div className="space-y-4">
            <h3 className="text-2xl font-bold text-white">
              {t('faqBottomTitle')}
            </h3>
            <p className="text-white/80">{t('faqBottomDescription')}</p>
            <div className="relative group inline-block">
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl blur opacity-30 group-hover:opacity-60 transition duration-300" />
              <Link
                href={toolUrl}
                className="relative px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-xl shadow-lg hover:opacity-90 hover:scale-105 transition-all inline-block"
              >
                {t('faqBottomButton')}
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* <style jsx>{`
        details > summary::-webkit-details-marker {
          display: none;
        }
      `}</style> */}
    </section>
  )
}

export default FAQSection
