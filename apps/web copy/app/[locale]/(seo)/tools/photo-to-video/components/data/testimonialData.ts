export interface Testimonial {
  content: string
  author: string
  stars: number
}

export const testimonialData: Testimonial[] = [
  {
    content: "testimonial1Content",
    author: 'testimonial1Author',
    stars: 5,
  },
  {
    content: "testimonial2Content",
    author: 'testimonial2Author',
    stars: 5,
  },
  {
    content: "testimonial3Content",
    author: 'testimonial3Author',
    stars: 5,
  },
  {
    content: "testimonial4Content",
    author: 'testimonial4Author',
    stars: 5,
  },
]