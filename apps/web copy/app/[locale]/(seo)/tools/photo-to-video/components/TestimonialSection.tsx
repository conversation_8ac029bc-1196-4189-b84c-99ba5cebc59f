import React from 'react'
import { Star, Quote } from 'lucide-react'
import { testimonialData } from './data/testimonialData'
import { getTranslations } from 'next-intl/server'

const TestimonialSection = async ({ toolUrl }: { toolUrl: string }) => {
  const t = await getTranslations('photoToVideo')

  return (
    <section className="py-20 relative">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-white mb-24">
          {t('testimonialTitle')}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {testimonialData.map((testimonial, index) => (
            <div
              key={index}
              className="bg-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-800 transition-all duration-300 hover:border-purple-500 hover:shadow-2xl hover:shadow-purple-500/10 hover:-translate-y-2 flex flex-col"
              style={{
                animation: `fade-in-up 0.5s ${index * 0.1}s ease-out both`,
              }}
            >
              <Quote className="w-8 h-8 text-purple-400 mb-4" />
              <div className="flex mb-4">
                {[...Array(testimonial.stars)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-yellow-400 fill-yellow-400"
                  />
                ))}
              </div>
              <p className="text-gray-300 mb-6 italic text-lg flex-grow">
                {t(testimonial.content)}
              </p>
              <div className="mt-auto">
                <p className="font-semibold text-white text-right">
                  — {t(testimonial.author)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
