export interface FAQ {
  question: string
  answer: string
  user: {
    name: string
    avatar: string
    role: string
  }
}

export const faqData: FAQ[] = [
  {
    question: 'faq1Question',
    answer: `faq1Answer`,
    user: {
      name: 'faq1User',
      avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=200&h=200&fit=crop&crop=face&auto=format',
      role: 'faq1Role'
    }
  },
  {
    question: 'faq2Question',
    answer: `faq2Answer`,
    user: {
      name: 'faq2User',
      avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=200&h=200&fit=crop&crop=face&auto=format',
      role: 'faq2Role'
    }
  },
  {
    question: 'faq3Question',
    answer: `faq3Answer`,
    user: {
      name: 'faq3User',
      avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=200&h=200&fit=crop&crop=face&auto=format',
      role: 'faq3Role'
    }
  },
  {
    question: 'faq4Question',
    answer: `faq4Answer`,
    user: {
      name: 'faq4User',
      avatar: 'https://images.unsplash.com/photo-1542909168-82c3e7fdca5c?w=200&h=200&fit=crop&crop=face&auto=format',
      role: 'faq4Role'
    }
  },
]