import React from 'react'
import { useCaseData } from './data/useCaseData'
import AnimatedCard from './AnimatedCard'
import { Link } from '@i18n/routing'

const UseCaseSection = ({ toolUrl }: { toolUrl: string }) => {
  return (
    <section className="py-24 px-4 relative">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-6">
            <span className="text-pink-400">
              Creative Applications of Our Image to Video AI
            </span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto">
            See how our image to video AI brings ideas to life. From personal
            projects to social media, our free ai image to video tool unlocks
            new creative possibilities for everyone.
          </p>
        </div>

        {/* Use Cases */}
        <div className="space-y-20">
          {useCaseData.map((item, index) => (
            <AnimatedCard key={index} delay={index * 100}>
              <div
                className={`flex flex-col ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                } items-center gap-8 lg:gap-12`}
              >
                {/* Video Container */}
                <div className="w-full lg:w-1/2 relative">
                  <div className="relative group">
                    <div className="absolute -inset-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur-xl opacity-20 group-hover:opacity-40 transition duration-500" />
                    <div className="relative overflow-hidden rounded-3xl">
                      <video
                        autoPlay
                        loop
                        muted
                        playsInline
                        className="w-full aspect-video object-cover transform transition duration-500 group-hover:scale-105"
                      >
                        <source src={item.video} type="video/mp4" />
                        Your browser does not support the video tag.
                      </video>
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20" />

                      {/* Before Image Overlay */}
                      <div className="absolute bottom-4 left-4 w-20 h-20 rounded-lg overflow-hidden border-2 border-white/70 shadow-xl">
                        <img
                          src={item.beforeImg}
                          alt={`Before - ${item.alt}`}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                          <span className="text-white text-xs font-semibold">
                            Before
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Container */}
                <div className="w-full lg:w-1/2 space-y-6">
                  <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white leading-tight">
                    {item.title}
                  </h3>

                  <p className="text-white/80 leading-relaxed text-lg">
                    {item.desc}
                  </p>

                  {/* CTA Button */}
                  <div className="pt-4">
                    <div className="relative group inline-block">
                      <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl blur opacity-30 group-hover:opacity-60 transition duration-300" />
                      <Link
                        href={toolUrl}
                        className="relative px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-500 text-white font-semibold rounded-xl shadow-lg hover:opacity-90 hover:scale-105 transition-all inline-block"
                      >
                        {item.btn}
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedCard>
          ))}
        </div>
      </div>
    </section>
  )
}

export default UseCaseSection
