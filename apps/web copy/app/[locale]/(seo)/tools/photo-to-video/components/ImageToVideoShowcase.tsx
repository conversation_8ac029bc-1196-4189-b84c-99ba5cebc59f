import React from 'react'
import { getTranslations } from 'next-intl/server'

export default async function ImageToVideoShowcase() {
  const t = await getTranslations('photoToVideo')

  return (
    <div className="relative flex items-end gap-6 animate-fade-in-up">
      {/* Before 图片 */}
      <div className="relative max-md:w-48 max-md:h-60 w-64 h-80 shadow-2xl rounded-2xl overflow-hidden transform transition-all duration-700 hover:scale-105 hover:shadow-3xl animate-slide-in-left">
        <img
          src="https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/A1.png"
          alt={t('showcaseBefore')}
          className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
        />
        <span className="absolute top-2 left-2 bg-black/60 text-white text-xs px-3 py-1 rounded-full font-semibold animate-pulse">
          {t('showcaseBefore')}
        </span>
        {/* 添加光晕效果 */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
      </div>

      {/* After 视频 */}
      <div className="relative shadow-2xl max-md:w-48 max-md:h-60 w-64 h-80 rounded-2xl overflow-hidden transform transition-all duration-700 hover:scale-105 hover:shadow-3xl animate-slide-in-right">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
        >
          <source
            src="https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/A2.mp4"
            type="video/mp4"
          />
          {t('showcaseBrowserError')}
        </video>
        <span className="absolute top-2 left-2 bg-black/60 text-white text-xs px-3 py-1 rounded-full font-semibold animate-pulse">
          {t('showcaseAfter')}
        </span>
        {/* 添加光晕效果 */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-500"></div>
      </div>

      {/* 弯曲箭头 */}
      <svg
        className="absolute left-56 max-md:left-40 bottom-16 animate-bounce-slow"
        width="80"
        height="60"
        viewBox="0 0 80 60"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10 10C40 60 60 0 70 40"
          stroke="#fff"
          strokeWidth="4"
          strokeLinecap="round"
          className="animate-draw-path"
        />
        <path
          d="M70 40L62 36M70 40L68 32"
          stroke="#fff"
          strokeWidth="4"
          strokeLinecap="round"
          className="animate-draw-arrow"
        />
      </svg>

      {/* 添加全局样式 */}
    </div>
  )
}
