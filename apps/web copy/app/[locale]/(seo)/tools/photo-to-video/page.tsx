import React from 'react'
import { UploadCloud } from 'lucide-react'
import HowToGuideSection from './components/HowToGuideSection'
import UseCaseSection from './components/UseCaseSection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'
import WhyUsSection from './components/WhyUsSection'
import ImageToVideoShowcase from './components/ImageToVideoShowcase'
import type { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations('photoToVideo')
  return {
    title: t('pageTitle'),
    description: t('pageDescription'),
    keywords: t('pageKeywords'),
    openGraph: {
      title: t('pageTitle'),
      description: t('pageDescription'),
      url: 'https://imggen.org/tools/photo-to-video',
      type: 'website',
      images: [
        {
          url: 'https://www.imggen.org/og-image-photo-to-video.jpg',
          width: 1200,
          height: 630,
          alt: t('pageTitle'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('pageTitle'),
      description: t('pageDescription'),
      images: ['https://www.imggen.org/twitter-card-photo-to-video.jpg'],
    },
  }
}

const PhotoToVideoPage = async () => {
  const t = await getTranslations('photoToVideo')

  // JSON-LD Schema
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: t('pageTitle'),
    description: t('pageDescription'),
    applicationCategory: 'MultimediaApplication',
    operatingSystem: 'Web',
    url: 'https://www.imggen.org/tools/image-to-video',
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '1572',
    },
    creator: {
      '@type': 'Organization',
      name: 'ImgGen',
    },
  }

  // 统一配置跳转URL
  const PHOTO_TO_VIDEO_TOOL_URL = '/ai/image-to-video'

  return (
    <div className="relative h-full w-full bg-[#0f172a]">
      <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)]"></div>
      {/* Animated background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1),rgba(255,255,255,0)_50%)]"></div>
        <div
          className="absolute h-[200px] w-[400px] bg-purple-500 rounded-full blur-[100px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse"
          style={{ animationDuration: '6s' }}
        ></div>
        <div
          className="absolute h-[150px] w-[300px] bg-pink-500 rounded-full blur-[80px] top-1/4 left-1/4 animate-pulse"
          style={{ animationDuration: '8s', animationDelay: '2s' }}
        ></div>
        <div
          className="absolute h-[180px] w-[350px] bg-fuchsia-500 rounded-full blur-[90px] bottom-1/4 right-1/4 animate-pulse"
          style={{ animationDuration: '7s', animationDelay: '4s' }}
        ></div>
      </div>

      {/* Add JSON-LD Schema */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* Header Section */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Text Content - moved up */}
        <div className="flex-1 flex items-center pt-12">
          <div className="relative container mx-auto px-4 md:px-8 w-full">
            <div className="text-center">
              <div className="relative">
                <div
                  className="absolute -inset-4 bg-gradient-to-r from-purple-600 to-pink-600 rounded-3xl blur-3xl opacity-20 animate-pulse"
                  style={{ animationDuration: '3s' }}
                ></div>
                <h1 className="relative text-4xl md:text-6xl font-bold mb-6 text-white animate-fade-in-up">
                  {t('heroTitle')}
                </h1>
              </div>
              <p
                className="text-xl md:text-2xl text-white/80 mb-12 max-w-4xl mx-auto leading-relaxed animate-fade-in-up"
                style={{ animationDelay: '0.2s' }}
              >
                {t('heroDescription')}
              </p>
              <div
                className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-fade-in-up"
                style={{ animationDelay: '0.4s' }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl blur opacity-40 group-hover:opacity-70 transition duration-300"></div>
                  <a
                    href={PHOTO_TO_VIDEO_TOOL_URL}
                    className="relative px-8 py-4 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold rounded-xl shadow-lg hover:scale-105 transition-all duration-300 flex items-center space-x-2"
                  >
                    <UploadCloud className="w-5 h-5" />
                    <span>{t('heroButton')}</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Image to Video Showcase - positioned higher */}
        <div className="pb-20 pt-4">
          <div className="container mx-auto px-4 md:px-8">
            <div className="flex justify-center">
              <ImageToVideoShowcase />
            </div>
          </div>
        </div>
      </div>

      <WhyUsSection toolUrl={PHOTO_TO_VIDEO_TOOL_URL} />
      <UseCaseSection toolUrl={PHOTO_TO_VIDEO_TOOL_URL} />
      <HowToGuideSection toolUrl={PHOTO_TO_VIDEO_TOOL_URL} />
      <TestimonialSection toolUrl={PHOTO_TO_VIDEO_TOOL_URL} />
      <FAQSection toolUrl={PHOTO_TO_VIDEO_TOOL_URL} />
    </div>
  )
}

export default PhotoToVideoPage
