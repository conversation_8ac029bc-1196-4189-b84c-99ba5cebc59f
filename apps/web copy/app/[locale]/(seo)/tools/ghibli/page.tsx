import React from 'react'
import { <PERSON> } from '@i18n/routing'
import { UploadCloud, Wand2, Zap, Gift, Shield } from 'lucide-react'
import { getTranslations } from 'next-intl/server'
import FeatureSection from './components/FeatureSection'
import CaseStudySection from './components/CaseStudySection'
import HowToGuideSection from './components/HowToGuideSection'
import TestimonialSection from './components/TestimonialSection'
import FAQSection from './components/FAQSection'

export async function generateMetadata() {
  const t = await getTranslations('ghibli')
  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      url: 'https://imggen.ai/ghibli',
      images: [
        {
          url: 'https://imggen.ai/images/og-ghibli.jpg',
          width: 1200,
          height: 630,
          alt: t('title'),
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t('title'),
      description: t('description'),
      images: ['https://imggen.ai/images/twitter-ghibli.jpg'],
    },
  }
}

const GhibliPage = async () => {
  // 统一配置跳转URL
  const GHIBLI_TOOL_URL = '/ai/image-to-image'
  const t = await getTranslations('ghibli')

  const features = [
    {
      icon: Wand2,
      text: t('advancedGhibliAiTechnology'),
      color: 'text-purple-400',
    },
    {
      icon: Zap,
      text: t('instantImageTransformation'),
      color: 'text-yellow-400',
    },
    { icon: Gift, text: t('completelyFreeToUse'), color: 'text-green-400' },
    { icon: Shield, text: t('privacyProtected'), color: 'text-blue-400' },
  ]

  return (
    <div className="relative  h-full w-full bg-[#0f172a]">
      {/* <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)]"></div> */}
      {/* Background Effects - Slower animations */}
      <div className="absolute inset-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)] transition-opacity duration-1000" />
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1),rgba(255,255,255,0)_50%)]" />
        <div
          className="absolute h-[200px] w-[400px] bg-purple-500 rounded-full blur-[100px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse"
          style={{ animationDuration: '8s' }}
        />
        <div
          className="absolute h-[150px] w-[300px] bg-pink-500 rounded-full blur-[80px] top-1/4 left-1/4 animate-pulse"
          style={{ animationDuration: '10s', animationDelay: '2s' }}
        />
        <div
          className="absolute h-[180px] w-[350px] bg-fuchsia-500 rounded-full blur-[90px] bottom-1/4 right-1/4 animate-pulse"
          style={{ animationDuration: '12s', animationDelay: '4s' }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 pt-32  h-[100vh]">
        <div className="container mx-auto px-4  md:py-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-start">
            {/* Left Column */}
            <div
              className="space-y-6 animate-fade-in"
              style={{ animationDuration: '1.5s' }}
            >
              <div className="max-w-2xl">
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
                  <span className="text-fuchsia-400">
                    {t('createYourOwnStudio')}{' '}
                  </span>
                  <span className="text-emerald-400">{t('ghibli')}</span>
                  <span className="text-fuchsia-400">
                    {' '}
                    {t('imageInstantly')}
                  </span>
                </h1>
                <p className="mt-4 text-gray-400 text-lg">
                  {t('transformPhotosDescription')}
                </p>
              </div>

              {/* Upload Card */}
              <div className="transform transition-all duration-700 hover:scale-[1.02] bg-slate-900/20 backdrop-blur-sm border-2 border-dashed border-gray-700 rounded-xl p-6 hover:border-purple-500">
                <div className="flex flex-col items-center space-y-8">
                  <Link
                    href={GHIBLI_TOOL_URL}
                    className="group relative overflow-hidden rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all px-6 py-3 inline-flex items-center space-x-2 text-white"
                  >
                    <UploadCloud className="w-5 h-5" />
                    <span>{t('uploadYourPhoto')}</span>
                  </Link>
                  <p className="text-gray-400 text-sm">
                    {t('dragAndDropText')}
                  </p>
                </div>
              </div>

              {/* Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-12">
                {features.map((feature, index) => {
                  const Icon = feature.icon
                  return (
                    <div
                      key={index}
                      className="flex items-center space-x-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-500"
                      style={{ animationDelay: `${index * 200}ms` }}
                    >
                      <Icon className={`w-5 h-5 ${feature.color}`} />
                      <span className="text-gray-300 text-sm">
                        {feature.text}
                      </span>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Right Column - Preview */}
            <div
              className="relative mt-8 lg:mt-0 animate-fade-in"
              style={{ animationDuration: '2s', animationDelay: '0.5s' }}
            >
              <div className="aspect-square rounded-2xl overflow-hidden bg-gradient-to-br from-purple-500/10 to-fuchsia-500/10 p-1 transition-transform duration-700 hover:scale-[1.02]">
                <div className="h-full w-full rounded-xl bg-slate-900/50 backdrop-blur flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    {/* <div
                      className="text-4xl mb-2 animate-pulse"
                      style={{ animationDuration: '3s' }}
                    >
                      ✨
                    </div> */}
                    <img
                      src="/ghibli/ghibli-header.jpg"
                      alt={t('ghibliCreationAlt')}
                      className="max-w-full h-auto mb-4 rounded"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Sections */}
      <div className="space-y-20 mt-20">
        <div
          className="animate-fade-in"
          style={{ animationDuration: '1.5s', animationDelay: '0.5s' }}
        >
          <FeatureSection toolUrl={GHIBLI_TOOL_URL} />
        </div>
        <div
          className="animate-fade-in"
          style={{ animationDuration: '1.5s', animationDelay: '0.7s' }}
        >
          <CaseStudySection toolUrl={GHIBLI_TOOL_URL} />
        </div>
        <div
          className="animate-fade-in"
          style={{ animationDuration: '1.5s', animationDelay: '0.9s' }}
        >
          <HowToGuideSection toolUrl={GHIBLI_TOOL_URL} />
        </div>
        <div
          className="animate-fade-in"
          style={{ animationDuration: '1.5s', animationDelay: '1.1s' }}
        >
          <TestimonialSection toolUrl={GHIBLI_TOOL_URL} />
        </div>
        <div
          className="animate-fade-in"
          style={{ animationDuration: '1.5s', animationDelay: '1.3s' }}
        >
          <FAQSection toolUrl={GHIBLI_TOOL_URL} />
        </div>
      </div>
    </div>
  )
}

export default GhibliPage
