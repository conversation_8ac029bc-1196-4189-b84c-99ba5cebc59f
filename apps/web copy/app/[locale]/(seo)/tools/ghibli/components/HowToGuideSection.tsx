import React from 'react'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

interface HowToGuideSectionProps {
  toolUrl: string
}

const HowToGuideSection = async ({ toolUrl }: HowToGuideSectionProps) => {
  const t = await getTranslations('ghibli')
  return (
    <div className="relative bg-slate-950/50 backdrop-blur-sm py-16 md:py-24">
      <div className="container mx-auto px-4 md:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            {t('getYourGhibliMasterpiece')}
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto">
            {t('creatingYourOwnStudioGhibli')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Step 1 */}
          <div className="relative">
            <div className="bg-gradient-to-br from-slate-900/60 to-purple-900/20 backdrop-blur-sm rounded-2xl p-8 border border-slate-700/50 h-full">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-fuchsia-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                  1
                </div>
                <h3 className="text-2xl font-bold text-white">
                  {t('step1UploadPhotograph')}
                </h3>
              </div>
              <p className="text-gray-300 text-lg leading-relaxed mb-6">
                {t('selectClearWellLitPhoto')}
              </p>
              <div className="flex justify-center">
                <img
                  src="/ghibli/ghibli-7.jpg"
                  alt={t('uploadYourPhotoAlt')}
                  className="w-full max-w-sm rounded-lg shadow-lg border border-gray-600"
                />
              </div>
            </div>
          </div>

          {/* Step 2 */}
          <div className="relative">
            <div className="bg-gradient-to-br from-slate-900/60 to-purple-900/20 backdrop-blur-sm rounded-2xl p-8 border border-slate-700/50 h-full">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-fuchsia-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                  2
                </div>
                <h3 className="text-2xl font-bold text-white">
                  {t('step2GenerateAndDownload')}
                </h3>
              </div>
              <p className="text-gray-300 text-lg leading-relaxed mb-6">
                {t('clickGenerateButton')}
              </p>
              <div className="flex justify-center">
                <img
                  src="/ghibli/ghibli-8.jpg"
                  alt={t('generateAndDownloadAlt')}
                  className="w-full max-w-sm rounded-lg shadow-lg border border-gray-600"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-12">
          <Link
            href={toolUrl}
            className="bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all text-white font-semibold px-8 py-4 text-lg rounded-lg inline-block"
          >
            {t('startCreating')}
          </Link>
        </div>
      </div>
    </div>
  )
}

export default HowToGuideSection
