import React from 'react'
import { Link } from '@i18n/routing'
import { getTranslations } from 'next-intl/server'

interface FeatureSectionProps {
  toolUrl: string
}

const FeatureSection = async ({ toolUrl }: FeatureSectionProps) => {
  const t = await getTranslations('ghibli')
  return (
    <div className="relative bg-slate-950/50 backdrop-blur-sm">
      <div className="container mx-auto px-4 md:px-8 py-16 md:py-24">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            {t('whyUseOurGhibliAi')}
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto">
            {t('ghibliAiOffersUnmatched')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center mb-16">
          {/* Feature 1 */}
          <div className="space-y-6">
            <h3 className="text-2xl md:text-3xl font-bold text-fuchsia-400">
              {t('advancedGhibliAiForHighFidelity')}
            </h3>
            <p className="text-gray-300 text-lg leading-relaxed">
              {t('uniqueGhibliDiffusionModel')}
            </p>
          </div>
          <div className="flex justify-center">
            <img
              src="/ghibli/ghibli-1.jpg"
              alt={t('advancedGhibliAiTechnologyAlt')}
              className="w-full max-w-md rounded-2xl shadow-2xl border border-gray-700"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          {/* Feature 2 - Reverse order on desktop */}
          <div className="flex justify-center md:order-1">
            <img
              src="/ghibli/ghibli-2.jpg"
              alt={t('simpleAndFastGhibliAiAlt')}
              className="w-full max-w-md rounded-2xl shadow-2xl border border-gray-700"
            />
          </div>
          <div className="space-y-6 md:order-2">
            <h3 className="text-2xl md:text-3xl font-bold text-fuchsia-400">
              {t('simpleFastAndFree')}
            </h3>
            <p className="text-gray-300 text-lg leading-relaxed">
              {t('experienceTheMagic')}
            </p>
            <div className="pt-4">
              <Link
                href={toolUrl}
                className="bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all text-white font-semibold px-6 py-3 rounded-lg duration-300 inline-block"
              >
                {t('tryForFreeNow')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FeatureSection
