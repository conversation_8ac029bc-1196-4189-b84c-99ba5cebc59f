import { Toaster } from '@ui/components/toaster'
import { cn } from '@ui/lib'
import { Provider as <PERSON><PERSON>Provider } from 'jotai'
import type { Metadata } from 'next'
import { NextIntlClientProvider } from 'next-intl'
import { getLocale, getMessages } from 'next-intl/server'
import { Poppins } from 'next/font/google'
import NextTopLoader from 'nextjs-toploader'
//
//
// @ts-ignore
// import { Analytics } from '@vercel/analytics/react'
import { SpeedInsights } from '@vercel/speed-insights/next'

import { ApiClientProvider } from '@shared/components/ApiClientProvider'
import { GradientBackgroundWrapper } from '@shared/components/GradientBackgroundWrapper'
import { GlobalModalManager } from '@shared/components/GlobalModalManager'
import { ThemeProvider } from '@shared/components/ThemeProvider'
import './globals.css'
import 'cropperjs/dist/cropper.css'

import Script from 'next/script'
import { AnalyticsPro } from './components/Analytics'
import { GoogleOneTapProvider } from '@shared/components/GoogleOneTapProvider'
import { ThemeScript } from '@shared/components/ThemeScript'
const sansFont = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-sans',
})

export const metadata: Metadata = {
  metadataBase: new URL('https://www.imggen.org'),
  // 图标配置
  icons: {
    // 基本 favicon
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon-16x16.ico', sizes: '16x16', type: 'image/png' },
      // { url: '/favicon-32x32.ico', sizes: '32x32', type: 'image/png' },
      // { url: '/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
      // { url: '/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' },
    ],

    // 苹果设备图标
    // apple: [
    //   { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    // ],

    // // 其他平台图标
    // other: [
    //   {
    //     rel: 'mask-icon',
    //     url: '/safari-pinned-tab.svg',
    //     color: '#5bbad5',
    //   },
    // ],
  },
  alternates: {
    languages: {
      'x-default': '/',
      en: '/',
      de: '/de',
      es: '/es',
      fr: '/fr',
      ko: '/ko',
      pt: '/pt',
      ru: '/ru',
      th: '/th',
      vi: '/vi',
      'zh-Hans': '/zh-cn', // 改用 zh-Hans 表示简体中文
      'zh-Hant-HK': '/zh-hk', // 改用 zh-Hant-HK 表示香港繁体
      'zh-Hant': '/zh-tw', // 改用 zh-Hant 表示繁体中文
    },
    canonical: 'https://www.imggen.org',
  },
  // 添加更多元数据
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://www.imggen.org',
    title: 'Free AI Image Generator | Create GPT-4o Quality Images Instantly',
    description:
      'Generate unlimited free AI images with GPT-4o-like quality. Get 5 free credits daily for stunning visuals, artistic creations, and photorealistic images without watermarks or sign-up required.', // 强调免费特性和GPT-4o质量
    siteName: 'ImgGen.org',
    images: ['og-imggen.png'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free AI Image Generator | Create GPT-4o Quality Images Instantly',
    description:
      'Generate unlimited free AI images with GPT-4o-like quality. Get 5 free credits daily for stunning visuals, artistic creations, and photorealistic images without watermarks or sign-up required.', // 强调免费特性和GPT-4o质量
    images: ['og-imggen.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const locale = await getLocale()
  const messages = await getMessages()

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        {/* <AnalyticsPro /> */}
        <style>{`
          div[data-radix-popper-content-wrapper] {
            z-index: 9999 !important;
          }
        `}</style>

        {/* 主题脚本 - 在页面加载前执行以避免闪烁 */}
        <ThemeScript />

        <Script
          async
          src="https://www.googletagmanager.com/gtag/js?id=G-LM4VPZBJWK"
        />
        <Script id="google-analytics">
          {`
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-LM4VPZBJWK');
    `}
        </Script>
      </head>
      <body
        className={cn(
          'min-h-screen bg-[#0f172a] font-sans text-foreground antialiased',
          sansFont.variable
        )}
        style={{
          // @ts-ignore
          '--removed-body-scroll-bar-size': '0px !important',
          marginRight: '0px !important',
          paddingRight: '0px !important',
        }}
      >
        <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_140%_at_50%_10%,rgba(255,255,255,0)_20%,rgba(127,50,237,1)_100%)]"></div>
        {/* Animated background */}
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(124,58,237,0.1),rgba(255,255,255,0)_50%)]"></div>
          <div
            className="absolute h-[200px] w-[400px] bg-purple-500 rounded-full blur-[100px] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-pulse"
            style={{ animationDuration: '6s' }}
          ></div>
          <div
            className="absolute h-[150px] w-[300px] bg-pink-500 rounded-full blur-[80px] top-1/4 left-1/4 animate-pulse"
            style={{ animationDuration: '8s', animationDelay: '2s' }}
          ></div>
          <div
            className="absolute h-[180px] w-[350px] bg-fuchsia-500 rounded-full blur-[90px] bottom-1/4 right-1/4 animate-pulse"
            style={{ animationDuration: '7s', animationDelay: '4s' }}
          ></div>
        </div>
        <NextTopLoader color="var(--colors-primary)" />
        <NextIntlClientProvider locale={locale} messages={messages}>
          <JotaiProvider>
            <ThemeProvider>
              <GoogleOneTapProvider>
                <ApiClientProvider>
                  <GradientBackgroundWrapper>
                    {children}
                  </GradientBackgroundWrapper>
                  <GlobalModalManager />
                </ApiClientProvider>
              </GoogleOneTapProvider>
            </ThemeProvider>
            <Toaster />
          </JotaiProvider>
        </NextIntlClientProvider>
        {/* vercel speed */}
        <SpeedInsights />

        {/* vercel */}
        {/* <Analytics /> */}
      </body>
    </html>
  )
}
